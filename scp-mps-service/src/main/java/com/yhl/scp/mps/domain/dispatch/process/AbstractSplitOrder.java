package com.yhl.scp.mps.domain.dispatch.process;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.beust.jcommander.internal.Sets;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.basic.resource.enums.ResourceTypeEnum;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.dispatch.model.dto.SplitResourceDTO;
import com.yhl.scp.mps.domain.dispatch.model.dto.SplitRoutingStepResourceDTO;
import com.yhl.scp.mps.domain.dispatch.model.dto.SplitStepResourceResultDTO;
import com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.sds.basic.order.infrastructure.po.WorkOrderBasicPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>AbstractSplitOrder</code>
 *
 * <p>大单拆小单流程定义实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-27 22:37:28
 */
@Slf4j
public abstract class AbstractSplitOrder extends AbstractMpsResult {

    @Resource
    private ProductAdvanceBatchRuleService productAdvanceBatchRuleService;
    @Resource
    private MasterPlanExtDao masterPlanExtDao;
    @Resource
    private MoldChangeTimeService moldChangeTimeService;

    /**
     * 组合大单拆分，尝试插入小单
     *
     * @param routingYieldResourceMap
     * @param ruleEncodingsMap
     * @param combinationBigWorkOrders
     * @param combinationSmallWorkOrders
     * @return
     */
    private Map<WorkOrderPO, List<WorkOrderPO>> getSplitWorkOrders(
            Map<String, MoldChangeTimeVO> changeTimeVOMap,
            Map<String, NewProductStockPointVO> productMap,
            String physicalResourceId,
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            Map<String, RuleEncodingsVO> ruleEncodingsMap,
            List<List<WorkOrderPO>> combinationBigWorkOrders,
            List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        Map<WorkOrderPO, List<WorkOrderPO>> result = MapUtil.newHashMap();
        if (CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
            return result;
        }
        for (int i = 0; i < combinationBigWorkOrders.size(); i++) {
            List<WorkOrderPO> workOrderPOList = combinationBigWorkOrders.get(i);
            if (CollectionUtils.isEmpty(workOrderPOList)) {
                continue;
            }
            WorkOrderPO key = workOrderPOList.get(0);
            String productId = key.getProductId();
            NewProductStockPointVO pointVO = productMap.get(productId);
            String moldChangeTimeKey = StringUtils.joinWith("&&", pointVO.getStockPointCode(), pointVO.getProductCode());
            MoldChangeTimeVO moldChangeTimeVO = changeTimeVOMap.get(moldChangeTimeKey);
            Long outsideDieChangeTime = 0L;
            if (Objects.nonNull(moldChangeTimeVO)) {
                outsideDieChangeTime = Objects.nonNull(moldChangeTimeVO.getOutsideDieChangeTime()) ?
                        moldChangeTimeVO.getOutsideDieChangeTime() : 0L;
            }
            String matchCode = UUIDUtil.getUUID();
            List<WorkOrderPO> value = Lists.newArrayList();
            if (canSplitByAverage(outsideDieChangeTime, routingYieldResourceMap, workOrderPOList, combinationSmallWorkOrders)) {
                List<WorkOrderPO> popCombinationWorkOrders =
                        popCombinationWorkOrdersAverage(
                                matchCode,
                                outsideDieChangeTime,
                                ruleEncodingsMap,
                                routingYieldResourceMap,
                                workOrderPOList,
                                combinationSmallWorkOrders);
                if (CollectionUtils.isEmpty(popCombinationWorkOrders)) {
                    continue;
                }
                while (CollectionUtils.isNotEmpty(popCombinationWorkOrders)) {
                    value.addAll(popCombinationWorkOrders);
                    workOrderPOList.removeAll(popCombinationWorkOrders);
                    popCombinationWorkOrders =
                            popCombinationWorkOrdersAverage(
                                    matchCode,
                                    outsideDieChangeTime,
                                    ruleEncodingsMap,
                                    routingYieldResourceMap,
                                    workOrderPOList,
                                    combinationSmallWorkOrders);
                }
            } else {
                List<WorkOrderPO> popCombinationWorkOrders =
                        popCombinationWorkOrders(
                                matchCode,
                                outsideDieChangeTime,
                                ruleEncodingsMap,
                                routingYieldResourceMap,
                                workOrderPOList,
                                combinationSmallWorkOrders);
                if (CollectionUtils.isEmpty(popCombinationWorkOrders)) {
                    continue;
                }
                while (CollectionUtils.isNotEmpty(popCombinationWorkOrders)) {
                    value.addAll(popCombinationWorkOrders);
                    workOrderPOList.removeAll(popCombinationWorkOrders);
                    popCombinationWorkOrders =
                            popCombinationWorkOrders(
                                    matchCode,
                                    outsideDieChangeTime,
                                    ruleEncodingsMap,
                                    routingYieldResourceMap,
                                    workOrderPOList,
                                    combinationSmallWorkOrders);
                }
            }
            result.put(key, value);
            if (CollectionUtils.isNotEmpty(value)) {
                String countingUnitId = key.getOrderNo() + "&" + physicalResourceId;
                for (WorkOrderPO po : value) {
                    po.setCountingUnitId(countingUnitId);
                }
            }
        }
        return result;
    }

    /**
     * 直接按单倍换模量直接拆分
     *
     * @param ruleEncodingsMap
     * @param routingYieldResourceMap
     * @param workOrderPOList
     * @param combinationSmallWorkOrders
     * @return
     */
    private List<WorkOrderPO> popCombinationWorkOrdersAverage(String matchCode, Long outsideDieChangeTime,
                                                              Map<String, RuleEncodingsVO> ruleEncodingsMap,
                                                              Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
                                                              List<WorkOrderPO> workOrderPOList,
                                                              List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        List<WorkOrderPO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
            return workOrderPOList;
        }
        if (Objects.isNull(outsideDieChangeTime) || outsideDieChangeTime <= 0) {
            return workOrderPOList;
        }
        Duration totalDuration = Duration.ZERO;
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            BigDecimal quantity = workOrderPO.getQuantity();
            Duration duration =
                    getWorkOrderDuration(quantity, yield, splitResource.getUnitProductionTime());
            totalDuration = totalDuration.plus(duration);
            workOrderPO.setMatchCode(matchCode);
        }
        if (totalDuration.compareTo(Duration.ofMinutes(outsideDieChangeTime * 2)) < 0) {
            return workOrderPOList;
        }
        Duration singleDuration = Duration.ofMinutes(outsideDieChangeTime);
        for (WorkOrderPO orderPO : workOrderPOList) {
            if (singleDuration.compareTo(Duration.ZERO) <= 0) {
                break;
            }
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(orderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal unitProductionTime = splitResource.getUnitProductionTime();
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            BigDecimal quantity = orderPO.getQuantity();
            Duration duration = getWorkOrderDuration(quantity, yield, unitProductionTime);
            if (duration.compareTo(singleDuration) < 0) {
                result.add(orderPO);
                singleDuration = singleDuration.minus(duration);
            } else {
                BigDecimal gapQuantity = getDurationQuantity(singleDuration, unitProductionTime, yield);
                // 拆，缺口数量
                orderPO.setQuantity(orderPO.getQuantity().subtract(gapQuantity));
                WorkOrderPO newOrderPO = new WorkOrderPO();
                BeanUtil.copyProperties(orderPO, newOrderPO);
                newOrderPO.setQuantity(gapQuantity);
                newOrderPO.setId(UUIDUtil.getUUID());
                newOrderPO.setOrderNo(this.getWorkOrderCode(ruleEncodingsMap));
                newOrderPO.setRemark(orderPO.getRemark() + "；根据" + orderPO.getOrderNo() + "拆分");
                newOrderPO.setMatchCode(matchCode);
                result.add(newOrderPO);
                singleDuration = Duration.ZERO;
            }
        }
        if (CollectionUtils.isNotEmpty(combinationSmallWorkOrders)) {
            List<WorkOrderPO> smallWorkOrders = combinationSmallWorkOrders.remove(0);
            result.addAll(smallWorkOrders);
        }
        return result;
    }

    /**
     * 大单是否可以按单倍换模时间均分
     *
     * @param routingYieldResourceMap
     * @param workOrderPOList
     * @param combinationSmallWorkOrders
     * @return
     */
    private Boolean canSplitByAverage(Long outsideDieChangeTime,
                                      Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
                                      List<WorkOrderPO> workOrderPOList,
                                      List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        if (CollectionUtils.isEmpty(workOrderPOList)
                || CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
            return false;
        }
        if (Objects.isNull(outsideDieChangeTime) || outsideDieChangeTime <= 0) {
            return false;
        }
        Duration totalDuration = Duration.ZERO;
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            BigDecimal quantity = workOrderPO.getQuantity();
            Duration duration =
                    getWorkOrderDuration(quantity, yield, splitResource.getUnitProductionTime());
            totalDuration = totalDuration.plus(duration);
        }
        long totalSeconds = totalDuration.getSeconds();
        int period = (int) totalSeconds / (outsideDieChangeTime.intValue() * 60);
        return (period - 1) < combinationSmallWorkOrders.size();
    }

    /**
     * 获取大单和小单
     *
     * @param ruleEncodingsMap
     * @param routingYieldResourceMap
     * @param workOrderPOList
     * @param combinationSmallWorkOrders
     * @return
     */
    private List<WorkOrderPO> popCombinationWorkOrders(String matchCode, Long outsideDieChangeTime,
                                                       Map<String, RuleEncodingsVO> ruleEncodingsMap,
                                                       Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
                                                       List<WorkOrderPO> workOrderPOList,
                                                       List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        List<WorkOrderPO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
            return workOrderPOList;
        }
        if (Objects.isNull(outsideDieChangeTime) || outsideDieChangeTime <= 0) {
            return workOrderPOList;
        }
        Duration totalDuration = Duration.ZERO;
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            BigDecimal quantity = workOrderPO.getQuantity();
            Duration duration =
                    getWorkOrderDuration(quantity, yield, splitResource.getUnitProductionTime());
            totalDuration = totalDuration.plus(duration);
            workOrderPO.setMatchCode(matchCode);
        }
        if (totalDuration.compareTo(Duration.ofMinutes(outsideDieChangeTime * 2)) < 0) {
            return workOrderPOList;
        }
        Duration singleDuration = Duration.ofMinutes(outsideDieChangeTime);
        Duration calcDuration = Duration.ZERO;
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            // 制造订单的数量
            BigDecimal quantity = workOrderPO.getQuantity();
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderPO.getId());
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            SplitResourceDTO splitResource = splitStepResourceResultDTO.getSplitResource();
            if (Objects.isNull(splitResource)) {
                continue;
            }
            BigDecimal unitProductionTime = splitResource.getUnitProductionTime();
            BigDecimal yield = splitStepResourceResultDTO.getYield();
            Duration duration = getWorkOrderDuration(quantity, yield, unitProductionTime);
            if (calcDuration.plus(duration).compareTo(singleDuration) <= 0) {
                calcDuration = calcDuration.plus(duration);
                totalDuration = totalDuration.minus(duration);
                result.add(workOrderPO);
                if (calcDuration.compareTo(singleDuration) >= 0) {
                    if (CollectionUtils.isNotEmpty(combinationSmallWorkOrders)) {
                        List<WorkOrderPO> smallWorkOrders = combinationSmallWorkOrders.remove(0);
                        result.addAll(smallWorkOrders);
                    }
                    break;
                }
            } else {
                // 缺口数量
                Duration gapDuration = singleDuration.minus(calcDuration);
                BigDecimal gapQuantity = getDurationQuantity(gapDuration, unitProductionTime, yield);
                // 排除订单gap剩余数量
                Duration remainCurrentDuration = totalDuration.minus(gapDuration);
                // 排除订单剩余数量
                Duration remainDuration = totalDuration.minus(duration);
                if (duration.compareTo(singleDuration.plus(singleDuration)) < 0
                        && remainDuration.compareTo(singleDuration) >= 0
                        || remainCurrentDuration.compareTo(singleDuration) < 0) {
                    result.add(workOrderPO);
                } else {
                    // 拆，缺口数量
                    workOrderPO.setQuantity(workOrderPO.getQuantity().subtract(gapQuantity));
                    WorkOrderPO orderPO = new WorkOrderPO();
                    BeanUtil.copyProperties(workOrderPO, orderPO);
                    orderPO.setQuantity(gapQuantity);
                    orderPO.setId(UUIDUtil.getUUID());
                    orderPO.setOrderNo(this.getWorkOrderCode(ruleEncodingsMap));
                    orderPO.setRemark(orderPO.getRemark() + "；根据" + workOrderPO.getOrderNo() + "拆分");
                    orderPO.setMatchCode(matchCode);
                    result.add(orderPO);
                }
                if (CollectionUtils.isNotEmpty(combinationSmallWorkOrders)) {
                    List<WorkOrderPO> smallWorkOrders = combinationSmallWorkOrders.remove(0);
                    result.addAll(smallWorkOrders);
                }
                break;
            }
        }
        return result;
    }

    /**
     * 制造订单大单拆小单
     *
     * @param mpsAnalysisContext 应用上下文
     * @param mpsAlgorithmOutput MPS结果
     */
    @Override
    protected void splitWorkOrder(
            MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        Date date = new Date();
        List<WorkOrderPO> createWorkOrderList = mpsAnalysisContext.getCreateWorkOrderList();
        if (CollectionUtils.isEmpty(createWorkOrderList)) {
            return;
        }
        // 清除历史优先级
        clearWorkOrderPriority(mpsAnalysisContext);
        // 计算制造订单的最早开始时间
        fillWorkOrderEarliestStartTime(mpsAnalysisContext, createWorkOrderList);
        // 处理制造订单最新的latest_end_time
        resetWorkOrderLatestEndTime(mpsAnalysisContext, createWorkOrderList);
        // 制造订单排序
        List<WorkOrderPO> sortWorkOrders = getSortWorkOrders(mpsAnalysisContext, createWorkOrderList);
        Integer maxPriority =
                Optional.ofNullable(masterPlanExtDao.selectWorkOrderMaxPriority()).orElse(0);
        // 重新设置制造订单的排序信息
        AtomicInteger priority = new AtomicInteger(maxPriority);
        for (WorkOrderPO sortedWorkOrder : sortWorkOrders) {
            sortedWorkOrder.setPriority(priority.incrementAndGet());
            sortedWorkOrder.setRemark(
                    sortedWorkOrder.getRemark() + "；原优先级：" + sortedWorkOrder.getPriority());
        }
        createWorkOrderList.clear();
        createWorkOrderList.addAll(sortWorkOrders);
        // 清除制造订单的最早开始时间
        // clearWorkOrderEarliestStartTime(createWorkOrderList);
        List<String> routingIds =
                sortWorkOrders.stream()
                        .map(WorkOrderPO::getRoutingId)
                        .distinct()
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(routingIds)) {
            // 没有工艺路径
            return;
        }
        List<SplitRoutingStepResourceDTO> splitRoutingStepResources =
                masterPlanExtDao.getSplitRoutingStepResources(routingIds);
        if (CollectionUtils.isEmpty(splitRoutingStepResources)) {
            // 没有工艺路径对应资源信息
            return;
        }
        // 制造订单推荐资源 key=workOrderId, value=physicalResourceId
        Map<String, String> workOrderOnResourceMap = mpsAnalysisContext.getWorkOrderOnResourceMap();
        Map<String, SplitStepResourceResultDTO> routingYieldResourceMap = getRoutingYieldResourceMap(sortWorkOrders,
                splitRoutingStepResources, workOrderOnResourceMap);
        // 计算烘弯炉所需模具数量
        calcWorkOrderNeedMoldQuantity(mpsAnalysisContext, sortWorkOrders, routingYieldResourceMap);
        // 查询S1YZ主资源下所有物理资源
        List<String> mainYztResourceIds = Lists.newArrayList();
        List<CollectionValueVO> mainYztResources = ipsFeign.getByCollectionCode("S1YZ");
        if (CollectionUtils.isNotEmpty(mainYztResources)) {
            String collectionValue = mainYztResources.get(0).getCollectionValue();
            mainYztResourceIds.addAll(
                    Optional.ofNullable(operationTaskExtDao.selectMainYztResource(collectionValue))
                            .orElse(Lists.newArrayList()));
        }
        if (CollectionUtils.isEmpty(mainYztResourceIds)) {
            // 没有压制炉资源
            return;
        }
        Map<String, List<WorkOrderPO>> sortedYztWorkOrderMap = MapUtil.newHashMap();
        for (WorkOrderPO sortWorkOrder : sortWorkOrders) {
            String workOrderId = sortWorkOrder.getId();
            SplitStepResourceResultDTO splitStepResourceResultDTO =
                    routingYieldResourceMap.get(workOrderId);
            if (Objects.isNull(splitStepResourceResultDTO)) {
                continue;
            }
            String physicalResourceId = splitStepResourceResultDTO.getSplitResource().getId();
            if (!mainYztResourceIds.contains(physicalResourceId)) {
                continue;
            }
            List<WorkOrderPO> sortYztWorkOrders =
                    sortedYztWorkOrderMap.getOrDefault(physicalResourceId, Lists.newArrayList());
            sortYztWorkOrders.add(sortWorkOrder);
            sortedYztWorkOrderMap.put(physicalResourceId, sortYztWorkOrders);
        }
        if (MapUtil.isEmpty(sortedYztWorkOrderMap)) {
            // 不存在需要拆单的制造订单
            return;
        }
        Map<String, Map<String, MoldChangeTimeVO>> moldChangeTimeMap = getMoldChangeTimeMap();
        Map<String, PhysicalResourceVO> physicalResourceMap = getPhysicalResourceMap(mpsAnalysisContext);
        Map<String, RuleEncodingsVO> ruleEncodingsMap = mpsAnalysisContext.getRuleEncodingsMap();
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap =
                mpsAnalysisContext.getProductStockPointBaseMap();
        Map<WorkOrderPO, List<WorkOrderPO>> remainSmallWorkOrdersMap = MapUtil.newHashMap();
        // 获取插单结果
        Map<WorkOrderPO, List<WorkOrderPO>> splitWorkOrderMap =
                getCombinationSplitWorkOrders(
                        moldChangeTimeMap,
                        physicalResourceMap,
                        ruleEncodingsMap,
                        productMap,
                        productStockPointBaseMap,
                        sortedYztWorkOrderMap,
                        routingYieldResourceMap,
                        remainSmallWorkOrdersMap);
        // 将插单结果调整至原排序制造订单结果里面
        for (Map.Entry<WorkOrderPO, List<WorkOrderPO>> entry : splitWorkOrderMap.entrySet()) {
            WorkOrderPO key = entry.getKey();
            List<WorkOrderPO> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                sortWorkOrders.removeAll(value.stream().filter(x -> x != key).collect(Collectors.toList()));
            }
            int index = sortWorkOrders.indexOf(key);
            sortWorkOrders.remove(key);
            sortWorkOrders.addAll(index, value);
        }
        // 重新调整小单的顺序，将小单堆在后面
        if (MapUtil.isNotEmpty(remainSmallWorkOrdersMap)) {
            for (Map.Entry<WorkOrderPO, List<WorkOrderPO>> entry : remainSmallWorkOrdersMap.entrySet()) {
                if (!sortWorkOrders.contains(entry.getKey())) {
                    continue;
                }
                List<WorkOrderPO> smallOrders = entry.getValue();
                sortWorkOrders.removeAll(smallOrders);
                int index = sortWorkOrders.indexOf(entry.getKey());
                sortWorkOrders.addAll(index + 1, smallOrders);
            }
        }
        priority = new AtomicInteger(maxPriority);
        for (WorkOrderPO sortedWorkOrder : sortWorkOrders) {
            sortedWorkOrder.setPriority(priority.incrementAndGet());
        }
        log.info("拆批插单--拆单后制造订单是否完全包含拆弹前制造订单：{}", sortWorkOrders.containsAll(createWorkOrderList));
        log.info("拆批插单--拆单前制造订单数量：{}条", createWorkOrderList.size());
        createWorkOrderList.clear();
        createWorkOrderList.addAll(sortWorkOrders);
        log.info("拆批插单--拆单后制造订单数量：{}条", createWorkOrderList.size());
        mpsAnalysisContext
                .getAlgorithmStepLogDTOList()
                .add(
                        getStepLog(
                                "MPS拆单",
                                MPS_MODULE,
                                mpsAnalysisContext.getAlgorithmLog().getId(),
                                date,
                                new Date()));
    }

    /**
     * 计算工单所需模具数量
     *
     * @param mpsAnalysisContext
     * @param sortWorkOrders
     * @param routingYieldResourceMap
     */
    private void calcWorkOrderNeedMoldQuantity(MpsAnalysisContext mpsAnalysisContext,
                                               List<WorkOrderPO> sortWorkOrders,
                                               Map<String, SplitStepResourceResultDTO> routingYieldResourceMap) {
        try {
            AlgorithmLog algorithmLog = mpsAnalysisContext.getAlgorithmLog();
            String scenario = algorithmLog.getScenario();
            List<PhysicalResourceVO> physicalResourceVOS = mdsFeign.selectAllPhysicalResource(scenario);
            Map<String, PhysicalResourceVO> physicalResourceMap = CollectionUtils.isEmpty(physicalResourceVOS) ?
                    MapUtil.newHashMap() :
                    physicalResourceVOS.stream().collect(Collectors.toMap(PhysicalResourceVO::getId,
                            Function.identity(),
                            (v1, v2) -> v1));
            Map<String, List<WorkOrderPO>> sortedHwWorkOrderMap = MapUtil.newHashMap();
            for (WorkOrderPO sortWorkOrder : sortWorkOrders) {
                String workOrderId = sortWorkOrder.getId();
                SplitStepResourceResultDTO splitStepResourceResultDTO = routingYieldResourceMap.get(workOrderId);
                if (Objects.isNull(splitStepResourceResultDTO)) {
                    continue;
                }
                String physicalResourceId = splitStepResourceResultDTO.getSplitResource().getId();
                PhysicalResourceVO physicalResourceVO = physicalResourceMap.get(physicalResourceId);
                if (Objects.isNull(physicalResourceVO)) {
                    continue;
                }
                String physicalResourceCode = physicalResourceVO.getPhysicalResourceCode();
                String resourceType = physicalResourceVO.getResourceType();
                // 非烘弯多重能力炉子，直接跳过
                if ((!physicalResourceCode.toUpperCase().contains("HW")) || (!ResourceTypeEnum.MULTIPLE.getCode().equals(resourceType))) {
                    continue;
                }
                List<WorkOrderPO> sortHwWorkOrders = sortedHwWorkOrderMap.getOrDefault(physicalResourceId,
                        Lists.newArrayList());
                sortHwWorkOrders.add(sortWorkOrder);
                sortedHwWorkOrderMap.put(physicalResourceId, sortHwWorkOrders);
            }
            if (MapUtil.isEmpty(sortedHwWorkOrderMap)) {
                return;
            }
            for (Map.Entry<String, List<WorkOrderPO>> entry : sortedHwWorkOrderMap.entrySet()) {
                String physicalResourceId = entry.getKey();
                List<WorkOrderPO> workOrderPOList = entry.getValue();
                for (WorkOrderPO order : workOrderPOList) {
                    String countingUnitId = order.getOrderNo() + "&" + physicalResourceId;
                    order.setCountingUnitId(countingUnitId);
                }
                Map<String, List<WorkOrderPO>> currencyMap =
                        workOrderPOList.stream().filter(x -> StringUtils.isNotBlank(x.getCurrencyUnitId()))
                                .collect(Collectors.groupingBy(WorkOrderPO::getCurrencyUnitId));
                currencyMap.forEach((key, value) -> {
                    if (CollectionUtils.isEmpty(value)) {
                        return;
                    }
                    WorkOrderPO orderPO = value.get(0);
                    SplitStepResourceResultDTO resourceResultDTO = routingYieldResourceMap.get(orderPO.getId());
                    if (Objects.isNull(resourceResultDTO)) {
                        return;
                    }
                    // 获取单位生产时间
                    BigDecimal unitProductionTime =
                            resourceResultDTO.getSplitResource().getUnitProductionTime();
                    // 成品率
                    BigDecimal yield = resourceResultDTO.getYield();
                    if (Objects.isNull(unitProductionTime) || unitProductionTime.compareTo(BigDecimal.ZERO) <= 0) {
                        return;
                    }
                    Integer moldQuantity = getMoldQuantity(mpsAnalysisContext, value, unitProductionTime,
                            yield);
                    for (WorkOrderPO workOrderPO : value) {
                        workOrderPO.setCurrencyUnitId(String.valueOf(moldQuantity));
                    }
                });
            }
        } catch (Exception ex) {
            log.error("计算工单所需模具数量失败：{}", ex.getMessage());
            ex.printStackTrace();
        }
    }

    private Integer getMoldQuantity(MpsAnalysisContext mpsAnalysisContext, List<WorkOrderPO> workOrderPOList,
                                    BigDecimal unitProductionTime, BigDecimal yield) {
        int moldQuantity = 1;
        BigDecimal totalQuantity = workOrderPOList.stream()
                .map(WorkOrderPO::getQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            return moldQuantity;
        }
        // 所有订单的工序数量
        BigDecimal moldQuantityBigDecimal = totalQuantity.divide(yield, 0, RoundingMode.DOWN);
        // 3天的产能
        BigDecimal unitQuantity =
                BigDecimal.valueOf(3 * 24 * 60 * 60).divide(BigDecimal.valueOf(unitProductionTime.intValue()), 0,
                        RoundingMode.DOWN);
        moldQuantity = moldQuantityBigDecimal.divide(unitQuantity, 0, RoundingMode.UP).intValue();
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        for (WorkOrderPO orderPO : workOrderPOList) {
            NewProductStockPointVO pointVO = productMap.get(orderPO.getProductId());
            Integer moldQuantityLimit = pointVO.getMoldQuantityLimit();
            if (Objects.isNull(moldQuantityLimit)) {
                moldQuantityLimit = 1;
            }
            if (moldQuantityLimit < moldQuantity) {
                moldQuantity = moldQuantityLimit;
            }
        }
        return moldQuantity;
    }

    /**
     * 重置工单的结束时间
     *
     * @param mpsAnalysisContext
     * @param createWorkOrderList
     */
    private void resetWorkOrderLatestEndTime(
            MpsAnalysisContext mpsAnalysisContext, List<WorkOrderPO> createWorkOrderList) {
        if (CollectionUtils.isEmpty(createWorkOrderList)) {
            return;
        }
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        List<String> productCodeList = Lists.newArrayList();
        for (WorkOrderPO workOrderPO : createWorkOrderList) {
            NewProductStockPointVO pointVO = productMap.get(workOrderPO.getProductId());
            if (Objects.nonNull(pointVO)) {
                productCodeList.add(pointVO.getProductCode());
            }
        }
        int initDays = 3;
        try {
            List<CollectionValueVO> combineBatchLimits =
                    ipsFeign.getByCollectionCode("COMBINE_BATCH_LIMIT");
            if (CollectionUtils.isNotEmpty(combineBatchLimits)) {
                String collectionValue = combineBatchLimits.get(0).getCollectionValue();
                initDays = Integer.parseInt(collectionValue);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("获取COMBINE_BATCH_LIMIT失败：{}", ex.getMessage());
        }
        // 查询风险等级
        List<PartRiskLevelVO> partRiskLevelVOS =
                dfpFeign.selectMaterialRiskLeveByProductCodeList(null, productCodeList);
        Map<String, PartRiskLevelVO> partRiskLevelVOMap =
                CollectionUtils.isEmpty(partRiskLevelVOS)
                        ? MapUtil.newHashMap()
                        : partRiskLevelVOS.stream()
                        .collect(
                                Collectors.toMap(
                                        PartRiskLevelVO::getProductCode, Function.identity(), (k1, k2) -> k1));
        // 查询提前生产批次规则
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS =
                productAdvanceBatchRuleService.selectAll();
        Map<String, ProductAdvanceBatchRuleVO> riskLevelAdvanceBatchRuleVOMap =
                CollectionUtils.isEmpty(productAdvanceBatchRuleVOS)
                        ? MapUtil.newHashMap()
                        : productAdvanceBatchRuleVOS.stream()
                        .filter(p -> StrUtil.isNotEmpty(p.getRiskLevel()))
                        .collect(
                                Collectors.toMap(
                                        ProductAdvanceBatchRuleVO::getRiskLevel, v -> v, (k1, k2) -> k1));
        String low = "低";
        for (WorkOrderPO workOrderPO : createWorkOrderList) {
            NewProductStockPointVO pointVO = productMap.get(workOrderPO.getProductId());
            if (Objects.isNull(pointVO)) {
                continue;
            }
            PartRiskLevelVO partRiskLevelVO = partRiskLevelVOMap.get(pointVO.getProductCode());
            ProductAdvanceBatchRuleVO advanceBatchRuleVO =
                    Objects.isNull(partRiskLevelVO)
                            ? riskLevelAdvanceBatchRuleVOMap.get(low)
                            : riskLevelAdvanceBatchRuleVOMap.get(partRiskLevelVO.getMaterialRiskLevel());
            if (Objects.isNull(advanceBatchRuleVO)) {
                continue;
            }
            if (Objects.isNull(workOrderPO.getLatestEndTime())) {
                continue;
            }
            int maxLeadDays = advanceBatchRuleVO.getMaxProductionLeadDays().intValue();
            long durationDays =
                    DateUtil.between(workOrderPO.getDueDate(), workOrderPO.getLatestEndTime(), DateUnit.DAY);
            if (durationDays < initDays) {
                // 小于3天的订单，默认使用合批天数根据最早开始时间进行偏移
                String remark =
                        workOrderPO.getRemark()
                                + "；原latest_end_time："
                                + DateUtil.formatDateTime(workOrderPO.getLatestEndTime());
                Date latestEndTime =
                        DateUtil.offsetDay(
                                workOrderPO.getDueDate(),
                                maxLeadDays);
                workOrderPO.setLatestEndTime(latestEndTime);
                remark = remark + "；偏移后latest_end_time："
                        + DateUtil.formatDateTime(workOrderPO.getLatestEndTime());
                workOrderPO.setRemark(remark);
                continue;
            }
            if (durationDays > maxLeadDays) {
                // 大于最大合批天数的订单，默认使用合批天数根据交期进行偏移
                String remark =
                        workOrderPO.getRemark()
                                + "；原latest_end_time："
                                + DateUtil.formatDateTime(workOrderPO.getLatestEndTime());
                Date latestEndTime =
                        DateUtil.offsetDay(
                                workOrderPO.getDueDate(),
                                maxLeadDays);
                workOrderPO.setLatestEndTime(latestEndTime);
                remark = remark + "；偏移后latest_end_time："
                        + DateUtil.formatDateTime(workOrderPO.getLatestEndTime());
                workOrderPO.setRemark(remark);
            }
        }
    }

    /**
     * 将小单堆在大单后
     *
     * @param workOrderPOS
     * @param lastBigWorkOrder,
     * @param remainSmallWorkOrders
     */
    private Map<WorkOrderPO, List<WorkOrderPO>> getResetSmallOrderMap(
            List<WorkOrderPO> workOrderPOS,
            WorkOrderPO lastBigWorkOrder,
            List<List<WorkOrderPO>> remainSmallWorkOrders) {
        HashMap<WorkOrderPO, List<WorkOrderPO>> result = MapUtil.newHashMap();
        if (CollectionUtils.isEmpty(remainSmallWorkOrders)) {
            return result;
        }
        List<WorkOrderPO> remainOrders =
                remainSmallWorkOrders.stream().flatMap(List::stream).collect(Collectors.toList());
        List<WorkOrderPO> matchSmallOrders = Lists.newArrayList();
        int index = workOrderPOS.indexOf(lastBigWorkOrder);
        for (int i = 0; i < index; i++) {
            WorkOrderPO orderPO = workOrderPOS.get(i);
            if (remainOrders.contains(orderPO)) {
                matchSmallOrders.add(orderPO);
            }
        }
        if (CollectionUtils.isNotEmpty(matchSmallOrders)) {
            result.put(lastBigWorkOrder, matchSmallOrders);
        }
        return result;
    }

    private void clearWorkOrderPriority(MpsAnalysisContext mpsAnalysisContext) {
        List<WorkOrderPO> workOrderPOS = mpsAnalysisContext.getWorkOrderAll();
        if (CollectionUtils.isEmpty(workOrderPOS)) {
            return;
        }
        List<String> workOrderIds =
                workOrderPOS.stream().map(WorkOrderPO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return;
        }
        List<List<String>> partition = com.google.common.collect.Lists.partition(workOrderIds, 2000);
        try {
            for (List<String> orderIds : partition) {
                masterPlanExtDao.clearBatchWorkOrderPriority(orderIds);
            }
        } catch (Exception e) {
            log.error("清除制造订单优先级失败：{}", e.getMessage());
        }
        for (WorkOrderPO workOrderPO : workOrderPOS) {
            workOrderPO.setPriority(null);
        }
    }

    /**
     * 获取外换模时间
     *
     * @return
     */
    private Map<String, Map<String, MoldChangeTimeVO>> getMoldChangeTimeMap() {
        List<MoldChangeTimeVO> moldChangeTimeVOS = moldChangeTimeService.selectKeyOperationTime();
        if (CollectionUtils.isEmpty(moldChangeTimeVOS)) {
            return MapUtil.newHashMap();
        }
        return moldChangeTimeVOS.stream()
                .filter(x -> StringUtils.isNotBlank(x.getResourceCode()))
                .collect(Collectors.groupingBy(MoldChangeTimeVO::getResourceCode,
                        Collectors.toMap(
                                x -> StringUtils.joinWith("&&", x.getStockPointCode(), x.getProductCode()),
                                Function.identity(),
                                (v1, v2) -> v1)));
    }

    /**
     * 获取所有物理资源
     *
     * @param mpsAnalysisContext
     * @return
     */
    private Map<String, PhysicalResourceVO> getPhysicalResourceMap(MpsAnalysisContext mpsAnalysisContext) {
        String scenario = mpsAnalysisContext.getAlgorithmLog().getScenario();
        List<PhysicalResourceVO> physicalResources = mdsFeign.selectAllPhysicalResource(scenario);
        return physicalResources.stream().collect(Collectors.toMap(PhysicalResourceVO::getId, Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 获取插单结果
     *
     * @param sortedYztWorkOrderMap
     * @param routingYieldResourceMap
     * @return
     */
    private Map<WorkOrderPO, List<WorkOrderPO>> getCombinationSplitWorkOrders(
            Map<String, Map<String, MoldChangeTimeVO>> moldChangeTimeMap,
            Map<String, PhysicalResourceVO> physicalResourceMap,
            Map<String, RuleEncodingsVO> ruleEncodingsMap,
            Map<String, NewProductStockPointVO> productMap,
            Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap,
            Map<String, List<WorkOrderPO>> sortedYztWorkOrderMap,
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            Map<WorkOrderPO, List<WorkOrderPO>> remainSmallWorkOrdersMap) {
        Map<WorkOrderPO, List<WorkOrderPO>> result = MapUtil.newHashMap();
        for (Map.Entry<String, List<WorkOrderPO>> entry : sortedYztWorkOrderMap.entrySet()) {
            String physicalResourceId = entry.getKey();
            PhysicalResourceVO physicalResourceVO = physicalResourceMap.get(physicalResourceId);
            String physicalResourceCode = physicalResourceVO.getPhysicalResourceCode();
            Map<String, MoldChangeTimeVO> changeTimeVOMap = moldChangeTimeMap.getOrDefault(physicalResourceCode, MapUtil.newHashMap());
            log.info("拆批插单--处理资源：{}", physicalResourceId);
            List<WorkOrderPO> workOrderPOList = entry.getValue();
            if (CollectionUtils.isEmpty(workOrderPOList)) {
                continue;
            }
            for (WorkOrderPO order : workOrderPOList) {
                String countingUnitId = order.getOrderNo() + "&" + physicalResourceId;
                order.setCountingUnitId(countingUnitId);
            }
            // 组合大单
            List<List<WorkOrderPO>> combinationBigWorkOrders = Lists.newArrayList();
            // 组合小单
            List<List<WorkOrderPO>> combinationSmallWorkOrders = Lists.newArrayList();
            // 获取组合大单和小单
            getCombinationBigWorkOrders(
                    changeTimeVOMap,
                    routingYieldResourceMap,
                    productMap,
                    productStockPointBaseMap,
                    workOrderPOList,
                    combinationBigWorkOrders,
                    combinationSmallWorkOrders);
            if (CollectionUtils.isEmpty(combinationBigWorkOrders)
                    || CollectionUtils.isEmpty(combinationSmallWorkOrders)) {
                continue;
            }
            List<WorkOrderPO> lastWorkOrders =
                    combinationBigWorkOrders.get(combinationBigWorkOrders.size() - 1);
            WorkOrderPO lastBigWorkOrder = lastWorkOrders.get(lastWorkOrders.size() - 1);
            log.info("拆批插单--组合大单共有：{}组", combinationBigWorkOrders.size());
            for (List<WorkOrderPO> pos : combinationBigWorkOrders) {
                if (CollectionUtils.isEmpty(pos)) {
                    continue;
                }
                String workOrderIds =
                        pos.stream().map(WorkOrderPO::getOrderNo).collect(Collectors.joining("，"));
                log.info("拆批插单--资源：{}--组合大单有：{}", physicalResourceId, workOrderIds);
            }
            log.info("拆批插单--小单共有：{}组", combinationSmallWorkOrders.size());
            for (List<WorkOrderPO> pos : combinationSmallWorkOrders) {
                if (CollectionUtils.isEmpty(pos)) {
                    continue;
                }
                String smallWorkOrderIds =
                        pos.stream().map(WorkOrderPO::getOrderNo).collect(Collectors.joining("，"));
                log.info("拆批插单--资源：{}--小单有：{}", physicalResourceId, smallWorkOrderIds);
            }
            Map<WorkOrderPO, List<WorkOrderPO>> resourceWorkOrdersMap =
                    getSplitWorkOrders(
                            changeTimeVOMap,
                            productMap,
                            physicalResourceId,
                            routingYieldResourceMap,
                            ruleEncodingsMap,
                            combinationBigWorkOrders,
                            combinationSmallWorkOrders);
            result.putAll(resourceWorkOrdersMap);
            // 将剩余小单插在大单后面
            if (MapUtil.isNotEmpty(resourceWorkOrdersMap)
                    && CollectionUtils.isNotEmpty(combinationSmallWorkOrders)) {
                remainSmallWorkOrdersMap.putAll(
                        getResetSmallOrderMap(workOrderPOList, lastBigWorkOrder, combinationSmallWorkOrders));
            }
        }
        // 更新小单最早开始时间
        if (MapUtil.isNotEmpty(result)) {
            for (Map.Entry<WorkOrderPO, List<WorkOrderPO>> entry : result.entrySet()) {
                List<WorkOrderPO> value = entry.getValue();
                Date earliestStartTime = entry.getKey().getEarliestStartTime();
                if (CollectionUtils.isEmpty(value)) {
                    continue;
                }
                for (WorkOrderPO order : value) {
                    order.setEarliestStartTime(earliestStartTime);
                }
            }
        }
        return result;
    }

    /**
     * 获取组合大单
     *
     * @param workOrderPOList
     * @return
     */
    private void getCombinationBigWorkOrders(
            Map<String, MoldChangeTimeVO> changeTimeVOMap,
            Map<String, SplitStepResourceResultDTO> routingYieldResourceMap,
            Map<String, NewProductStockPointVO> productMap,
            Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap,
            List<WorkOrderPO> workOrderPOList,
            List<List<WorkOrderPO>> combinationBigWorkOrders,
            List<List<WorkOrderPO>> combinationSmallWorkOrders) {
        Map<Integer, List<WorkOrderPO>> combinationMap = MapUtil.newHashMap();
        Integer index = 0;
        String standardResourceId = "";
        for (WorkOrderPO workOrderPO : workOrderPOList) {
            String gzmj =
                    getGzmjByWorkOrder(productMap, productStockPointBaseMap, workOrderPO.getProductId());
            if (StringUtils.isNotBlank(standardResourceId) && standardResourceId.equals(gzmj)) {
                List<WorkOrderPO> combinationWorkOrders =
                        combinationMap.getOrDefault(index, Lists.newArrayList());
                combinationWorkOrders.add(workOrderPO);
                combinationMap.put(index, combinationWorkOrders);
            } else {
                index++;
                standardResourceId = gzmj;
                List<WorkOrderPO> combinationWorkOrders =
                        combinationMap.getOrDefault(index, Lists.newArrayList());
                combinationWorkOrders.add(workOrderPO);
                combinationMap.put(index, combinationWorkOrders);
            }
        }

        for (Map.Entry<Integer, List<WorkOrderPO>> entry : combinationMap.entrySet()) {
            List<WorkOrderPO> combinationWorkOrders = entry.getValue();
            if (CollectionUtils.isEmpty(combinationWorkOrders)) {
                continue;
            }
            Long outsideDieChangeTime = 0L;
            WorkOrderPO orderPO = combinationWorkOrders.get(0);
            String productId = orderPO.getProductId();
            NewProductStockPointVO pointVO = productMap.get(productId);
            String stockPointCode = pointVO.getStockPointCode();
            String productCode = pointVO.getProductCode();
            String key = StringUtils.joinWith("&&", stockPointCode, productCode);
            MoldChangeTimeVO moldChangeTimeVO = changeTimeVOMap.get(key);
            if (Objects.nonNull(moldChangeTimeVO)) {
                outsideDieChangeTime = Objects.nonNull(moldChangeTimeVO.getOutsideDieChangeTime()) ?
                        moldChangeTimeVO.getOutsideDieChangeTime() : 0L;
            }
            // 两倍换模时间
            Duration doubleDuration = Duration.ofMinutes(outsideDieChangeTime * 2);
            // 单倍换模时间
            Duration singleDuration = Duration.ofMinutes(outsideDieChangeTime);
            Duration totalDuration = Duration.ZERO;
            Iterator<WorkOrderPO> iterator = combinationWorkOrders.iterator();
            while (iterator.hasNext()) {
                WorkOrderPO workOrderPO = iterator.next();
                SplitStepResourceResultDTO resourceResultDTO =
                        routingYieldResourceMap.get(workOrderPO.getId());
                if (Objects.isNull(resourceResultDTO)) {
                    iterator.remove();
                    continue;
                }
                BigDecimal unitProductionTime =
                        resourceResultDTO.getSplitResource().getUnitProductionTime();
                // 成品率
                BigDecimal yield = resourceResultDTO.getYield();
                BigDecimal quantity = workOrderPO.getQuantity();
                Duration workOrderDuration = getWorkOrderDuration(quantity, yield, unitProductionTime);
                totalDuration = totalDuration.plus(workOrderDuration);
                workOrderPO.setRemark(workOrderPO.getRemark() + "；订单原数量：" + quantity.intValue());
            }
            // 大于等于2倍换模时间
            if (totalDuration.compareTo(doubleDuration) >= 0) {
                combinationBigWorkOrders.add(combinationWorkOrders);
            } else {
                if (totalDuration.compareTo(singleDuration) < 0) {
                    combinationSmallWorkOrders.add(combinationWorkOrders);
                }
            }
        }
    }

    /**
     * 获取工装模具
     *
     * @param productMap
     * @param productStockPointBaseMap
     * @param productId
     * @return
     */
    private String getGzmjByWorkOrder(
            Map<String, NewProductStockPointVO> productMap,
            Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap,
            String productId) {
        NewProductStockPointVO pointVO = productMap.get(productId);
        if (Objects.isNull(pointVO)) {
            return null;
        }
        // 产品类型
        String cplx = pointVO.getVehicleModelCode();
        String gzmj = cplx;
        MdsProductStockPointBaseVO pointBaseVO = productStockPointBaseMap.get(pointVO.getProductCode());
        if (Objects.nonNull(pointBaseVO)) {
            // 工装模具
            String workOrderStandardResourceId = pointBaseVO.getStandardResourceId();
            if (StringUtils.isNotBlank(workOrderStandardResourceId)
                    && !"/".equals(workOrderStandardResourceId)) {
                gzmj = workOrderStandardResourceId;
            }
        }
        return StringUtils.isBlank(gzmj) ? "" : gzmj;
    }

    /**
     * 单倍换模数量（向下取整）
     *
     * @param unitProductionTime
     * @return
     */
    private BigDecimal getSingleQuantity(BigDecimal unitProductionTime, BigDecimal yield) {
        BigDecimal singleTime = new BigDecimal(4 * 60 * 60);
        return yield.multiply(singleTime).divide(unitProductionTime, 0, RoundingMode.DOWN);
    }

    /**
     * 获取指定时长的订单数量
     *
     * @param duration
     * @param unitProductionTime
     * @param yield
     * @return
     */
    private BigDecimal getDurationQuantity(
            Duration duration, BigDecimal unitProductionTime, BigDecimal yield) {
        BigDecimal singleTime = new BigDecimal(duration.getSeconds());
        return yield.multiply(singleTime).divide(unitProductionTime, 0, RoundingMode.DOWN);
    }

    /**
     * 获取制造订单时长
     *
     * @param quantity
     * @param yield
     * @param unitProductionTime
     * @return
     */
    private Duration getWorkOrderDuration(
            BigDecimal quantity, BigDecimal yield, BigDecimal unitProductionTime) {
        if (Objects.isNull(quantity) || Objects.isNull(yield) || Objects.isNull(unitProductionTime)) {
            return Duration.ZERO;
        }
        BigDecimal operationQuantity = quantity.divide(yield, 0, RoundingMode.DOWN);
        return Duration.ofSeconds(operationQuantity.multiply(unitProductionTime).longValue());
    }

    /**
     * 获取累计成品率、优先级最高资源
     *
     * @param splitRoutingStepResources
     * @return
     */
    private Map<String, SplitStepResourceResultDTO> getRoutingYieldResourceMap(
            List<WorkOrderPO> sortWorkOrders,
            List<SplitRoutingStepResourceDTO> splitRoutingStepResources,
            Map<String, String> workOrderOnResourceMap) {
        Map<String, SplitStepResourceResultDTO> routingYieldResourceMap = MapUtil.newHashMap();
        if (CollectionUtils.isEmpty(sortWorkOrders)
                || CollectionUtils.isEmpty(splitRoutingStepResources)) {
            return routingYieldResourceMap;
        }
        Map<String, List<SplitRoutingStepResourceDTO>> routingMap =
                splitRoutingStepResources.stream()
                        .collect(Collectors.groupingBy(SplitRoutingStepResourceDTO::getRoutingId));
        for (WorkOrderPO sortWorkOrder : sortWorkOrders) {
            String routingId = sortWorkOrder.getRoutingId();
            String workOrderId = sortWorkOrder.getId();
            // mps推荐资源
            String recommendResourceId = workOrderOnResourceMap.get(workOrderId);
            List<SplitRoutingStepResourceDTO> stepResources = routingMap.get(routingId);
            if (CollectionUtils.isEmpty(stepResources)) {
                continue;
            }
            if (StringUtils.isBlank(recommendResourceId)) {
                sortWorkOrder.setRemark(sortWorkOrder.getRemark() + "；MPS推荐资源为空");
                continue;
            }
            List<SplitRoutingStepResourceDTO> sortedStepResources =
                    stepResources.stream()
                            .sorted(
                                    Comparator.comparing(SplitRoutingStepResourceDTO::getStepSequenceNo).reversed())
                            .collect(Collectors.toList());
            // 累计成品率
            BigDecimal accumulateYield = BigDecimal.ONE;
            SplitResourceDTO splitResourceDTO = null;
            for (SplitRoutingStepResourceDTO stepResource : sortedStepResources) {
                BigDecimal yield =
                        Objects.isNull(stepResource.getYield()) ? BigDecimal.ONE : stepResource.getYield();
                accumulateYield = accumulateYield.multiply(yield);
                if (!StandardStepEnum.FORMING_PROCESS
                        .getCode()
                        .equals(stepResource.getStandardStepType())) {
                    continue;
                }
                List<SplitResourceDTO> physicalResources = stepResource.getPhysicalResources();
                if (CollectionUtils.isEmpty(physicalResources)) {
                    continue;
                }
                Optional<SplitResourceDTO> firstResource =
                        physicalResources.stream()
                                .filter(x -> recommendResourceId.equals(x.getId()))
                                .min(
                                        Comparator.comparing(SplitResourceDTO::getPriority)
                                                .thenComparing(SplitResourceDTO::getId));
                if (firstResource.isPresent()) {
                    splitResourceDTO = firstResource.get();
                } else {
                    sortWorkOrder.setRemark(sortWorkOrder.getRemark() + "；MPS推荐资源不存在匹配候选资源");
                }
                break;
            }
            if (Objects.isNull(splitResourceDTO)) {
                continue;
            }
            SplitStepResourceResultDTO resourceResultDTO = new SplitStepResourceResultDTO();
            resourceResultDTO.setRoutingId(routingId);
            resourceResultDTO.setYield(accumulateYield);
            resourceResultDTO.setSplitResource(splitResourceDTO);
            routingYieldResourceMap.put(workOrderId, resourceResultDTO);
        }
        return routingYieldResourceMap;
    }

    /**
     * 计算制造订单的最早开始时间
     *
     * @param mpsAnalysisContext
     * @param createWorkOrderList
     */
    private void fillWorkOrderEarliestStartTime(
            MpsAnalysisContext mpsAnalysisContext, List<WorkOrderPO> createWorkOrderList) {
        if (CollectionUtils.isEmpty(createWorkOrderList)) {
            return;
        }
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        List<String> productCodeList = Lists.newArrayList();
        for (WorkOrderPO workOrderPO : createWorkOrderList) {
            NewProductStockPointVO pointVO = productMap.get(workOrderPO.getProductId());
            if (Objects.nonNull(pointVO)) {
                productCodeList.add(pointVO.getProductCode());
            }
        }
        // 查询风险等级
        List<PartRiskLevelVO> partRiskLevelVOS =
                dfpFeign.selectMaterialRiskLeveByProductCodeList(null, productCodeList);
        Map<String, PartRiskLevelVO> partRiskLevelVOMap =
                CollectionUtils.isEmpty(partRiskLevelVOS)
                        ? MapUtil.newHashMap()
                        : partRiskLevelVOS.stream()
                        .collect(
                                Collectors.toMap(
                                        PartRiskLevelVO::getProductCode, Function.identity(), (k1, k2) -> k1));
        // 查询提前生产批次规则
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS =
                productAdvanceBatchRuleService.selectAll();
        // 根据不同维度分组提前生产批次规则（物料编码，车型，风险等级）
        Map<String, ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StringUtils.isNotBlank(p.getProductCode()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductCode, Function.identity(), (k1, k2) -> k1));
        Map<String, ProductAdvanceBatchRuleVO> productTypeAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StringUtils.isNotBlank(p.getProductType()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductType, Function.identity(), (k1, k2) -> k1));
        Map<String, ProductAdvanceBatchRuleVO> riskLevelAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StringUtils.isNotBlank(p.getRiskLevel()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getRiskLevel, Function.identity(), (k1, k2) -> k1));

        String low = "低";
        for (WorkOrderPO workOrderPO : createWorkOrderList) {
            NewProductStockPointVO pointVO = productMap.get(workOrderPO.getProductId());
            if (Objects.isNull(pointVO)) {
                continue;
            }
            ProductAdvanceBatchRuleVO advanceBatchRuleVO = productAdvanceBatchRuleVOMap.get(pointVO.getProductCode());
            if (Objects.isNull(advanceBatchRuleVO)) {
                advanceBatchRuleVO = productTypeAdvanceBatchRuleVOMap.get(pointVO.getProductType());
            }
            if (Objects.isNull(advanceBatchRuleVO)) {
                PartRiskLevelVO partRiskLevelVO = partRiskLevelVOMap.get(pointVO.getProductCode());
                advanceBatchRuleVO = Objects.isNull(partRiskLevelVO)
                        ? riskLevelAdvanceBatchRuleVOMap.get(low)
                        : riskLevelAdvanceBatchRuleVOMap.get(partRiskLevelVO.getMaterialRiskLevel());
            }
            if (Objects.isNull(advanceBatchRuleVO)) {
                continue;
            }
            Date earliestBeginTime =
                    DateUtil.offsetDay(
                            workOrderPO.getDueDate(), -advanceBatchRuleVO.getMaxProductionLeadDays().intValue());
            workOrderPO.setEarliestStartTime(earliestBeginTime);
            String remark =
                    workOrderPO.getRemark()
                            + "；原最早开始时间："
                            + DateUtil.formatDateTime(workOrderPO.getEarliestStartTime());
            workOrderPO.setRemark(remark);
        }
    }

    /**
     * 根据交期、时间交集、风栅、工装大类、车型编码、物品编码排序
     *
     * @param mpsAnalysisContext
     * @param createWorkOrderList
     * @return
     */
    private List<WorkOrderPO> getSortWorkOrders(MpsAnalysisContext mpsAnalysisContext, List<WorkOrderPO> createWorkOrderList) {
        List<WorkOrderPO> sortedWorkOrders = com.google.common.collect.Lists.newArrayList(createWorkOrderList);
        // 根据制造订单的交期排序，再按编码排序
        sortedWorkOrders.sort(Comparator.comparing(WorkOrderPO::getDueDate).thenComparing(WorkOrderPO::getOrderNo));
        // 获取物品信息
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        // 获取物品风栅类型
        Map<String, String> productFixtureRelationMap = getProductFixtureRelationMap(sortedWorkOrders, productMap);
        // 获取物品基本信息
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap = mpsAnalysisContext.getProductStockPointBaseMap();
        // 分组后的workOrder，key=目标workOrder，value=符合条件workOrder包括key
        Map<WorkOrderPO, List<WorkOrderPO>> groupWorkOrderMap = MapUtil.newHashMap();
        Set<String> handledWorkOrderIds = Sets.newHashSet();
        for (int index = 0; index < sortedWorkOrders.size(); index++) {
            WorkOrderPO workOrder = sortedWorkOrders.get(index);
            if (handledWorkOrderIds.contains(workOrder.getId())) {
                continue;
            }
            insertWorkOrder(index, sortedWorkOrders, workOrder, groupWorkOrderMap, handledWorkOrderIds, productMap, productStockPointBaseMap, productFixtureRelationMap);
        }
        if (MapUtil.isEmpty(groupWorkOrderMap)) {
            return sortedWorkOrders;
        }
        for (Map.Entry<WorkOrderPO, List<WorkOrderPO>> entry : groupWorkOrderMap.entrySet()) {
            WorkOrderPO key = entry.getKey();
            List<WorkOrderPO> value = entry.getValue();
            int index = sortedWorkOrders.indexOf(key);
            sortedWorkOrders.removeAll(value);
            List<WorkOrderPO> mergedWorkOrders = mergeWorkOrders(value, productMap);
            sortedWorkOrders.addAll(index, mergedWorkOrders);
        }
        return sortedWorkOrders;
    }

    /**
     * 合并workOrder
     *
     * @param value
     * @param productMap
     * @return
     */
    private List<WorkOrderPO> mergeWorkOrders(List<WorkOrderPO> value, Map<String, NewProductStockPointVO> productMap) {
        List<WorkOrderPO> mergedWorkOrders = Lists.newArrayList();
        if (CollectionUtils.isEmpty(value)) {
            return mergedWorkOrders;
        }
        Map<String, WorkOrderPO> productCodeWorkOrderMap = MapUtil.newHashMap();
        for (WorkOrderPO orderPO : value) {
            String bomType = orderPO.getBomType();
            if (!"CHAIN_BOM".equals(bomType)) {
                // 非链式直接跳过
                mergedWorkOrders.add(orderPO);
                continue;
            }
            NewProductStockPointVO pointVO = productMap.get(orderPO.getProductId());
            String productCode = pointVO.getProductCode();
            if (!productCodeWorkOrderMap.containsKey(productCode)) {
                productCodeWorkOrderMap.put(productCode, orderPO);
                mergedWorkOrders.add(orderPO);
                continue;
            }
            WorkOrderPO prevWorkOrder = productCodeWorkOrderMap.get(productCode);
            // 合并WorkOrder、Demand、Supply、Fulfillment
            mergeWorkOrderDemandSupplyFulfillment(prevWorkOrder, orderPO);
        }
        return mergedWorkOrders;
    }

    /**
     * 合并workOrder的Demand、Supply、Fulfillment
     *
     * @param prevWorkOrder
     * @param orderPO
     */
    private void mergeWorkOrderDemandSupplyFulfillment(WorkOrderPO prevWorkOrder, WorkOrderPO orderPO) {
        log.info("制造订单合并，workOrder-{}合并了workOrder-{}", prevWorkOrder.getOrderNo(), orderPO.getOrderNo());
        Date prevEarliestStartTime = prevWorkOrder.getEarliestStartTime();
        Date prevDueDate = prevWorkOrder.getDueDate();
        Date prevLatestEndTime = prevWorkOrder.getLatestEndTime();
        Date earliestStartTime = orderPO.getEarliestStartTime();
        Date dueDate = orderPO.getDueDate();
        Date latestEndTime = orderPO.getLatestEndTime();
        // 获取最早开始时间、最早交期、最晚结束时间
        if (earliestStartTime.before(prevEarliestStartTime)) {
            prevWorkOrder.setEarliestStartTime(earliestStartTime);
        }
        if (dueDate.before(prevDueDate)) {
            prevWorkOrder.setDueDate(dueDate);
        }
        if (latestEndTime.after(prevLatestEndTime)) {
            prevWorkOrder.setLatestEndTime(latestEndTime);
        }
        // 数量累加
        BigDecimal prevQuantity = prevWorkOrder.getQuantity();
        BigDecimal quantity = orderPO.getQuantity();
        prevWorkOrder.setQuantity(prevQuantity.add(quantity));
        log.info("制造订单合并，原数量：{}，现数量：{}", prevQuantity.intValue(), prevWorkOrder.getQuantity().intValue());
    }

    /**
     * 获取目标workOrder匹配到的matchWOrkOrders
     *
     * @param index
     * @param createWorkOrderList
     * @param workOrder
     * @param groupWorkOrderMap
     * @param handledWorkOrderIds
     * @param productMap
     * @param productStockPointBaseMap
     * @param productFixtureRelationMap
     */
    private void insertWorkOrder(Integer index, List<WorkOrderPO> createWorkOrderList, WorkOrderPO workOrder, Map<WorkOrderPO, List<WorkOrderPO>> groupWorkOrderMap, Set<String> handledWorkOrderIds, Map<String, NewProductStockPointVO> productMap, Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap, Map<String, String> productFixtureRelationMap) {
        handledWorkOrderIds.add(workOrder.getId());
        NewProductStockPointVO pointVO = productMap.get(workOrder.getProductId());
        if (Objects.isNull(pointVO)) {
            return;
        }
        List<WorkOrderPO> matchWorkOrderList = Lists.newArrayList();
        // 查找匹配的制造订单并组装groupWorkOrderMap
        for (int i = index + 1; i < createWorkOrderList.size(); i++) {
            WorkOrderPO current = createWorkOrderList.get(i);
            // 时间不存在交集，跳过
            if (!isTimeOverlap(workOrder, current)) {
                continue;
            }
            NewProductStockPointVO currentPointVO = productMap.get(current.getProductId());
            if (Objects.isNull(currentPointVO)) {
                continue;
            }
            if (!workOrderIsMatch(workOrder, current, productMap, productStockPointBaseMap, productFixtureRelationMap)) {
                continue;
            }
            handledWorkOrderIds.add(current.getId());
            matchWorkOrderList.add(current);
        }
        if (CollectionUtils.isEmpty(matchWorkOrderList)) {
            return;
        }
        // 设置同工装模具的最早开始时间取堆的最早一个
        Date prevEarliestStartTime = workOrder.getEarliestStartTime();
        for (WorkOrderPO orderPO : matchWorkOrderList) {
            Date earliestStartTime = orderPO.getEarliestStartTime();
            if (Objects.nonNull(prevEarliestStartTime) && Objects.nonNull(earliestStartTime) && prevEarliestStartTime.before(earliestStartTime)) {
                orderPO.setEarliestStartTime(prevEarliestStartTime);
                orderPO.setLatestEndTime(workOrder.getLatestEndTime());
            }
            String currencyUnitId = workOrder.getCurrencyUnitId();
            if (StringUtils.isBlank(currencyUnitId)) {
                currencyUnitId = workOrder.getId();
            }
            workOrder.setCurrencyUnitId(currencyUnitId);
            orderPO.setCurrencyUnitId(currencyUnitId);
            handledWorkOrderIds.add(orderPO.getId());
        }
        // 将匹配的重新排序
        reSortMatchWorkOrders(workOrder, matchWorkOrderList, productMap);
        groupWorkOrderMap.put(workOrder, matchWorkOrderList);
    }

    /**
     * 重新排序匹配的工单
     *
     * @param workOrder
     * @param matchWorkOrderList
     * @param productMap
     */
    private void reSortMatchWorkOrders(WorkOrderPO workOrder, List<WorkOrderPO> matchWorkOrderList, Map<String, NewProductStockPointVO> productMap) {
        NewProductStockPointVO pointVO = productMap.get(workOrder.getProductId());
        String productCode = pointVO.getProductCode();
        List<WorkOrderPO> remainSameWorkOrders = Lists.newArrayList();
        List<WorkOrderPO> otherWorkOrders = Lists.newArrayList();
        for (WorkOrderPO current : matchWorkOrderList) {
            NewProductStockPointVO currentPointVO = productMap.get(current.getProductId());
            String currentProductCode = currentPointVO.getProductCode();
            if (productCode.equals(currentProductCode)) {
                remainSameWorkOrders.add(current);
                continue;
            }
            otherWorkOrders.add(current);
        }
        Map<String, List<WorkOrderPO>> grouped = otherWorkOrders.stream()
                .collect(Collectors.groupingBy(order -> productMap.get(order.getProductId()).getProductCode()));
        List<WorkOrderPO> result = grouped.values().stream()
                .sorted(Comparator.comparing(group -> group.stream().map(WorkOrderPO::getDueDate).min(Comparator.naturalOrder()).get()))
                .flatMap(group -> group.stream().sorted(Comparator.comparing(WorkOrderPO::getDueDate)))
                .collect(Collectors.toList());
        matchWorkOrderList.clear();
        matchWorkOrderList.add(workOrder);
        if (CollectionUtils.isNotEmpty(remainSameWorkOrders)) {
            remainSameWorkOrders.sort(Comparator.comparing(WorkOrderPO::getDueDate));
            matchWorkOrderList.addAll(remainSameWorkOrders);
        }
        matchWorkOrderList.addAll(result);
    }

    /**
     * 字段匹配
     *
     * @param preWorkOrder
     * @param nextWorkOrder
     * @return
     */
    private boolean workOrderIsMatch(WorkOrderPO preWorkOrder, WorkOrderPO nextWorkOrder, Map<String, NewProductStockPointVO> productMap, Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap, Map<String, String> productFixtureRelationMap) {
        // 目标制造订单的信息
        NewProductStockPointVO pointVO = productMap.get(preWorkOrder.getProductId());
        if (Objects.isNull(pointVO)) {
            return false;
        }
        String productCode = pointVO.getProductCode();
        String vehicleModelCode = pointVO.getVehicleModelCode();
        MdsProductStockPointBaseVO pointBaseVO = productStockPointBaseMap.get(productCode);
        String standardResourceId = Objects.nonNull(pointBaseVO) ? pointBaseVO.getStandardResourceId() : null;
        String fixtureRelationType = productFixtureRelationMap.get(productCode);
        // 匹配制造订单的信息
        NewProductStockPointVO currentPointVO = productMap.get(nextWorkOrder.getProductId());
        if (Objects.isNull(currentPointVO)) {
            return false;
        }
        String currentProductCode = currentPointVO.getProductCode();
        String currentVehicleModelCode = currentPointVO.getVehicleModelCode();
        MdsProductStockPointBaseVO currentPointBaseVO = productStockPointBaseMap.get(currentProductCode);
        String currentStandardResourceId = (currentPointBaseVO != null) ? currentPointBaseVO.getStandardResourceId() : null;
        String currentFixtureRelationType = productFixtureRelationMap.get(currentProductCode);
        // 优先匹配工装模具
        if (StringUtils.isNotBlank(standardResourceId) || StringUtils.isNotBlank(currentStandardResourceId)) {
            return StringUtils.isNotBlank(standardResourceId)
                    && StringUtils.isNotBlank(currentStandardResourceId)
                    && standardResourceId.equals(currentStandardResourceId);
        }
        // 其次匹配风栅类型
        if (StringUtils.isNotBlank(fixtureRelationType) || StringUtils.isNotBlank(currentFixtureRelationType)) {
            return StringUtils.isNotBlank(fixtureRelationType)
                    && StringUtils.isNotBlank(currentFixtureRelationType)
                    && fixtureRelationType.equals(currentFixtureRelationType);
        }
        // 然后匹配车型编码
        if (StringUtils.isNotBlank(vehicleModelCode) || StringUtils.isNotBlank(currentVehicleModelCode)) {
            return StringUtils.isNotBlank(vehicleModelCode)
                    && StringUtils.isNotBlank(currentVehicleModelCode)
                    && vehicleModelCode.equals(currentVehicleModelCode);
        }
        // 最后匹配物品编码
        return StringUtils.isNotBlank(productCode)
                && StringUtils.isNotBlank(currentProductCode)
                && productCode.equals(currentProductCode);
    }

    /**
     * 制造订单排序
     *
     * @param mpsAnalysisContext
     * @param createWorkOrderList
     */
    private List<WorkOrderPO> getSortWorkOrdersBak(MpsAnalysisContext mpsAnalysisContext, List<WorkOrderPO> createWorkOrderList) {
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        // 风栅类型
        Map<String, String> productFixtureRelationMap = getProductFixtureRelationMap(createWorkOrderList, productMap);
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap = mpsAnalysisContext.getProductStockPointBaseMap();
        // 1、根据制造订单的交期排序
        CollectionUtil.sort(createWorkOrderList, Comparator.comparing(WorkOrderBasicPO::getDueDate));
        Map<String, WorkOrderPO> workOrderMap = createWorkOrderList.stream().collect(Collectors.toMap(WorkOrderBasicPO::getId, Function.identity(), (v1, v2) -> v1));
        List<String> sortWorkOrderIds = createWorkOrderList.stream().map(WorkOrderPO::getId).collect(Collectors.toList());
        List<WorkOrderPO> sortedWorkOrders = Lists.newArrayList();
        // 2、根据工装模具、产品类型排序
        for (String workOrderId : sortWorkOrderIds) {
            WorkOrderPO workOrder = workOrderMap.get(workOrderId);
            NewProductStockPointVO pointVO = productMap.get(workOrder.getProductId());
            if (Objects.isNull(pointVO)) {
                sortedWorkOrders.add(workOrder);
                continue;
            }
            // 产品类型
            String cplx = pointVO.getVehicleModelCode();
            String gzmj = cplx;
            MdsProductStockPointBaseVO pointBaseVO = productStockPointBaseMap.get(pointVO.getProductCode());
            if (Objects.nonNull(pointBaseVO)) {
                // 工装模具
                String standardResourceId = pointBaseVO.getStandardResourceId();
                if (StringUtils.isNotBlank(standardResourceId) && !"/".equals(standardResourceId)) {
                    gzmj = standardResourceId;
                }
            }
            if (StringUtils.isBlank(gzmj)) {
                sortedWorkOrders.add(workOrder);
                continue;
            }
            // 寻找合适地插入位置
            Integer insertIndex = findInsertIndex(mpsAnalysisContext, workOrder, gzmj, cplx, sortedWorkOrders);
            if (insertIndex == -1) {
                sortedWorkOrders.add(workOrder);
            } else {
                // 设置同工装模具的最早开始时间取堆的最早一个
                WorkOrderPO prevWorkOrder = sortedWorkOrders.get(insertIndex);
                if (Objects.nonNull(prevWorkOrder)) {
                    Date prevEarliestStartTime = prevWorkOrder.getEarliestStartTime();
                    Date earliestStartTime = workOrder.getEarliestStartTime();
                    if (Objects.nonNull(prevEarliestStartTime) && Objects.nonNull(earliestStartTime) && prevEarliestStartTime.before(earliestStartTime)) {
                        workOrder.setEarliestStartTime(prevEarliestStartTime);
                        workOrder.setLatestEndTime(prevWorkOrder.getLatestEndTime());
                    }
                    String currencyUnitId = prevWorkOrder.getCurrencyUnitId();
                    if (StringUtils.isBlank(currencyUnitId)) {
                        currencyUnitId = prevWorkOrder.getId();
                    }
                    prevWorkOrder.setCurrencyUnitId(currencyUnitId);
                    workOrder.setCurrencyUnitId(currencyUnitId);
                }
                sortedWorkOrders.add(insertIndex + 1, workOrder);
            }
        }
        return sortedWorkOrders;
    }

    /**
     * 根据物品查询风栅信息
     *
     * @param createWorkOrderList
     * @param productMap
     * @return
     */
    private Map<String, String> getProductFixtureRelationMap(List<WorkOrderPO> createWorkOrderList, Map<String, NewProductStockPointVO> productMap) {
        List<String> productCodes = createWorkOrderList.stream().map(x -> {
            NewProductStockPointVO pointVO = productMap.get(x.getProductId());
            if (Objects.nonNull(pointVO)) {
                return pointVO.getProductCode();
            }
            return null;
        }).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodes)) {
            return MapUtil.newHashMap();
        }
        List<ProductFixtureRelationPO> fixtureRelations = masterPlanExtDao.selectWindFenceProductFixtureRelationsByProductCodes(productCodes);
        return CollectionUtils.isEmpty(fixtureRelations) ? MapUtil.newHashMap() : fixtureRelations.stream().collect(Collectors.toMap(x -> StringUtils.joinWith("_", x.getStockPointCode(), x.getProductCode()), ProductFixtureRelationPO::getPhysicalResourceId, (v1, v2) -> v1));
    }

    /**
     * 从sortedWorkOrders中寻找合适的插入位置
     *
     * @param workOrder
     * @param gzmj
     * @param cplx
     * @param sortedWorkOrders
     * @return
     */
    private Integer findInsertIndex(
            MpsAnalysisContext mpsAnalysisContext,
            WorkOrderPO workOrder,
            String gzmj,
            String cplx,
            List<WorkOrderPO> sortedWorkOrders) {
        Map<String, NewProductStockPointVO> productMap = mpsAnalysisContext.getProductMap();
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap =
                mpsAnalysisContext.getProductStockPointBaseMap();
        int index = -1;
        // 是否存在同产品类型
        Boolean existSameCplx = false;
        for (int i = 0; i < sortedWorkOrders.size(); i++) {
            WorkOrderPO current = sortedWorkOrders.get(i);
            // 时间不存在交集
            if (!isTimeOverlap(workOrder, current)) {
                continue;
            }
            NewProductStockPointVO pointVO = productMap.get(current.getProductId());
            String currentCplx = Objects.isNull(pointVO) ? null : pointVO.getVehicleModelCode();
            String currentGzmj = currentCplx;
            if (Objects.nonNull(pointVO)) {
                MdsProductStockPointBaseVO pointBaseVO =
                        productStockPointBaseMap.get(pointVO.getProductCode());
                String standardResourceId =
                        Objects.isNull(pointBaseVO) ? null : pointBaseVO.getStandardResourceId();
                if (StringUtils.isNotBlank(standardResourceId) && !"/".equals(standardResourceId)) {
                    currentGzmj = standardResourceId;
                }
            }
            // 工装模具匹配
            if (StringUtils.isNotBlank(currentGzmj)
                    && StringUtils.isNotBlank(gzmj)
                    && currentGzmj.equals(gzmj)) {
                if (StringUtils.isNotBlank(currentCplx)
                        && StringUtils.isNotBlank(cplx)
                        && currentCplx.equals(cplx)) {
                    index = i;
                    existSameCplx = true;
                } else {
                    if (!existSameCplx) {
                        index = i;
                    }
                }
            }
        }
        return index;
    }

    /**
     * 判断时间是否存在交集
     *
     * @param wo1
     * @param wo2
     * @return
     */
    private boolean isTimeOverlap(WorkOrderPO wo1, WorkOrderPO wo2) {
        Date leftStartTime =
                Objects.isNull(wo1.getEarliestStartTime())
                        ? wo1.getLatestEndTime()
                        : wo1.getEarliestStartTime();
        Date rightStartTime =
                Objects.isNull(wo2.getEarliestStartTime())
                        ? wo2.getLatestEndTime()
                        : wo2.getEarliestStartTime();
        return leftStartTime.before(wo2.getLatestEndTime())
                && rightStartTime.before(wo1.getLatestEndTime());
    }
}
