package com.yhl.scp.mps.domain.dispatch.process;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.ips.utils.UserThreadLocal;
import com.yhl.scp.mds.basic.rule.enums.RuleEncodingsEnum;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepDO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.rule.util.RuleEncodingsUtils;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.algorithm.enums.DemandTypeEnum;
import com.yhl.scp.mps.algorithm.enums.FulfillmentStatusEnum;
import com.yhl.scp.mps.dispatch.mps.input.FinishedHalfProductMapping;
import com.yhl.scp.mps.dispatch.mps.input.ProductionPlannedMergeMapping;
import com.yhl.scp.mps.dispatch.output.*;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.sync.IWorkOrderSync;
import com.yhl.scp.mps.domain.sync.process.AbstractOperationSync;
import com.yhl.scp.mps.order.infrastructure.dao.WorkOrderDeletionDao;
import com.yhl.scp.mps.order.infrastructure.po.WorkOrderDeletionPO;
import com.yhl.scp.mps.plan.infrastructure.po.ProductionIntervalPO;
import com.yhl.scp.mps.productionLeadTime.enums.ProductionLeadTimeEnum;
import com.yhl.scp.mps.productionLeadTime.vo.ProductionLeadTimeVO;
import com.yhl.scp.sds.basic.enums.KitStatusEnum;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.pegging.enums.SupplyTypeEnum;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.DemandPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.FulfillmentPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.SupplyPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 结果解析
 */
@Slf4j
public abstract class AbstractMpsResult extends AbstractSchedule {
    @Resource
    private IWorkOrderSync workOrderSync;
    @Resource
    private IAmsSchedule amsSchedule;
    @Resource
    private WorkOrderDeletionDao workOrderDeletionDao;

    // 链式BOM
    private final static String CHAIN_BOM = "CHAIN_BOM";
    // 多级BOM
    private final static String TREE_BOM = "TREE_BOM";
    // 子BOM,作为某个成品的输入订单类型
    private final static String CHILD_BOM = "SEMI_BOM";
    private final static String INTERMEDIATE_DUMMY = "intermediateDummy";
    // 限制修改交期的制造订单状态
    private final static List<String> limitStatus = Arrays.asList(PlannedStatusEnum.STARTED.getCode(),
            PlannedStatusEnum.FINISHED.getCode());

    /**
     * 解析优化算法结果
     */
    @Override
    @BusinessMonitorLog(businessCode = "生产计划编制", moduleCode = "MPS", businessFrequency = "DAY")
    public void doAnalysisAlgorithmOutputData(RzzMpsAlgorithmOutput mpsAlgorithmOutput, AlgorithmLog algorithmLog) {
        UserThreadLocal.set(algorithmLog.getCreator());
        // 初始化MPS结果解析上下文
        MpsAnalysisContext mpsAnalysisContext = super.initResultContext(mpsAlgorithmOutput, algorithmLog);
        // 解析前处理删除未计划制造订单,及锁定期外不存在需求的制造订单
        preDeleteWorkOrder(mpsAnalysisContext, mpsAlgorithmOutput);
        // 解析结果
        analysisWorkOrder(mpsAnalysisContext, mpsAlgorithmOutput);
        // 取消计划
        cancelPlan(mpsAnalysisContext);
        // 制造订单大单拆小单
        splitWorkOrder(mpsAnalysisContext, mpsAlgorithmOutput);
        // MPS结果数据库操作
        databaseOperation(mpsAnalysisContext, mpsAlgorithmOutput);
        // 制造订单重新展开
        sync(mpsAlgorithmOutput, mpsAnalysisContext);
        // AMS调用
        amsSchedule.doAmsSchedule(algorithmLog, mpsAnalysisContext);
        // 重新排序制造订单的
        resetWorkOrderSequence(mpsAnalysisContext, mpsAlgorithmOutput);
        // 记录求解步骤日志
        ipsFeign.batchSaveStepLog(mpsAnalysisContext.getAlgorithmStepLogDTOList());
        // 清除线程上下文
        UserThreadLocal.clear();
    }

    /**
     * 制造订单大单拆小单
     *
     * @param mpsAnalysisContext 应用上下文
     * @param mpsAlgorithmOutput MPS结果
     */
    protected abstract void splitWorkOrder(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput);

    /**
     * 制造订单大单拆小单
     *
     * @param mpsAnalysisContext 应用上下文
     * @param mpsAlgorithmOutput MPS结果
     */
    protected abstract void resetWorkOrderSequence(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput);


    private void sync(RzzMpsAlgorithmOutput mpsAlgorithmOutput, MpsAnalysisContext mpsAnalysisContext) {
        Date dateTime = new Date();
        mpsAlgorithmOutput.setRoundToNearestFiveFlag(Boolean.TRUE);
        workOrderSync.doSyncOrder(mpsAlgorithmOutput);
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("制造订单展开完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime, new Date()));
        // 新展开的订单加入待排制造订单排程
        mpsAnalysisContext.getWorkOrderIds().addAll(mpsAlgorithmOutput.getWorkOrderIds());
    }

    private void supplementaryPackagingProcess(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        Date operationDate = new Date();
        List<WorkOrderPO> workOrderPOS = new ArrayList<>();
        List<RoutingVO> routingVOS = mpsAnalysisContext.getRoutingVOS();
        List<RoutingStepVO> routingStepVOS = mpsAnalysisContext.getRoutingStepVOS();
        List<ProductCandidateResourceTimeVO> productCandidateResourceVOS = newMdsFeign.selectProductCandidateResourceTimeByParams(null, new HashMap<>());
        List<ProductionLeadTimeVO> productionLeadTimeVOS = productionLeadTimeService.selectAll();

        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap = mpsAnalysisContext.getDeliveryPlanMap();
        Map<String, List<RoutingStepVO>> stepMap = StreamUtils.mapListByColumn(routingStepVOS, RoutingStepVO::getRoutingId);
        Map<String, RoutingVO> routingVOMap = StreamUtils.mapByColumn(routingVOS, RoutingVO::getProductCode);
        Map<String, List<ProductCandidateResourceTimeVO>> productCandidateResourceMap = productCandidateResourceVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));
        Map<String, List<ProductionLeadTimeVO>> productionLeadTimeMap = productionLeadTimeVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getOperationCode(), p.getLeadTimeType())));
        // 获取要补充的包装产品
        List<String> supplementaryPackagingCode = supplementaryPackagingProductCode(mpsAnalysisContext, deliveryPlanMap, stepMap, routingVOMap);
        if (CollectionUtils.isEmpty(supplementaryPackagingCode)) {
            log.info("没有发货计划需要补充制造订单");
            return;
        }
        List<SafetyStockLevelVO> safetyStockLevelVOS = dfpFeign.selectSafetyStockLevelByProductCodeList(null, supplementaryPackagingCode);
        Map<String, List<SafetyStockLevelVO>> safetyStockLecelMap = safetyStockLevelVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockCode(), p.getProductCode())));
        for (String supplyProductCode : supplementaryPackagingCode) {
            RoutingVO routingVO = routingVOMap.get(supplyProductCode);
            String stockPointCode = routingVO.getStockPointId();
            // 包装工序
            RoutingStepVO routingStepVO = stepMap.get(routingVO.getId()).get(0);
            Integer sequenceNo = routingStepVO.getSequenceNo();
            String mainKey = CharSequenceUtil.join("&", stockPointCode, supplyProductCode, sequenceNo);
            List<DeliveryPlanPublishedVO> packDeliveryList = deliveryPlanMap.get(supplyProductCode);
            List<List<DeliveryPlanPublishedVO>> partition = Lists.partition(packDeliveryList, 7);
            NewStockPointVO newStockPointVO = mpsAnalysisContext.getStockMap().get(stockPointCode);
            // 7天合成一批
            for (List<DeliveryPlanPublishedVO> deliverList : partition) {
                deliverList.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));
                int demandQuantity = deliverList.stream().mapToInt(DeliveryPlanPublishedVO::getDemandQuantity).sum();
                // 最早的发货计划计算提前期
                DeliveryPlanPublishedVO deliveryPlanPublishedVO = deliverList.get(0);
                Date dueDate = deliveryPlanPublishedVO.getDemandTime();
                String safetyKey = CharSequenceUtil.join("&", routingVO.getStockPointId(), supplyProductCode);
                // -标准安全库存天数
                if (safetyStockLecelMap.containsKey(safetyKey)) {
                    SafetyStockLevelVO safetyStockLevelVO = safetyStockLecelMap.get(safetyKey).get(0);
                    int minStockDay = safetyStockLevelVO.getStandardStockDay().intValue();
                    dueDate = DateUtil.offsetDay(dueDate, -minStockDay);
                }
                // -该物料的发货数量 X 包装工序的生产节拍
                if (productCandidateResourceMap.containsKey(mainKey)) {
                    ProductCandidateResourceTimeVO productCandidateResourceVO = productCandidateResourceMap.get(mainKey).get(0);
                    BigDecimal multiple = BigDecimalUtils.multiply(BigDecimalUtils.toBigDecimal(demandQuantity), BigDecimalUtils.toBigDecimal(productCandidateResourceVO.getBeat()));
                    dueDate = DateUtil.offsetSecond(dueDate, -multiple.intValue());
                }
                // -包装工序后处理时间
                String key = CharSequenceUtil.join("&", stockPointCode, sequenceNo, ProductionLeadTimeEnum.POST_PRODUCTION_PROCESSING_TIME.getCode());
                dueDate = calculateTime(key, productionLeadTimeMap, dueDate);
                // 组装workOrder
                String demandCategoryType = getDemandCategoryType(deliverList);
                int endingInventoryMinSafeDiff = getEndingInventoryMinSafeDiff(deliverList, mpsAnalysisContext);
                WorkOrderPO workOrderPOInsert = new WorkOrderPO();
                workOrderPOInsert.setId(UUIDUtil.getUUID());
                workOrderPOInsert.setOrderNo(getWorkOrderCode(mpsAnalysisContext.getRuleEncodingsMap()));
                workOrderPOInsert.setQuantity(BigDecimalUtils.toBigDecimal(demandQuantity));
                workOrderPOInsert.setDueDate(dueDate);
                workOrderPOInsert.setProductId(routingVO.getProductId());
                workOrderPOInsert.setProductStockPointId(routingVO.getProductId());
                workOrderPOInsert.setCreateTime(operationDate);
                workOrderPOInsert.setModifyTime(operationDate);
                workOrderPOInsert.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
                workOrderPOInsert.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
                workOrderPOInsert.setKitStatus(KitStatusEnum.UNKIT.getCode());
                workOrderPOInsert.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
                workOrderPOInsert.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                workOrderPOInsert.setCreateTime(operationDate);
                workOrderPOInsert.setModifyTime(operationDate);
                workOrderPOInsert.setEnabled(YesOrNoEnum.YES.getCode());
                workOrderPOInsert.setRoutingId(routingVO.getId());
                workOrderPOInsert.setRemark("supply workOrder");
                workOrderPOInsert.setBomType("packing");
                workOrderPOInsert.setDemandCategory(demandCategoryType);
                workOrderPOInsert.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                workOrderPOInsert.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                workOrderPOS.add(workOrderPOInsert);
            }
        }
        // 更新编码规则
        mdsFeign.selectiveUpdateRuleEncodings(mpsAnalysisContext.getRuleEncodingsMap().get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc()));
        log.info("补充包装制造订单数量：{}", workOrderPOS.size());
        if (CollectionUtils.isNotEmpty(workOrderPOS)) {
            List<String> insertWorkOrder = workOrderPOS.stream().map(WorkOrderPO::getId).collect(Collectors.toList());
            mpsAlgorithmOutput.getWorkOrderIds().addAll(insertWorkOrder);
            workOrderDao.insertBatch(workOrderPOS);
        }
    }

    private List<String> supplementaryPackagingProductCode(MpsAnalysisContext mpsAnalysisContext,
                                                           Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap,
                                                           Map<String, List<RoutingStepVO>> stepMap, Map<String, RoutingVO> routingVOMap) {
        List<WorkOrderVO> workOrderVOS = workOrderService.selectAll();
        Map<String, List<WorkOrderVO>> workOrderMap = StreamUtils.mapListByColumn(workOrderVOS, WorkOrderVO::getProductId);
        Map<String, StandardStepVO> standardStepVOMap = mpsAnalysisContext.getStandardStepVOMap();

        String packing = "包装";
        List<String> supplementaryPackagingCode = new ArrayList<>();
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : deliveryPlanMap.entrySet()) {
            String productCode = entry.getKey();
            RoutingVO routingVO = routingVOMap.get(productCode);
            if (routingVO == null) {
                continue;
            }
            String productId = routingVO.getProductId();
            if (workOrderMap.containsKey(productId)) {
                continue;
            }
            String routingVOId = routingVO.getId();
            List<RoutingStepVO> routingSteps = stepMap.get(routingVOId);
            if (routingSteps == null || routingSteps.size() != 1) {
                continue;
            }
            RoutingStepVO routingStepVO = routingSteps.get(0);
            StandardStepVO standardStepVO = standardStepVOMap.get(routingStepVO.getStandardStepId());
            if (standardStepVO == null || !packing.equals(standardStepVO.getStandardStepName())) {
                continue;
            }
            log.info("发货计划物品：{}，不存在制造订单，准备补充", productCode);
            supplementaryPackagingCode.add(productCode);
        }
        log.info("补充只含包装工序的发货计划：{}", supplementaryPackagingCode);
        return supplementaryPackagingCode.stream().distinct().collect(Collectors.toList());
    }

    private List<String> handleDeleteWorkOrder(List<String> deleteWorkOrderIds) {
        if (CollectionUtils.isEmpty(deleteWorkOrderIds)) {
            return new ArrayList<>();
        }
        // 根据制造订单查找出关联的子订单父订单
        List<String> allDeleteWorkOrders = operationTaskExtDao.selectAllIdMappingWorkOrders(deleteWorkOrderIds);
        List<WorkOrderPO> workOrderPOS = workOrderDao.selectByParams(ImmutableMap.of("ids", allDeleteWorkOrders));
        List<WorkOrderDeletionPO> workOrderDeletionPOS = new ArrayList<>();
        String userId = SystemHolder.getUserId();
        Date date = new Date();
        for (WorkOrderPO workOrderPO : workOrderPOS) {
            WorkOrderDeletionPO workOrderDeletionPO = new WorkOrderDeletionPO();
            BeanUtils.copyProperties(workOrderPO, workOrderDeletionPO);
            workOrderDeletionPO.setId(UUIDUtil.getUUID());
            workOrderDeletionPO.setCreator(userId);
            workOrderDeletionPO.setModifier(userId);
            workOrderDeletionPO.setCreateTime(date);
            workOrderDeletionPO.setModifyTime(date);
            workOrderDeletionPO.setUpperOrderId(workOrderPO.getId());
            workOrderDeletionPO.setRemark(workOrderPO.getRemark() + "-自动排程删除订单");
            workOrderDeletionPOS.add(workOrderDeletionPO);
        }
        if (CollectionUtils.isNotEmpty(workOrderDeletionPOS)) {
            BasePOUtils.insertBatchFiller(workOrderDeletionPOS);
            workOrderDeletionDao.insertBatchWithPrimaryKey(workOrderDeletionPOS);
        }
        log.info("记录删除制造订单deletion数量：{}", workOrderDeletionPOS.size());
        return allDeleteWorkOrders;
    }

    private void preDeleteWorkOrder(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        Date dateTime = new Date();
        // 计划单，仅关联了成品信息
        List<WorkOrderPO> workOrderPOList = mpsAnalysisContext.getWorkOrderAll();
        List<WorkOrderPO> workOrderAll = mpsAnalysisContext.getWorkOrderPOS();
        // 子订单对应订单id
        Map<String, List<WorkOrderPO>> parentOrder = workOrderPOList.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getParentId()))
                .collect(Collectors.groupingBy(WorkOrderPO::getParentId));
        // 当前计划员负责的未计划订单
        List<WorkOrderPO> workOrderPOS = workOrderAll.stream().filter(p ->
                PlannedStatusEnum.UNPLAN.getCode().equals(p.getPlanStatus())).collect(Collectors.toList());
        // MPS运行标记不删除的制造订单
        List<String> lockWorkOrderIds = runCacheWorkOrderIds(RedisKeyManageEnum.MPS_ALGORITHM_FIX_WORK_ORDER.getKey(), mpsAnalysisContext.getCreatorId());
        Map<String, Object> deleteMap = new HashMap<>();
        List<String> deleteWorkOrderIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(workOrderPOS)) {
            List<String> notDeleteOrderIds = new ArrayList<>(lockWorkOrderIds);
            log.info("过滤成品未计划关联计划单前数量：{}", workOrderPOS.size());

            // 过滤出未关联主计划且未计划的工单，并删除它们
            List<WorkOrderPO> deleteWorkOrder = workOrderPOS.stream()
                    .filter(p -> !notDeleteOrderIds.contains(p.getId()))
                    .collect(Collectors.toList());

            // 构建删除数据
            List<WorkOrderPO> deleteWorkOrderAll = new ArrayList<>();
            // 删除未计划父订单级联删除对应半品订单，半品不会存在未计划情况
            for (WorkOrderPO workOrderPO : deleteWorkOrder) {
                // 成品删除对应半品订单
                deleteWorkOrderAll.add(workOrderPO);
                String id = workOrderPO.getId();
                if (parentOrder.containsKey(id)) {
                    log.info("删除父订单，关联删除子订单：{}", id);
                    List<WorkOrderPO> childWorkOrder = parentOrder.get(id);
                    deleteWorkOrderAll.addAll(childWorkOrder);
                }
            }

            log.info("删除未关联计划单未计划制造订单数量：{}", deleteWorkOrderAll.size());
            if (CollectionUtils.isNotEmpty(deleteWorkOrderAll)) {
                List<String> workOrderId = deleteWorkOrderAll.stream()
                        .map(WorkOrderPO::getId)
                        .collect(Collectors.toList());
                deleteWorkOrderIds.addAll(workOrderId);
            }
        }
        deleteMap.put("UNPLAN", deleteWorkOrderIds);
        deleteMap.put("LOCK", lockWorkOrderIds);
        List<String> waitingDeleteNoDemandWorkOrder = runCacheWorkOrderIds(RedisKeyManageEnum.MPS_ALGORITHM_DELETE_WORK_ORDER.getKey(), mpsAnalysisContext.getCreatorId());
        if (CollectionUtils.isNotEmpty(waitingDeleteNoDemandWorkOrder)) {
            deleteMap.put("MPS", waitingDeleteNoDemandWorkOrder);
            log.info("MPS解析结果处理删除锁定期外制造订单数量：{}", waitingDeleteNoDemandWorkOrder.size());
            deleteWorkOrderIds.addAll(waitingDeleteNoDemandWorkOrder);
        }
        if (CollectionUtils.isNotEmpty(deleteWorkOrderIds)) {
            // 记录删除的制造订单id
            List<String> handleDeleteWorkOrder = handleDeleteWorkOrder(deleteWorkOrderIds);
            // 执行删除制造订单
            deletePlanByWorkOrder(handleDeleteWorkOrder);
        }
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("解析前处理删除订单完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime, new Date()));
        mpsAnalysisContext.setScheduleInfoMap(deleteMap);
        log.info("MPS解析前处理完成");
    }

    private List<String> runCacheWorkOrderIds(String key, String creatorId) {
        String redisKey = key.replace("{userId}", creatorId);
        List<Object> cacheDeleteWorkOrderDelete = redisUtil.lGet(redisKey);
        List<String> waitingDeleteNoDemandWorkOrder = CollectionUtils.isEmpty(cacheDeleteWorkOrderDelete) ? new ArrayList<>() :
                (List<String>) cacheDeleteWorkOrderDelete.get(0);
        log.info("cache key：{}", redisKey);
        return waitingDeleteNoDemandWorkOrder;
    }

    private void analysisWorkOrder(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        log.info("开始解析MPS结果");
        Date dateTime1 = new Date();
        List<RzzProductionIntervalOutput> productionIntervalOutputDataList = mpsAnalysisContext.getProductionIntervalOutputDataList();
        Map<String, List<RzzProductionPlannedInIntervalOutput>> inIntervalMpa = mpsAnalysisContext.getInIntervalMap();
        List<RzzProductionPlannedOutput> productionPlannedOutputDataList = mpsAnalysisContext.getProductionPlannedOutputDataList();
        Map<String, RoutingStepVO> routingStepVOMap = mpsAnalysisContext.getRoutingStepVOMap();
        Map<String, StandardStepVO> standardStepVOMap = mpsAnalysisContext.getStandardStepVOMap();

        // 供应
        List<RzzSupplyOutput> supplyOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getSupplyOutputDataList();
        // 分配关系
        List<RzzFulfillmentOutput> fulfillmentOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getFulfillmentOutputDataList();
        // 需求
        List<RzzDemandOutput> demandOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getDemandOutputDataList();
        // BOM关系决策主物料
        List<FinishedHalfProductMapping> finishedHalfProductMappingList = null == mpsAlgorithmOutput.getFinishedHalfProductMappingList()
                ? new ArrayList<>() : mpsAlgorithmOutput.getFinishedHalfProductMappingList();
        // 筛选主物料根据主物料构建另外半片
        Map<String, List<FinishedHalfProductMapping>> finishHalfProductMappingMap = StreamUtils.mapListByColumn(finishedHalfProductMappingList, FinishedHalfProductMapping::getFinishedProductId);
        if (CollectionUtils.isNotEmpty(demandOutputDataList)) {
            for (RzzDemandOutput rzzDemandOutput : demandOutputDataList) {
                String demandTime = rzzDemandOutput.getDemandTime();
                if (StrUtil.isEmpty(demandTime)) {
                    continue;
                }
                Date offsetDay = DateUtil.offsetDay(DateUtils.stringToDate(demandTime, DateUtils.COMMON_DATE_STR1), 1);
                rzzDemandOutput.setDemandTime(DateUtils.dateToString(offsetDay, DateUtils.COMMON_DATE_STR1));
                rzzDemandOutput.setDemandDateTime(offsetDay);
            }
        }
        Date operationDate = new Date();
        mpsAnalysisContext.setOperationDate(operationDate);
        List<String> formingProcessBatch = new ArrayList<>();
        List<String> noDemandInterval = new ArrayList<>();
        String interval = "production_interval";
        String formingProcess = StandardStepEnum.FORMING_PROCESS.getCode();
        List<String> warningLogs = new ArrayList<>();
        for (RzzProductionIntervalOutput productionIntervalOutput : productionIntervalOutputDataList) {
            String productionIntervalId = productionIntervalOutput.getProductionIntervalId();
            if (!productionIntervalId.contains(interval)) {
                formingProcessBatch.add(productionIntervalId);
                continue;
            }
            BigDecimal qty = productionIntervalOutput.getQty();
            List<RzzProductionPlannedInIntervalOutput> inInterval = inIntervalMpa.get(productionIntervalId);
            if (null == inInterval || CollectionUtils.isEmpty(inInterval)) {
                String waningLog = String.format("批量：%s，没有输出批量关系", productionIntervalId);
                log.warn(waningLog);
                warningLogs.add(waningLog);
                continue;
            }
            if (qty.compareTo(BigDecimal.ZERO) == 0) {
                log.warn("批量：{}，没有输出批量关系且合批数量为0", productionIntervalId);
                continue;
            }
            List<String> plannedIds = inInterval.stream().map(RzzProductionPlannedInIntervalOutput::getProductionPlannedId)
                    .collect(Collectors.toList());
            List<RzzProductionPlannedOutput> productionPlannedOutputs = productionPlannedOutputDataList.stream()
                    .filter(p -> plannedIds.contains(p.getSelfProductionPlannedId())).collect(Collectors.toList());
            // 生产批量对应得supply信息
            List<RzzSupplyOutput> plannedSupply = supplyOutputDataList.stream()
                    .filter(s -> plannedIds.contains(s.getSelfPlannedId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(plannedSupply)) {
                RzzSupplyOutput rzzSupplyOutput = plannedSupply.get(0);
                String productSupplyId = rzzSupplyOutput.getProductStockPointId();
                String productBaseId;
                if (productSupplyId.contains(VIRTUAL)) {
                    String[] split = productSupplyId.split(STR_JOIN_VALUE);
                    productBaseId = split[2];
                } else {
                    productBaseId = productSupplyId;
                }
                productionIntervalOutput.setSemiProductId(productBaseId);
            }
            List<String> selfSupplyIds = plannedSupply.stream()
                    .map(RzzSupplyOutput::getSelfSupplyId).collect(Collectors.toList());
            // 批量对应得分配关系关联demand信息
            List<RzzFulfillmentOutput> plannedSupplyFulfillment = fulfillmentOutputDataList.stream()
                    .filter(f -> selfSupplyIds.contains(f.getSupplyId())).collect(Collectors.toList());
            List<String> demandIds = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandId).distinct().sorted().collect(Collectors.toList());
            Optional<RzzDemandOutput> demand = demandOutputDataList.stream().filter(d -> demandIds.contains(d.getDemandId())).findFirst();
            // 判断是否成型批次，镀膜则跳过
            List<String> routingStepIds = productionPlannedOutputs.stream()
                    .map(RzzProductionPlannedOutput::getRoutingStepId).distinct().collect(Collectors.toList());
            for (String routingStepId : routingStepIds) {
                RoutingStepVO routingStepVO = routingStepVOMap.get(routingStepId);
                String standardStepId = routingStepVO.getStandardStepId();
                StandardStepVO standardStepVO = standardStepVOMap.get(standardStepId);
                String standardStepType = standardStepVO.getStandardStepType();
                if (standardStepType.equals(formingProcess)) {
                    // 标记成型批次
                    formingProcessBatch.add(productionIntervalId);
                    break;
                }
            }
            Optional<Date> first = plannedSupplyFulfillment.stream()
                    .map(p -> DateUtils.stringToDate(p.getDemandTime(), DateUtils.COMMON_DATE_STR1)).min(Comparator.comparing(Date::getTime));
            Optional<Date> last = plannedSupplyFulfillment.stream()
                    .map(p -> DateUtils.stringToDate(p.getDemandTime(), DateUtils.COMMON_DATE_STR1)).max(Comparator.comparing(Date::getTime));
            Date earliestDemandTime = first.orElse(null);
            Date latestDemandTime = last.orElse(null);
            RzzDemandOutput rzzDemandOutput = demand.orElse(null);
            if (null == earliestDemandTime) {
                log.warn("批量：{}，没有对应最早需求时间", productionIntervalId);
                noDemandInterval.add(productionIntervalId);
                mpsAnalysisContext.addSupplyDemandBalanceLog(String.format("批量：%s，没有对应最早需求时间", productionIntervalId));
            }
            productionIntervalOutput.setEarliestDemandTime(earliestDemandTime);
            productionIntervalOutput.setLatestDemandTime(latestDemandTime);
            if (Objects.nonNull(rzzDemandOutput)) {
                productionIntervalOutput.setProductBaseId(rzzDemandOutput.getProductStockPointId());
            } else {
                log.warn("批量：{}，没有找到供应需求，物料id设置为空", productionIntervalId);
            }
        }
        if (CollectionUtils.isNotEmpty(noDemandInterval)) {
//            throw new BusinessException(JSON.toJSONString(warning));
            productionIntervalOutputDataList.removeIf(p -> noDemandInterval.contains(p.getProductionIntervalId()));
        }
        productionIntervalOutputDataList.removeIf(p -> !formingProcessBatch.contains(p.getProductionIntervalId()));
        String fixedValue = "True";
        List<String> productLineCodeList = Arrays.asList(mpsAnalysisContext.getAlgorithmLog().getProductLine().split(","));
        productionIntervalOutputDataList.removeIf(p -> !productLineCodeList.contains(p.getStandardResourceId()));

        // 固定订单
        List<RzzProductionIntervalOutput> productionIntervalUpdateDuedateList = productionIntervalOutputDataList.stream()
                .filter(p -> p.getFixed().equals(fixedValue)).collect(Collectors.toList());
        // Chain production filter
        Predicate<RzzProductionIntervalOutput> isChainProduction = p ->
                !p.getFixed().equals(fixedValue)
                        && p.getProductBaseId() != null
                        && p.getSemiProductId() != null
                        && p.getProductBaseId().equals(p.getSemiProductId());
        List<RzzProductionIntervalOutput> chainProductionInterval =
                productionIntervalOutputDataList.stream()
                        .filter(isChainProduction)
                        .collect(Collectors.toList());

        // BOM production filter
        Predicate<RzzProductionIntervalOutput> isBomProduction = p ->
                !p.getFixed().equals(fixedValue)
                        && p.getProductBaseId() != null
                        && p.getSemiProductId() != null
                        && !p.getProductBaseId().equals(p.getSemiProductId());
        List<RzzProductionIntervalOutput> bomProductionInterval =
                productionIntervalOutputDataList.stream()
                        .filter(isBomProduction)
                        .collect(Collectors.toList());
        Map<String, List<RzzProductionIntervalOutput>> bomProductionIntervalMap = StreamUtils.mapListByColumn(bomProductionInterval, RzzProductionIntervalOutput::getProductStockPointId);

        // 解析链式批量
        analyzeBatchProductionInterval(chainProductionInterval, mpsAnalysisContext, interval, inIntervalMpa,
                supplyOutputDataList, fulfillmentOutputDataList, demandOutputDataList, warningLogs,
                finishHalfProductMappingMap, Boolean.TRUE, BigDecimal.ZERO);

        // 解析多级BOM批量
        for (Map.Entry<String, List<RzzProductionIntervalOutput>> entry : bomProductionIntervalMap.entrySet()) {
            List<RzzProductionIntervalOutput> productionIntervalOutputs = entry.getValue();
            // 成品物料id
            String productStockPointId = productionIntervalOutputs.get(0).getProductBaseId();
            // 半品物料id
            String semiProductId = productionIntervalOutputs.get(0).getSemiProductId();
            if (StrUtil.isEmpty(productStockPointId) || StrUtil.isEmpty(semiProductId)) {
                log.warn("解析异常，物料id信息为空：{}", JSON.toJSONString(productionIntervalOutputs.get(0)));
                continue;
            }
            // 校验是否主解析批次
            List<FinishedHalfProductMapping> mappings = finishHalfProductMappingMap.get(productStockPointId);
            List<FinishedHalfProductMapping> whetherMainProduct = Optional.ofNullable(mappings)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(f -> semiProductId.equals(f.getHalfProductId())
                            && YesOrNoEnum.YES.getCode().equals(f.getWhetherMainMaterial()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(whetherMainProduct)) {
                log.info("物料：{}，非主解析批次", semiProductId);
                continue;
            }
            FinishedHalfProductMapping finishedHalfProductMapping = whetherMainProduct.get(0);
            // 库存差值，轮询扣减推出来批量大小
            BigDecimal stockQtyDifference = finishedHalfProductMapping.getStockQtyDifference();
            productionIntervalOutputs.sort(Comparator.comparing(RzzProductionIntervalOutput::getEarliestDemandTime));
            // 解析批量
            analyzeBatchProductionInterval(productionIntervalOutputs, mpsAnalysisContext, interval, inIntervalMpa,
                    supplyOutputDataList, fulfillmentOutputDataList, demandOutputDataList, warningLogs,
                    finishHalfProductMappingMap, Boolean.FALSE, stockQtyDifference);
        }

        // 固定批量交期更新
        afterProcessDueDate(productionIntervalUpdateDuedateList, inIntervalMpa, supplyOutputDataList,
                fulfillmentOutputDataList, demandOutputDataList, mpsAnalysisContext, mpsAlgorithmOutput);
        // 校验需求关系
        checkDemandRelation(mpsAnalysisContext);
        // 供需分配检查批量修正
//        afterFulfillmentCheckUpdate(mpsAnalysisContext, productionIntervalOutputDataList, mpsAlgorithmOutput, demandOutputDataList);

        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("MPS中间结果解析完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime1, new Date()));
        mpsAnalysisContext.getScheduleInfoMap().put("warningLog", warningLogs);
        mpsAnalysisContext.getScheduleInfoMap().put("supplyDemandBalanceLog", mpsAnalysisContext.getSupplyDemandBalanceLog());
        log.info("MPS解析结束");
    }

    private void afterFulfillmentCheckUpdate(MpsAnalysisContext mpsAnalysisContext,
                                             List<RzzProductionIntervalOutput> productionIntervalOutputs,
                                             RzzMpsAlgorithmOutput mpsAlgorithmOutput, List<RzzDemandOutput> demandOutputDataList) {
        /*
         筛选成型工序净需求大于0的产品，进行供需平衡检查
         锁定批次排产数量 ≥ 成型工序净需求的数量，该需求已被满足；
         锁定批次排产数量 < 成型工序净需求的数量，检查是否成型工序净需求的数量 = 锁定批次排产数量 + 非锁定批次排产数量
         如果b满足，则跳过；如果b不满足，则进行修正：
         成型工序净需求的数量 > 锁定批次排产数量 + 非锁定批次排产数量，则根据超出的数量，从非锁定批次排产数量最后一个批次开始扣减（大小片同时扣减）；
         成型工序净需求的数量 < 锁定批次排产数量 + 非锁定批次排产数量，则根据不足的数量，需新增对应数量的批次，新增时要考虑交期和单个批次量。
         取demand明细，按日期升序排，扣减成品/半品库存数量和锁定批次排产数量，剩余的第一个demand时间为第一个批次的交期，批次最多覆盖该产品的最大合批天数的demand数量；然后依次再生成下一个批次
         */
        List<String> scheduleProductIds = StreamUtils.columnToList(demandOutputDataList, RzzDemandOutput::getProductStockPointId).stream().distinct().collect(Collectors.toList());
        List<FinishedHalfProductMapping> finishedHalfProductMappingList = mpsAlgorithmOutput.getFinishedHalfProductMappingList();
        List<FinishedHalfProductMapping> scheduleFinishedHalfProductMapping = finishedHalfProductMappingList.stream()
                .filter(p -> scheduleProductIds.contains(p.getFinishedProductId()))
                .filter(p -> p.getWhetherMainMaterial().equals(YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());

        List<WorkOrderPO> createWorkOrderList = mpsAnalysisContext.getCreateWorkOrderList();
        List<WorkOrderPO> childOrder = StreamUtils.filter(createWorkOrderList, p -> StrUtil.isNotEmpty(p.getParentId()));
        List<RzzProductionIntervalOutput> lockIntervalList = productionIntervalOutputs.stream()
                .filter(p -> p.getFixed().equals("True")).collect(Collectors.toList());
        List<RzzProductionIntervalOutput> newIntervalList = productionIntervalOutputs.stream()
                .filter(p -> !p.getFixed().equals("True")).collect(Collectors.toList());

        // result convert map
        Map<String, List<FinishedHalfProductMapping>> semiFinishedHalfProductMappingMap = StreamUtils.mapListByColumn(finishedHalfProductMappingList, FinishedHalfProductMapping::getFinishedProductId);
        Map<String, List<RzzProductionIntervalOutput>> lockIntervalProductMap = getProductInterval(lockIntervalList);
        Map<String, List<RzzProductionIntervalOutput>> newIntervalProductMap = getProductInterval(newIntervalList);
        Map<String, List<WorkOrderPO>> workOrderProductMap = StreamUtils.mapListByColumn(createWorkOrderList, WorkOrderPO::getProductId);
        Map<String, List<WorkOrderPO>> childOrderMap = StreamUtils.mapListByColumn(childOrder, WorkOrderPO::getParentId);
        Map<String, List<RzzDemandOutput>> demandMap = demandOutputDataList.stream()
                .filter(p -> p.getDemandType().equals(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode()))
                .collect(Collectors.groupingBy(RzzDemandOutput::getProductStockPointId));

        for (FinishedHalfProductMapping finishedHalfProductMapping : scheduleFinishedHalfProductMapping) {
            boolean whetherMainMaterial = finishedHalfProductMapping.getWhetherMainMaterial().equals(YesOrNoEnum.YES.getCode());
            if (!whetherMainMaterial) {
                continue;
            }
            // 是否多层级BOM结构
            boolean whetherMultilevelBom = finishedHalfProductMapping.getWhetherMultilevelBom().equals(YesOrNoEnum.YES.getCode());
            String productId = whetherMultilevelBom ? finishedHalfProductMapping.getHalfProductId() : finishedHalfProductMapping.getFinishedProductId();
            String productCode = whetherMultilevelBom ? finishedHalfProductMapping.getHalfProductCode() : finishedHalfProductMapping.getFinishedProductCode();
            String finishedProductId = finishedHalfProductMapping.getFinishedProductId();
            if (!demandMap.containsKey(finishedProductId)) {
                continue;
            }
            BigDecimal netDemandQty = finishedHalfProductMapping.getNetDemandQty();
            if (netDemandQty.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("物料：{}，净需求为0，不进行供需匹配", productCode);
                continue;
            }
            // 锁定批次数量
            BigDecimal lockQty = getIntervalQty(lockIntervalProductMap, productId, productCode);
            if (lockQty.compareTo(netDemandQty) >= 0) {
                log.info("物料：{}，锁定需求已满足，不进行供需匹配", productCode);
                continue;
            }
            List<RzzProductionIntervalOutput> newIntervalProductMapOrDefault = newIntervalProductMap.getOrDefault(productId, newIntervalProductMap.get(productCode));
            if (CollectionUtils.isEmpty(newIntervalProductMapOrDefault)) {
                log.info("物料：{}，新增批次排产数量为0，不进行供需匹配", productCode);
                continue;
            }
            // 新增批次排产数量
            BigDecimal newQty = getIntervalQty(newIntervalProductMap, productId, productCode);
            // 汇总数量
            BigDecimal sumDemand = BigDecimalUtils.add(lockQty, newQty);
            if (sumDemand.compareTo(netDemandQty) == 0) {
                log.info("物料：{}，锁定需求+新增批次量已满足净需求，不进行供需匹配", productCode);
                continue;
            }
            RzzProductionIntervalOutput rzzProductionIntervalOutput = newIntervalProductMapOrDefault.get(0);
            boolean differenceStatus = sumDemand.compareTo(netDemandQty) > 0;
            BigDecimal difference = sumDemand.subtract(netDemandQty).abs();
            if (differenceStatus) {
                // 排产数量大于净需求，根据超出的数量，从非锁定批次排产数量最后一个批次开始扣减（大小片同时扣减）；
                deductionInterval(difference, mpsAnalysisContext, finishedHalfProductMapping,
                        workOrderProductMap, childOrderMap);
            } else {
                // 排产数量小于净需求，取demand明细，按日期升序排，扣减成品/半品库存数量和锁定批次排产数量，剩余的第一个demand时间为第一个批次的交期，
                // 批次最多覆盖该产品的最大合批天数的demand数量；然后依次再生成下一个批次
                replenishInterval(difference, mpsAnalysisContext, finishedHalfProductMapping,
                        demandMap, lockQty, rzzProductionIntervalOutput, semiFinishedHalfProductMappingMap);
            }
        }
        handleBalanceResult(mpsAnalysisContext);
    }

    private void handleBalanceResult(MpsAnalysisContext mpsAnalysisContext) {
        List<String> tempDeleteWorkOrderList = mpsAnalysisContext.getTempDeleteWorkOrderList();
        Map<String, BigDecimal> tempUpdateWorkOrderList = mpsAnalysisContext.getTempUpdateWorkOrderList();

        if (CollectionUtils.isNotEmpty(tempDeleteWorkOrderList)) {
            List<WorkOrderPO> deleteTempOrder = mpsAnalysisContext.getCreateWorkOrderList().stream()
                    .filter(p -> tempDeleteWorkOrderList.contains(p.getId())).collect(Collectors.toList());
            for (WorkOrderPO workOrderPO : deleteTempOrder) {
                mpsAnalysisContext.addSupplyDemandBalanceLog(String.format("批量：%s，因供需平衡被删除", workOrderPO.getRemark()));
            }
            mpsAnalysisContext.getCreateWorkOrderList().removeIf(p -> tempDeleteWorkOrderList.contains(p.getId()));
            mpsAnalysisContext.getCreateDemandsList().removeIf(p -> tempDeleteWorkOrderList.contains(p.getCountingUnitId()));
            mpsAnalysisContext.getCreateSupplyList().removeIf(p -> tempDeleteWorkOrderList.contains(p.getSupplyOrderId()));
            mpsAnalysisContext.getCreateFulfillmentList().removeIf(p -> tempDeleteWorkOrderList.contains(p.getSupplyOrderId()));
        }
        if (MapUtil.isNotEmpty(tempUpdateWorkOrderList)) {
            for (WorkOrderPO workOrderPO : mpsAnalysisContext.getCreateWorkOrderList()) {
                String id = workOrderPO.getId();
                if (!tempUpdateWorkOrderList.containsKey(id)) {
                    continue;
                }
                BigDecimal newQuantity = tempUpdateWorkOrderList.get(id);
                workOrderPO.setQuantity(newQuantity);
                mpsAnalysisContext.addSupplyDemandBalanceLog(String.format("批量：%s，因供需平衡被修改", workOrderPO.getRemark()));
                String bomType = workOrderPO.getBomType();
                if (bomType.equals(CHILD_BOM)) {
                    // 修改 demand/supply/fulfillment
                    mpsAnalysisContext.getCreateDemandsList().stream()
                            .filter(d -> id.equals(d.getCountingUnitId()))
                            .forEach(d -> d.setQuantity(newQuantity));

                    // Update supply quantity
                    mpsAnalysisContext.getCreateSupplyList().stream()
                            .filter(s -> id.equals(s.getSupplyOrderId()))
                            .forEach(s -> s.setQuantity(newQuantity));

                    // Update fulfillment quantity
                    mpsAnalysisContext.getCreateFulfillmentList().stream()
                            .filter(f -> id.equals(f.getSupplyOrderId()))
                            .forEach(f -> f.setFulfillmentQuantity(newQuantity));
                }
            }
        }

    }

    private void deductionInterval(BigDecimal difference, MpsAnalysisContext mpsAnalysisContext,
                                   FinishedHalfProductMapping finishedHalfProductMapping,
                                   Map<String, List<WorkOrderPO>> workOrderProductMap,
                                   Map<String, List<WorkOrderPO>> childOrderMap) {
        String finishedProductId = finishedHalfProductMapping.getFinishedProductId();
        String whetherMultilevelBom = finishedHalfProductMapping.getWhetherMultilevelBom();
        List<WorkOrderPO> workOrderPOS = workOrderProductMap.get(finishedProductId);
        // 按照交期倒序排列，优先处理最晚的工单
        workOrderPOS.sort(Comparator.comparing(WorkOrderPO::getDueDate).reversed());
        for (WorkOrderPO workOrderPO : workOrderPOS) {
            if (difference.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            String id = workOrderPO.getId();
            BigDecimal quantity = workOrderPO.getQuantity();
            if (difference.compareTo(quantity) >= 0) {
                // 差值大于等于当前工单数量，完全扣除当前工单，继续处理下一个
                difference = difference.subtract(quantity);
                mpsAnalysisContext.getTempDeleteWorkOrderList().add(id);
                if (whetherMultilevelBom.equals(YesOrNoEnum.YES.getCode())) {
                    // 父订单关联子订单级联删除
                    List<WorkOrderPO> childOrder = childOrderMap.get(id);
                    List<String> childOrderIds = StreamUtils.columnToList(childOrder, WorkOrderPO::getId);
                    mpsAnalysisContext.getTempDeleteWorkOrderList().addAll(childOrderIds);
                }
            } else {
                // 差值小于当前工单数量，部分扣除当前工单数量
                BigDecimal newQuantity = quantity.subtract(difference);
                mpsAnalysisContext.getTempUpdateWorkOrderList().put(id, newQuantity);
                if (whetherMultilevelBom.equals(YesOrNoEnum.YES.getCode())) {
                    List<WorkOrderPO> childOrder = childOrderMap.get(id);
                    for (WorkOrderPO orderPO : childOrder) {
                        BigDecimal childOrderQty = orderPO.getQuantity();
                        BigDecimal newChildOrderQty = childOrderQty.subtract(difference);
                        if (newChildOrderQty.compareTo(BigDecimal.ZERO) <= 0) {
                            mpsAnalysisContext.getTempDeleteWorkOrderList().add(orderPO.getId());
                        } else {
                            mpsAnalysisContext.getTempUpdateWorkOrderList().put(orderPO.getId(), newChildOrderQty);
                        }
                    }
                }
                difference = BigDecimal.ZERO;
            }
        }
    }


    private void replenishInterval(BigDecimal difference, MpsAnalysisContext mpsAnalysisContext,
                                   FinishedHalfProductMapping finishedHalfProductMapping,
                                   Map<String, List<RzzDemandOutput>> demandMap, BigDecimal lockQty,
                                   RzzProductionIntervalOutput rzzProductionIntervalOutput,
                                   Map<String, List<FinishedHalfProductMapping>> semiFinishedHalfProductMappingMap) {
        String finishedProductId = finishedHalfProductMapping.getFinishedProductId();

        List<RzzDemandOutput> rzzDemandOutputs = demandMap.get(finishedProductId);
        rzzDemandOutputs.sort(Comparator.comparing(RzzDemandOutput::getDemandTime));
        RzzDemandOutput lastDemand = rzzDemandOutputs.get(rzzDemandOutputs.size() - 1);
        Date lastDemandTime = DateUtils.stringToDate(lastDemand.getDemandTime(), DateUtils.COMMON_DATE_STR1);

        // 计算锁定的/成品率数量
        BigDecimal lockYieldQty = calculateYieldQty(lockQty, mpsAnalysisContext, finishedProductId);
        // 成品库存
        BigDecimal stockQty = finishedHalfProductMapping.getStockQty();
        BigDecimal sumYieldQty = BigDecimalUtils.add(lockYieldQty, stockQty);
        String startDueDate = null;
        // 遍历需求，找到需要补充排产的第一个交期
        for (RzzDemandOutput rzzDemandOutput : rzzDemandOutputs) {
            String demandTime = rzzDemandOutput.getDemandTime();
            BigDecimal demandQty = rzzDemandOutput.getQty();
            if (demandQty.compareTo(sumYieldQty) > 0) {
                // 需求量大于可用量，需要排产，设置交期
                startDueDate = demandTime;
                break;
            } else if (demandQty.compareTo(sumYieldQty) == 0) {
                // 需求量等于可用量，刚好满足，但仍需排产以防后续需求
                startDueDate = demandTime;
                break;
            } else {
                // 需求量小于可用量，扣减可用量继续下一条需求
                sumYieldQty = BigDecimalUtils.subtract(sumYieldQty, demandQty);
            }
        }
        // 如果找到需要排产的交期，则创建新的排产批次
        if (startDueDate != null) {
            // 最大合批天数
            Date startDueDateTime = DateUtils.stringToDate(startDueDate, DateUtils.COMMON_DATE_STR1);
            String maxAheadProdTime = finishedHalfProductMapping.getMaxAheadProdTime();
            BigDecimal maxAheadProdTimeVal = BigDecimalUtils.toBigDecimal(maxAheadProdTime).setScale(0, RoundingMode.HALF_UP);
            int maxForCount = 1;
            while (startDueDateTime.getTime() < lastDemandTime.getTime()) {
                if (difference.compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
                Date endTime = DateUtil.offsetDay(startDueDateTime, maxAheadProdTimeVal.intValue());
                Date finalStartDueDateTime = startDueDateTime;
                List<RzzDemandOutput> mergeDemand = rzzDemandOutputs.stream()
                        .filter(p -> p.getDemandDateTime().getTime() >= finalStartDueDateTime.getTime()
                                && p.getDemandDateTime().getTime() <= endTime.getTime()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(mergeDemand)) {
                    startDueDateTime = endTime;
                } else {
                    mergeDemand.sort(Comparator.comparing(RzzDemandOutput::getDemandTime).reversed());
                    Date lastDemandDate = mergeDemand.get(0).getDemandDateTime();
                    if (lastDemandDate.getTime() > startDueDateTime.getTime()) {
                        startDueDateTime = lastDemandDate;
                    } else {
                        startDueDateTime = endTime;
                    }
                    BigDecimal mergeDemandQty = mergeDemand.stream().map(RzzDemandOutput::getQty).reduce(BigDecimal::add).get();
                    // 根据合并的批量需求，补充新增批次，处理数量扣减
                    BigDecimal supplyQty = mergeDemandQty.min(difference);
                    difference = BigDecimalUtils.subtract(difference, supplyQty);
                    createInterval(supplyQty, mergeDemand, mpsAnalysisContext, finishedHalfProductMapping,
                            rzzProductionIntervalOutput, semiFinishedHalfProductMappingMap);
                }
                maxForCount++;
                if (maxForCount > 90) {
                    throw new BusinessException("补充订单异常，循环次数异常");
                }
            }
            log.info("为产品 {} 找到需要补充排产的起始交期: {}", finishedProductId, startDueDate);
        }
    }

    private void createInterval(BigDecimal supplyQty, List<RzzDemandOutput> mergeDemand,
                                MpsAnalysisContext mpsAnalysisContext,
                                FinishedHalfProductMapping finishedHalfProductMapping,
                                RzzProductionIntervalOutput productionIntervalOutput,
                                Map<String, List<FinishedHalfProductMapping>> finishHalfProductMappingMap) {
        String whetherMultilevelBom = finishedHalfProductMapping.getWhetherMultilevelBom();
        String finishedProductId = finishedHalfProductMapping.getFinishedProductId();
        String semiProductId = finishedHalfProductMapping.getHalfProductId();
        BigDecimal lockYieldQty = calculateYieldQty(supplyQty, mpsAnalysisContext, semiProductId);
        List<String> demandIds = StreamUtils.columnToList(mergeDemand, RzzDemandOutput::getSelfDemandId);
        // 创建成品批次
        Date earliestDemandTime = mergeDemand.stream().map(RzzDemandOutput::getDemandDateTime).min(Comparator.comparing(Date::getTime)).orElse(null);
        Date latestDemandTime = mergeDemand.stream().map(RzzDemandOutput::getDemandDateTime).max(Comparator.comparing(Date::getTime)).orElse(null);
        productionIntervalOutput.setEarliestDemandTime(earliestDemandTime);
        productionIntervalOutput.setLatestDemandTime(latestDemandTime);
        // 构建链式/成品制造订单
        WorkOrderPO finishedWorkOrder = buildWorkOrder(lockYieldQty, demandIds, "build_interval",
                productionIntervalOutput, mpsAnalysisContext, true);
        mpsAnalysisContext.addSupplyDemandBalanceLog(String.format("订单：%s，供需平衡补充", finishedWorkOrder.getId()));
        if (whetherMultilevelBom.equals(YesOrNoEnum.YES.getCode())) {
            List<WorkOrderPO> semiWorkOrderPOList = new ArrayList<>();
            // 创建半品批次/需求/供应/供应关系
            // 构建当前半品需求的制造订单及demand
            semiFinishedWorkOrder(semiProductId, earliestDemandTime, supplyQty,
                    "build_interval_semi", mpsAnalysisContext, productionIntervalOutput, finishedWorkOrder, semiWorkOrderPOList);
            // 获取其余得小片
            List<FinishedHalfProductMapping> semiProduct = finishHalfProductMappingMap.get(finishedProductId).stream()
                    .filter(p -> !p.getHalfProductId().equals(semiProductId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(semiProduct)) {
                return;
            }
            for (FinishedHalfProductMapping semiProductMapping : semiProduct) {
                String halfProductId = semiProductMapping.getHalfProductId();
                semiFinishedWorkOrder(halfProductId, earliestDemandTime, supplyQty,
                        "build_interval_semi", mpsAnalysisContext, productionIntervalOutput, finishedWorkOrder, semiWorkOrderPOList);
            }
        }

    }

    private BigDecimal calculateYieldQty(BigDecimal qty, MpsAnalysisContext mpsAnalysisContext, String productId) {
        RoutingVO routingVO = mpsAnalysisContext.getRoutingVOMap().get(productId);
        List<RoutingStepVO> routingStepVOS = mpsAnalysisContext.getRoutingStepOnRoutingMap().get(routingVO.getId());
        // FIXME 考虑是否去除成型前工序
        List<RoutingStepDO> routingStepDOList = routingStepVOS.stream().map(p -> {
                    RoutingStepDO routingStepDO = new RoutingStepDO();
                    BeanUtils.copyProperties(p, routingStepDO);
                    return routingStepDO;
                })
                .collect(Collectors.toList());
        Map<Integer, BigDecimal> sequenceQtyMap = AbstractOperationSync.computeOperationQty(qty, routingStepDOList);
        Integer maxSequence = routingStepDOList.stream().map(RoutingStepDO::getSequenceNo).max(Comparator.comparingInt(Integer::intValue)).get();
        return sequenceQtyMap.get(maxSequence);
    }


    private BigDecimal getIntervalQty(Map<String, List<RzzProductionIntervalOutput>> intervalProductMap, String productId, String productCode) {
        List<RzzProductionIntervalOutput> lockInterval = intervalProductMap.getOrDefault(productId, intervalProductMap.get(productCode));
        return CollectionUtils.isEmpty(lockInterval) ? BigDecimal.ZERO :
                lockInterval.stream().map(RzzProductionIntervalOutput::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    private Map<String, List<RzzProductionIntervalOutput>> getProductInterval(List<RzzProductionIntervalOutput> productionIntervalUpdateDuedateList) {
        return Optional.ofNullable(productionIntervalUpdateDuedateList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.groupingBy(p -> {
                    String productStockPointId = p.getProductStockPointId();
                    if (productStockPointId == null) {
                        return "";
                    }
                    if (productStockPointId.contains(INTERMEDIATE_DUMMY)) {
                        Matcher matcher = Pattern.compile("&\\d+&([^*]+)\\*").matcher(productStockPointId);
                        if (matcher.find()) {
                            return matcher.group(1);
                        }
                        return "";
                    } else {
                        String[] split = productStockPointId.split("\\*");
                        return split.length > 0 ? split[0] : "";
                    }
                }));
    }

    private void checkDemandRelation(MpsAnalysisContext mpsAnalysisContext) {
        List<String> demandCheck = new ArrayList<>();
        // 需求
        List<RzzDemandOutput> demandOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getDemandOutputDataList()
                .stream().filter(p -> p.getDemandType().equals(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode())).collect(Collectors.toList());
        // 分配关系
        List<RzzFulfillmentOutput> fulfillmentOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getFulfillmentOutputDataList();
        Map<String, List<RzzFulfillmentOutput>> fulfillmenMap = StreamUtils.mapListByColumn(fulfillmentOutputDataList, RzzFulfillmentOutput::getDemandId);
        // 供应
        List<RzzSupplyOutput> supplyOutputDataList = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getSupplyOutputDataList();

        if (CollectionUtils.isEmpty(demandOutputDataList)) {
            return;
        }
        for (RzzDemandOutput rzzDemandOutput : demandOutputDataList) {
            BigDecimal qty = rzzDemandOutput.getQty();
            BigDecimal unfulfilledQty = rzzDemandOutput.getUnfulfilledQty();
            if (unfulfilledQty.compareTo(qty) == 0) {
                continue;
            }
            String demandId = rzzDemandOutput.getDemandId();
            if (!fulfillmenMap.containsKey(demandId)) {
                demandCheck.add(String.format("需求：%s，被分配，没有fulfillmentDeliveryOut分配关系", demandId));
                continue;
            }
            List<RzzFulfillmentOutput> allPegFulfillmentMapping = fulfillmenMap.get(demandId);
            List<RzzFulfillmentOutput> pegFulfillmentMapping = allPegFulfillmentMapping.stream()
                    .filter(p -> p.getSupplyType().equals(SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pegFulfillmentMapping)) {
                continue;
            }
            List<String> supplyIds = StreamUtils.columnToList(pegFulfillmentMapping, RzzFulfillmentOutput::getSupplyId);
            List<RzzSupplyOutput> pegSupplyList = supplyOutputDataList.stream().filter(p ->
                    supplyIds.contains(p.getSelfSupplyId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pegSupplyList)) {
                demandCheck.add(String.format("需求：%s，被分配，有fulfillmentDeliveryOut分配关系，没有supplyOut关系", demandId));
                continue;
            }
            List<String> pegSupplyIds = StreamUtils.columnToList(pegSupplyList, RzzSupplyOutput::getSelfPlannedId);
            List<RzzProductionPlannedInIntervalOutput> productionPlannedInIntervalOutputDataList = mpsAnalysisContext
                    .getRzzMpsAlgorithmOutput().getProductionPlannedInIntervalOutputDataList()
                    .stream().filter(p -> pegSupplyIds.contains(p.getProductionPlannedId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productionPlannedInIntervalOutputDataList)) {
                demandCheck.add(String.format("需求：%s，被分配，有fulfillmentDeliveryOut分配关系，有supplyOut关系，没有productionPlannedInInterval关系", demandId));
                continue;
            }
            List<String> productionPlannedInIntervalIds = StreamUtils.columnToList(productionPlannedInIntervalOutputDataList,
                    RzzProductionPlannedInIntervalOutput::getProductionIntervalId);
            List<RzzProductionIntervalOutput> productionPlannedOutputOutputDataList = mpsAnalysisContext
                    .getRzzMpsAlgorithmOutput().getProductionIntervalOutputDataList()
                    .stream().filter(p -> productionPlannedInIntervalIds.contains(p.getProductionIntervalId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productionPlannedOutputOutputDataList)) {
                demandCheck.add(String.format("需求：%s，被分配，有fulfillmentDeliveryOut分配关系，有supplyOut关系，有productionPlannedInIntervalOut关系，没有productionIntervalOut批量关系", demandId));
            }
        }
        mpsAnalysisContext.getScheduleInfoMap().put("demandCheck", demandCheck);
    }

    private void analyzeBatchProductionInterval(List<RzzProductionIntervalOutput> productionIntervalOutputDataList,
                                                MpsAnalysisContext mpsAnalysisContext, String interval,
                                                Map<String, List<RzzProductionPlannedInIntervalOutput>> inIntervalMpa,
                                                List<RzzSupplyOutput> supplyOutputDataList, List<RzzFulfillmentOutput> fulfillmentOutputDataList,
                                                List<RzzDemandOutput> demandOutputDataList,
                                                List<String> warningLogs,
                                                Map<String, List<FinishedHalfProductMapping>> finishHalfProductMappingMap, Boolean whetherChain,
                                                BigDecimal stockQtyDifference) {
        if (CollectionUtils.isEmpty(productionIntervalOutputDataList)) {
            String method = whetherChain.equals(Boolean.TRUE) ? "链式批量" : "BOM批量";
            log.info("解析结果productionIntervalOutputDataList为空，解析类型：{}", method);
            return;
        }
        // 仅成型工序创建制造订单
        for (RzzProductionIntervalOutput productionIntervalOutput : productionIntervalOutputDataList) {
            String productionIntervalId = productionIntervalOutput.getProductionIntervalId();
            BigDecimal qty = productionIntervalOutput.getQty();
            if (qty.compareTo(BigDecimal.ZERO) == 0 && !productionIntervalId.contains(interval)) {
                // 如果当前批次批量=0并且不是新的批次，说明量被合到了另一个批次，当前制造订单做取消计划并且删除
                mpsAnalysisContext.getWorkOrderCancelPlanIdList().add(productionIntervalId);
                continue;
            }
            List<RzzProductionPlannedInIntervalOutput> inInterval = inIntervalMpa.get(productionIntervalId);
            if (CollectionUtils.isEmpty(inInterval)) {
                log.warn("生产批量关联不到批量明细：{}", productionIntervalId);
                continue;
            }
            List<String> plannedIds = inInterval.stream().map(RzzProductionPlannedInIntervalOutput::getProductionPlannedId)
                    .collect(Collectors.toList());
            // 生产批量对应得supply信息
            List<RzzSupplyOutput> plannedSupply = supplyOutputDataList.stream()
                    .filter(s -> plannedIds.contains(s.getSelfPlannedId())).collect(Collectors.toList());
            List<String> selfSupplyIds = plannedSupply.stream()
                    .map(RzzSupplyOutput::getSelfSupplyId).collect(Collectors.toList());
            // 批量对应得分配关系关联demand信息
            List<RzzFulfillmentOutput> plannedSupplyFulfillment = fulfillmentOutputDataList.stream()
                    .filter(p -> p.getQty().compareTo(BigDecimal.ZERO) > 0)
                    .filter(f -> selfSupplyIds.contains(f.getSupplyId())).collect(Collectors.toList());
            List<String> demandIds = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandId).distinct().collect(Collectors.toList());

            // 分配需求类型
            List<String> demandTypes = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandType).distinct().collect(Collectors.toList());

            if (demandTypes.contains(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode())) {
                // 当前批量对应的客户订单
                List<RzzDemandOutput> customerOrderList = demandOutputDataList.stream()
                        .filter(p -> demandIds.contains(p.getSelfDemandId())).collect(Collectors.toList());
                // 单个虚拟物料直接构建成品的制造订单及需求，链式结构，合批在关键工序的虚拟物料
                List<String> selfDemandId = customerOrderList.stream()
                        .map(RzzDemandOutput::getSelfDemandId).collect(Collectors.toList());
                // 物料id
                String semiProductId = productionIntervalOutput.getSemiProductId();
                NewProductStockPointVO newProductStockPointVO = mpsAnalysisContext.getProductMap().getOrDefault(semiProductId, null);
                if (Objects.isNull(newProductStockPointVO)) {
                    log.warn("批次：{}，没有找到物料：{}", productionIntervalId, semiProductId);
                    continue;
                }
                MdsProductStockPointBaseVO pointBaseVO = mpsAnalysisContext.getProductStockPointBaseMap()
                        .getOrDefault(newProductStockPointVO.getProductCode(), null);
                if (null == pointBaseVO) {
                    log.warn("批次：{}，没有找到物料基础数据：{}", productionIntervalId, newProductStockPointVO.getProductCode());
                    continue;
                }
                if (whetherChain) {
                    // 构建链式成品制造订单
                    buildWorkOrder(qty,
                            selfDemandId, productionIntervalId,
                            productionIntervalOutput, mpsAnalysisContext, Boolean.TRUE);
                } else {
                    // 半品订单
                    List<WorkOrderPO> semiWorkOrderPOList = new ArrayList<>();
                    String finishedProductId = productionIntervalOutput.getProductBaseId();
                    // 成品订单
                    WorkOrderPO finishedWorkOrder = buildWorkOrder(null,
                            selfDemandId, productionIntervalId,
                            productionIntervalOutput, mpsAnalysisContext, Boolean.FALSE);
                    Date earliestDemandTime = productionIntervalOutput.getEarliestDemandTime();
                    // 构建当前半品需求的制造订单及demand
                    assert finishedWorkOrder != null;
                    semiFinishedWorkOrder(semiProductId, earliestDemandTime, qty,
                            productionIntervalId, mpsAnalysisContext, productionIntervalOutput, finishedWorkOrder, semiWorkOrderPOList);
                    // 获取其余得小片
                    List<FinishedHalfProductMapping> semiProduct = finishHalfProductMappingMap.get(finishedProductId).stream()
                            .filter(p -> !p.getHalfProductId().equals(semiProductId)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(semiProduct)) {
                        finishedWorkOrder.setQuantity(semiWorkOrderPOList.get(0).getQuantity());
                        mpsAnalysisContext.getCreateWorkOrderList().add(finishedWorkOrder);
                        continue;
                    }
                    for (FinishedHalfProductMapping semiProductMapping : semiProduct) {
                        String halfProductId = semiProductMapping.getHalfProductId();
                        BigDecimal newQty;
                        if (stockQtyDifference.compareTo(BigDecimal.ZERO) == 0) {
                            // 当库存差为0时，直接使用qty
                            newQty = qty;
                        } else if (stockQtyDifference.compareTo(qty) >= 0) {
                            // 当库存差大于等于qty时，消耗库存差
                            stockQtyDifference = stockQtyDifference.subtract(qty);
                            finishedWorkOrder.setRemark(finishedWorkOrder.getRemark() + "小片因库存不生成;");
                            break;
                        } else {
                            // 当库存差小于qty时，计算需要新生产的数量
                            newQty = qty.subtract(stockQtyDifference);
                            stockQtyDifference = BigDecimal.ZERO;
                        }
                        // 只有当需要生产时才调用
                        if (newQty.compareTo(BigDecimal.ZERO) > 0) {
                            semiFinishedWorkOrder(halfProductId, earliestDemandTime,
                                    newQty, productionIntervalId, mpsAnalysisContext,
                                    productionIntervalOutput, finishedWorkOrder, semiWorkOrderPOList);
                        }
                    }
                    semiWorkOrderPOList.sort(Comparator.comparing(WorkOrderPO::getQuantity).reversed());
                    finishedWorkOrder.setQuantity(semiWorkOrderPOList.get(0).getQuantity());
                    mpsAnalysisContext.getCreateWorkOrderList().add(finishedWorkOrder);
                }
            } else {
                String logMsg = String.format("生产批量：%s，没有找到供应需求信息", productionIntervalId);
                warningLogs.add(logMsg);
                log.warn("生产批量：{}，没有找到供应需求信息", productionIntervalId);
            }
        }
    }

    private void afterProcessDueDate(List<RzzProductionIntervalOutput> productionIntervalUpdateDuedateList,
                                     Map<String, List<RzzProductionPlannedInIntervalOutput>> inIntervalMpa,
                                     List<RzzSupplyOutput> supplyOutputDataList,
                                     List<RzzFulfillmentOutput> fulfillmentOutputDataList,
                                     List<RzzDemandOutput> demandOutputDataList, MpsAnalysisContext mpsAnalysisContext,
                                     RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        log.info("后处理MPS更新固定制造订单交期开始，数量：{}", productionIntervalUpdateDuedateList.size());
        if (CollectionUtils.isEmpty(productionIntervalUpdateDuedateList)) {
            return;
        }
        Map<String, WorkOrderPO> workOrderMap = mpsAnalysisContext.getWorkOrderMap();
        PlanningHorizonVO planningHorizonVO = mpsAnalysisContext.getPlanningHorizonVO();
        Date planEndTime = planningHorizonVO.getPlanEndTime();
        List<WorkOrderPO> workOrderPOS = new ArrayList<>();
        // 合并workOrder更新交期数据
        List<ProductionPlannedMergeMapping> productionPlannedMergeMappings = mpsAlgorithmOutput.getProductionPlannedMergeMappingList();
        log.info("算法解析更新交期缓存mapping关系数量：{}", productionPlannedMergeMappings.size());
        Map<String, List<ProductionPlannedMergeMapping>> mergeOrderMap = StreamUtils.mapListByColumn(productionPlannedMergeMappings,
                ProductionPlannedMergeMapping::getWorkOrderId);
        for (RzzProductionIntervalOutput productionIntervalOutput : productionIntervalUpdateDuedateList) {
            String productionIntervalId = productionIntervalOutput.getProductionIntervalId();
            List<ProductionPlannedMergeMapping> mergeProductionPlannedMergeMappings = mergeOrderMap.get(productionIntervalId);
            if (!inIntervalMpa.containsKey(productionIntervalId)) {
                log.warn("固定制造订单找不到批量关系-更新计划结束期间交期：{}", productionIntervalId);
                getUpdateDueDateOrder(planEndTime, workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
                continue;
            }
            List<RzzProductionPlannedInIntervalOutput> inInterval = inIntervalMpa.get(productionIntervalId);
            List<String> plannedIds = inInterval.stream().map(RzzProductionPlannedInIntervalOutput::getProductionPlannedId)
                    .collect(Collectors.toList());
            // 生产批量对应得supply信息
            List<RzzSupplyOutput> plannedSupply = supplyOutputDataList.stream()
                    .filter(s -> plannedIds.contains(s.getSelfPlannedId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(plannedSupply)) {
                log.warn("固定制造订单找不到供应信息-更新计划结束期间交期：{}", productionIntervalId);
                getUpdateDueDateOrder(planEndTime, workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
                continue;
            }
            List<String> selfSupplyIds = plannedSupply.stream()
                    .map(RzzSupplyOutput::getSelfSupplyId).collect(Collectors.toList());
            // 批量对应得分配关系关联demand信息
            List<RzzFulfillmentOutput> plannedSupplyFulfillment = fulfillmentOutputDataList.stream()
                    .filter(p -> p.getQty().compareTo(BigDecimal.ZERO) > 0)
                    .filter(f -> selfSupplyIds.contains(f.getSupplyId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(plannedSupplyFulfillment)) {
                log.warn("固定制造订单找不到供需关系-更新计划结束期间交期：{}", productionIntervalId);
                getUpdateDueDateOrder(planEndTime, workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
                continue;
            }
            List<String> demandIds = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandId).distinct().sorted().collect(Collectors.toList());
            // 分配需求类型
            List<String> demandTypes = plannedSupplyFulfillment.stream()
                    .map(RzzFulfillmentOutput::getDemandType).distinct().collect(Collectors.toList());

            if (demandTypes.contains(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode())) {
                // 当前批量对应的客户订单
                List<RzzDemandOutput> customerOrderList = demandOutputDataList.stream()
                        .filter(d -> demandIds.contains(d.getSelfDemandId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(customerOrderList)) {
                    log.warn("固定制造订单找不到原始需求-更新计划结束期间交期：{}", productionIntervalId);
                    getUpdateDueDateOrder(planEndTime, workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
                    continue;
                }
                customerOrderList.sort(Comparator.comparing(RzzDemandOutput::getDemandTime));
                RzzDemandOutput rzzDemandOutput = customerOrderList.get(0);
                String demandTime = rzzDemandOutput.getDemandTime();
                if (!workOrderMap.containsKey(productionIntervalId)) {
                    log.warn("固定制造订单找不到原始制造订单-更新计划结束期间交期：{}", productionIntervalId);
                    continue;
                }
                getUpdateDueDateOrder(DateUtils.stringToDate(demandTime, DateUtils.COMMON_DATE_STR1), workOrderPOS, workOrderMap, mergeProductionPlannedMergeMappings);
            }
        }
        if (CollectionUtils.isNotEmpty(workOrderPOS)) {
            BasePOUtils.updateBatchFiller(workOrderPOS);
            Lists.partition(workOrderPOS, 1000).forEach(p -> workOrderDao.updateBatch(p));
        }
        log.info("二次排程待更新固定制造订单交期数量：{}", workOrderPOS.size());
    }

    private void getUpdateDueDateOrder(Date demandTime, List<WorkOrderPO> workOrderPOS,
                                       Map<String, WorkOrderPO> workOrderMap,
                                       List<ProductionPlannedMergeMapping> mergeProductionPlannedMergeMappings) {

        for (ProductionPlannedMergeMapping mergeProductionPlannedMergeMapping : mergeProductionPlannedMergeMappings) {
            String mergeWorkOrderId = mergeProductionPlannedMergeMapping.getMergeWorkOrderId();
            WorkOrderPO updateWorkOrder = workOrderMap.get(mergeWorkOrderId);
            if (null == updateWorkOrder) {
                log.warn("更新交期订单找不到对应制造订单: {}", mergeWorkOrderId);
                continue;
            }
            String fixed = Optional.ofNullable(updateWorkOrder.getFixed()).orElse(YesOrNoEnum.NO.getCode());
            // 已开始，已完工的workOrder不更新交期
            if (limitStatus.contains(updateWorkOrder.getPlanStatus()) || YesOrNoEnum.YES.getCode().equals(fixed)) {
                continue;
            }
            updateWorkOrder.setDueDate(demandTime);
            updateWorkOrder.setRemark(updateWorkOrder.getRemark() + "；更新交期订单");
            workOrderPOS.add(updateWorkOrder);
            log.info("交期更新订单id：{}", updateWorkOrder.getId());
            String parentId = updateWorkOrder.getParentId();
            if (StrUtil.isNotEmpty(parentId)) {
                WorkOrderPO parentWorkOrder = workOrderMap.get(parentId);
                if (null != parentWorkOrder) {
                    parentWorkOrder.setDueDate(demandTime);
                    parentWorkOrder.setRemark(parentWorkOrder.getRemark() + "；更新交期订单");
                    workOrderPOS.add(parentWorkOrder);
                    log.info("交期更新父订单id：{}", parentWorkOrder.getId());
                }
            }
        }
    }

    private void semiFinishedWorkOrder(String productId,
                                       Date dueDate,
                                       BigDecimal qty,
                                       String productionIntervalId,
                                       MpsAnalysisContext mpsAnalysisContext,
                                       RzzProductionIntervalOutput productionIntervalOutput,
                                       WorkOrderPO workOrderPO, List<WorkOrderPO> semiWorkOrderPOList) {
        Date operationDate = mpsAnalysisContext.getOperationDate();
        String demandOrderId = workOrderPO.getId();
        // productId为半品供应的id，创建半品的制造订单
        NewProductStockPointVO newProductStockPointVO = mpsAnalysisContext.getProductMap().get(productId);
        if (null == newProductStockPointVO) {
            log.warn("成品的半品发货计划物品对应信息不存在：{}", productId);
            return;
        }
        NewStockPointVO newStockPointVO = mpsAnalysisContext.getStockMap().get(newProductStockPointVO.getStockPointCode());
        RoutingVO routingVO = mpsAnalysisContext.getRoutingVOMap().get(newProductStockPointVO.getId());
        if (null == routingVO) {
            log.warn("成品的半品发货计划物品对应工艺路径信息不存在：{}", productId);
            return;
        }
        if (!mpsAnalysisContext.getWorkOrderMap().containsKey(productionIntervalId)) {
            String workOrderCode = getWorkOrderCode(mpsAnalysisContext.getRuleEncodingsMap());
            // 创建workOrder
            String workOrderId = UUIDUtil.getUUID();
            WorkOrderPO workOrderInsert = new WorkOrderPO();
            workOrderInsert.setId(workOrderId);
            workOrderInsert.setOrderNo(workOrderCode);
            workOrderInsert.setQuantity(qty);
            workOrderInsert.setDueDate(dueDate);
            workOrderInsert.setProductId(productId);
            workOrderInsert.setProductStockPointId(productId);
            workOrderInsert.setCreateTime(operationDate);
            workOrderInsert.setModifyTime(operationDate);
            workOrderInsert.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
            workOrderInsert.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
            workOrderInsert.setKitStatus(KitStatusEnum.UNKIT.getCode());
            workOrderInsert.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
            workOrderInsert.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            workOrderInsert.setCreateTime(operationDate);
            workOrderInsert.setModifyTime(operationDate);
            workOrderInsert.setEnabled(YesOrNoEnum.YES.getCode());
            workOrderInsert.setRoutingId(routingVO.getId());
            workOrderInsert.setRemark(productionIntervalId);
            workOrderInsert.setBomType(CHILD_BOM);
            workOrderInsert.setParentId(demandOrderId);
            workOrderInsert.setTopOrderId(demandOrderId);
            workOrderInsert.setDemandCategory(workOrderPO.getDemandCategory());
            workOrderInsert.setOrderType(workOrderPO.getOrderType());
            workOrderInsert.setEndingInventoryMinSafeDiff(workOrderPO.getEndingInventoryMinSafeDiff());
            workOrderInsert.setLatestEndTime(productionIntervalOutput.getLatestDemandTime());
            mpsAnalysisContext.getCreateWorkOrderList().add(workOrderInsert);
            semiWorkOrderPOList.add(workOrderInsert);
            mpsAnalysisContext.getWorkOrderOnResourceMap().put(workOrderInsert.getId(),
                    mpsAnalysisContext.getWorkOrderOnResourceMap().get(productionIntervalOutput.getStandardResourceId()));
            // 创建demand
            String customerOrderDemandCode = getCustomerOrderDemandCode(mpsAnalysisContext.getRuleEncodingsMap());
            String demandId = UUIDUtil.getUUID();
            DemandPO demandPO = new DemandPO();
            demandPO.setId(demandId);
            demandPO.setProductId(productId);
            demandPO.setProductStockPointId(productId);
            demandPO.setDemandCode(customerOrderDemandCode);
            demandPO.setDemandOrderId(demandOrderId);
            demandPO.setDemandType(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode());
            demandPO.setQuantity(qty);
            demandPO.setUnfulfilledQuantity(BigDecimal.ZERO);
            demandPO.setDemandTime(dueDate);
            demandPO.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
            demandPO.setFulfillmentStatus(FulfillmentStatusEnum.ALL_FULFILLED.getCode());
            demandPO.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
            demandPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            demandPO.setEnabled(YesOrNoEnum.YES.getCode());
            demandPO.setCreateTime(operationDate);
            demandPO.setModifyTime(operationDate);
            demandPO.setRemark(productionIntervalId);
            demandPO.setCountingUnitId(workOrderId);
            mpsAnalysisContext.getCreateDemandsList().add(demandPO);
            // 创建supply
            String supplyCode = getOrderCode(mpsAnalysisContext.getRuleEncodingsMap(), RuleEncodingsEnum.WORK_ORDER_SUPPLY.getDesc());
            String supplyId = UUIDUtil.getUUID();
            SupplyPO supplyPO = new SupplyPO();
            supplyPO.setId(supplyId);
            supplyPO.setProductId(productId);
            supplyPO.setProductStockPointId(productId);
            supplyPO.setSupplyCode(supplyCode);
            supplyPO.setSupplyOrderId(workOrderId);
            supplyPO.setSupplyType(SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode());
            supplyPO.setQuantity(qty);
            supplyPO.setUnfulfilledQuantity(BigDecimal.ZERO);
            supplyPO.setSupplyTime(dueDate);
            supplyPO.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
            supplyPO.setFulfillmentStatus(FulfillmentStatusEnum.ALL_FULFILLED.getCode());
            supplyPO.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
            supplyPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            supplyPO.setCreateTime(operationDate);
            supplyPO.setModifyTime(operationDate);
            supplyPO.setEnabled(YesOrNoEnum.YES.getCode());
            supplyPO.setRemark(productionIntervalId);
            mpsAnalysisContext.getCreateSupplyList().add(supplyPO);
            // 创建fulfillment
            String stockPointId = null == newStockPointVO ? null : newStockPointVO.getId();
            FulfillmentPO fulfillmentPO = new FulfillmentPO();
            fulfillmentPO.setId(UUIDUtil.getUUID());
            fulfillmentPO.setSupplyId(supplyId);
            fulfillmentPO.setDemandId(demandId);
            fulfillmentPO.setFulfillmentQuantity(qty);
            fulfillmentPO.setSupplyOrderId(workOrderId);
            fulfillmentPO.setDemandOrderId(demandOrderId);
            fulfillmentPO.setDemandType(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode());
            fulfillmentPO.setSupplyType(SupplyTypeEnum.WORK_ORDER_SUPPLY.getCode());
            fulfillmentPO.setDemandProductId(productId);
            fulfillmentPO.setDemandProductStockPointId(productId);
            fulfillmentPO.setSupplyProductId(productId);
            fulfillmentPO.setSupplyProductStockPointId(productId);
            fulfillmentPO.setDemandStockPointId(stockPointId);
            fulfillmentPO.setSupplyStockPointId(stockPointId);
            fulfillmentPO.setCreateTime(operationDate);
            fulfillmentPO.setModifyTime(operationDate);
            fulfillmentPO.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
            fulfillmentPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            fulfillmentPO.setEnabled(YesOrNoEnum.YES.getCode());
            fulfillmentPO.setRemark(productionIntervalId);
            mpsAnalysisContext.getCreateFulfillmentList().add(fulfillmentPO);
        } else {
            // 待修改的制造订单
            WorkOrderPO workOrderOld = mpsAnalysisContext.getWorkOrderMap().get(productionIntervalId);
            workOrderOld.setRoutingId(routingVO.getId());
            workOrderOld.setQuantity(qty);
            workOrderOld.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            workOrderOld.setModifyTime(operationDate);
            workOrderOld.setDueDate(dueDate);
            workOrderOld.setBomType(CHILD_BOM);
            workOrderOld.setDemandCategory(workOrderPO.getDemandCategory());
            workOrderOld.setOrderType(workOrderPO.getOrderType());
            workOrderOld.setEndingInventoryMinSafeDiff(workOrderPO.getEndingInventoryMinSafeDiff());
            workOrderOld.setRemark(productionIntervalId);
            // 修改的制造订单
            mpsAnalysisContext.getUpdateWorkOrderList().add(workOrderOld);
            // 修改半品制造订单对应的供需关系信息（数量，交期）
            List<FulfillmentPO> fulfillmentPOS = mpsAnalysisContext.getFulfillmentPOMap().get(workOrderOld.getId());
            if (CollectionUtils.isEmpty(fulfillmentPOS)) {
                return;
            }
            FulfillmentPO fulfillmentPO = fulfillmentPOS.get(0);
            fulfillmentPO.setFulfillmentQuantity(qty);
            fulfillmentPO.setModifyTime(operationDate);
            fulfillmentPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
            fulfillmentPO.setRemark(productionIntervalId);
            mpsAnalysisContext.getUpdateFulfillmentList().add(fulfillmentPO);

            String supplyId = fulfillmentPO.getSupplyId();
            String demandId = fulfillmentPO.getDemandId();
            if (mpsAnalysisContext.getSupplyPOMap().containsKey(supplyId)) {
                SupplyPO supplyPO = mpsAnalysisContext.getSupplyPOMap().get(supplyId);
                supplyPO.setQuantity(qty);
                supplyPO.setSupplyTime(dueDate);
                supplyPO.setModifyTime(operationDate);
                supplyPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                supplyPO.setRemark(productionIntervalId);
                mpsAnalysisContext.getUpdateSupplyList().add(supplyPO);
            }
            if (mpsAnalysisContext.getDemandMap().containsKey(demandId)) {
                DemandPO demandPO = mpsAnalysisContext.getDemandMap().get(demandId);
                demandPO.setQuantity(qty);
                demandPO.setDemandTime(dueDate);
                demandPO.setModifyTime(operationDate);
                demandPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                demandPO.setRemark(productionIntervalId);
                mpsAnalysisContext.getUpdateDemandsList().add(demandPO);
            }

        }
    }

    private String getDemandCategoryType(List<DeliveryPlanPublishedVO> deliverDemandList) {
        if (CollectionUtils.isNotEmpty(deliverDemandList)) {
            List<DeliveryPlanPublishedVO> demandTypeList = deliverDemandList.stream().filter(p -> StrUtil.isNotEmpty(p.getDemandCategory())
                            && p.getDemandCategory().equals(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(demandTypeList)) {
                return ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode();
            }
        }
        return null;
    }

    private int getEndingInventoryMinSafeDiff(List<DeliveryPlanPublishedVO> deliverDemandList, MpsAnalysisContext mpsAnalysisContext) {
        deliverDemandList.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));

        DeliveryPlanPublishedVO firstDemand = deliverDemandList.get(0);
        DeliveryPlanPublishedVO lastDemand = deliverDemandList.get(deliverDemandList.size() - 1);

        String oemCode = firstDemand.getOemCode();
        String productCode = firstDemand.getProductCode();
        Date demandTime = firstDemand.getDemandTime();
        Date lastDemandTime = lastDemand.getDemandTime();

        String key = StrUtil.join(STR_JOIN_VALUE, oemCode, productCode);

        List<InventoryShiftVO> inventoryShiftVOS = mpsAnalysisContext.getInventoryShiftMap().getOrDefault(key, ListUtil.empty())
                .stream()
                .filter(p -> p.getDeliveryDate().getTime() >= demandTime.getTime()
                        && p.getDeliveryDate().getTime() <= lastDemandTime.getTime()
                        && p.getEndingInventoryMinSafeDiff() != null)
                .collect(Collectors.toList());
        OptionalInt minEndingInventoryMinSafeDiff = inventoryShiftVOS.stream()
                .mapToInt(InventoryShiftVO::getEndingInventoryMinSafeDiff)
                .min();
        if (minEndingInventoryMinSafeDiff.isPresent()) {
            return minEndingInventoryMinSafeDiff.getAsInt();
        }
        return 0;
    }

    private WorkOrderPO buildWorkOrder(BigDecimal qty,
                                       List<String> demandIds,
                                       String productionIntervalId,
                                       RzzProductionIntervalOutput productionIntervalOutput,
                                       MpsAnalysisContext mpsAnalysisContext, Boolean whetherFinishProduct) {
        Date operationDate = mpsAnalysisContext.getOperationDate();
        // 半品需求，最后一道工序为瓶颈工序直接构建客户订单及供应关系
        List<RzzDemandOutput> workOrderDemand = mpsAnalysisContext.getRzzMpsAlgorithmOutput().getDemandOutputDataList().stream()
                .filter(d -> demandIds.contains(d.getSelfDemandId()))
                .sorted(Comparator.comparing(RzzDemandOutput::getDemandTime)).collect(Collectors.toList());
        List<String> deliverDemandId = workOrderDemand.stream().map(RzzDemandOutput::getDemandId).collect(Collectors.toList());
        String demandCategoryType = null;
        int endingInventoryMinSafeDiff = 0;
        if (CollectionUtils.isNotEmpty(deliverDemandId)) {
            List<DeliveryPlanPublishedVO> deliverDemandList = mpsAnalysisContext.getDeliveryPlanPublishedVOS().stream()
                    .filter(p -> deliverDemandId.contains(p.getId())).collect(Collectors.toList());
            // 判断需求类型
            if (CollectionUtils.isNotEmpty(deliverDemandList)) {
                demandCategoryType = getDemandCategoryType(deliverDemandList);
            }
            // 获取期末库存最小安全库存差值
            if (CollectionUtils.isNotEmpty(deliverDemandList)) {
                endingInventoryMinSafeDiff = getEndingInventoryMinSafeDiff(deliverDemandList, mpsAnalysisContext);
            }
        }
        // 合批后最早的需求订单
        RzzDemandOutput earliestDemand = workOrderDemand.get(0);
        String demandTime = earliestDemand.getDemandTime();
        // 如果存在原始需求时间，则使用原始需求时间，不存在则使用计算完提前期的时间
        String productStockPointId = earliestDemand.getProductStockPointId();
        NewProductStockPointVO newProductStockPointVO = mpsAnalysisContext.getProductMap().get(productStockPointId);
        if (null == newProductStockPointVO) {
            log.warn("成品发货计划物品对应信息不存在：{}", productStockPointId);
            return null;
        }
        NewStockPointVO newStockPointVO = mpsAnalysisContext.getStockMap().get(newProductStockPointVO.getStockPointCode());
        RoutingVO routingVO = mpsAnalysisContext.getRoutingVOMap().get(newProductStockPointVO.getId());
        if (null == routingVO) {
            log.warn("成品发货计划物品对应工艺路径信息不存在：{}", productStockPointId);
            return null;
        }
        Date dueDate = DateUtils.stringToDate(demandTime, DateUtils.COMMON_DATE_STR1);
        WorkOrderPO workOrder;
        if (whetherFinishProduct) {
            // 如果不包含该workOrder，则说明当前批次是新增的批次
            if (!mpsAnalysisContext.getWorkOrderMap().containsKey(productionIntervalId)) {
                String productId = newProductStockPointVO.getId();
                String workOrderId = UUIDUtil.getUUID();
                // 创建workOrder
                WorkOrderPO workOrderPOInsert = createWorkOrder(workOrderId, qty, dueDate, productId, operationDate,
                        newStockPointVO, mpsAnalysisContext, routingVO, productionIntervalId, productionIntervalOutput, CHAIN_BOM);
                workOrderPOInsert.setDemandCategory(demandCategoryType);
                workOrderPOInsert.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                workOrderPOInsert.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                mpsAnalysisContext.getCreateWorkOrderList().add(workOrderPOInsert);
                workOrder = workOrderPOInsert;
                mpsAnalysisContext.getWorkOrderOnResourceMap().put(workOrderPOInsert.getId(),
                        mpsAnalysisContext.getPhysicalResourceCodeOnIdMap().get(productionIntervalOutput.getStandardResourceId()));
            } else {
                WorkOrderPO workOrderOld = mpsAnalysisContext.getWorkOrderMap().get(productionIntervalId);
                workOrderOld.setRoutingId(routingVO.getId());
                workOrderOld.setQuantity(qty);
                workOrderOld.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                workOrderOld.setModifyTime(operationDate);
                workOrderOld.setDueDate(dueDate);
                workOrderOld.setRemark(productionIntervalId);
                workOrderOld.setDemandCategory(demandCategoryType);
                workOrderOld.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                workOrderOld.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                // 修改的制造订单
                mpsAnalysisContext.getUpdateWorkOrderList().add(workOrderOld);

                ProductionIntervalPO productionIntervalPO = mpsAnalysisContext.getProductionIntervalMap().get(workOrderOld.getId());
                if (null != productionIntervalPO) {
                    setOther(productionIntervalPO, mpsAnalysisContext, productionIntervalOutput, operationDate);
                    productionIntervalPO.setQty(qty);
                    productionIntervalPO.setWhetherFixed(productionIntervalOutput.getFixed());
                    // 修改的计划批量
                    mpsAnalysisContext.getUpdateProductionIntervalList().add(productionIntervalPO);
                }
                workOrder = workOrderOld;
            }
        } else {
            if (!mpsAnalysisContext.getWorkOrderMap().containsKey(productionIntervalId)) {
                String productId = newProductStockPointVO.getId();
                String workOrderId = UUIDUtil.getUUID();
                // 创建半品对应的成品workOrder
                WorkOrderPO workOrderPOInsert = createWorkOrder(workOrderId, qty, dueDate, productId, operationDate,
                        newStockPointVO, mpsAnalysisContext, routingVO, productionIntervalId, productionIntervalOutput, TREE_BOM);
                workOrderPOInsert.setDemandCategory(demandCategoryType);
                workOrderPOInsert.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                workOrderPOInsert.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                workOrder = workOrderPOInsert;
            } else {
                // 通过供需关系查找当前半品批次供给哪个制造订单，进行制造订单的修改操作，如果找不到，则创建新的制造订单
                WorkOrderPO workOrderOld = mpsAnalysisContext.getWorkOrderMap().get(productionIntervalId);
                String workOrderId = workOrderOld.getId();
                if (!mpsAnalysisContext.getFulfillmentPOMap().containsKey(workOrderId)) {
                    return null;
                }
                // 通过workOrderId查询分配关系，通过分配关系找到demand信息，再通过demand找到需求订单也就是成品订单，修改成品订单交期及时间
                List<FulfillmentPO> fulfillmentPOList = mpsAnalysisContext.getFulfillmentPOMap().get(workOrderId);
                String demandId = fulfillmentPOList.get(0).getDemandId();
                DemandPO demandPO = mpsAnalysisContext.getDemandMap().get(demandId);
                if (null == demandPO) {
                    return null;
                }
                // 当前半品供应的需求订单id
                String demandOrderId = demandPO.getDemandOrderId();
                // 待修改的制造订单
                WorkOrderPO fnishedProductWorkOrderPO = mpsAnalysisContext.getWorkOrderMap().get(demandOrderId);
                fnishedProductWorkOrderPO.setDueDate(dueDate);
                fnishedProductWorkOrderPO.setQuantity(qty);
                fnishedProductWorkOrderPO.setModifyTime(operationDate);
                fnishedProductWorkOrderPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
                fnishedProductWorkOrderPO.setDemandCategory(demandCategoryType);
                fnishedProductWorkOrderPO.setOrderType(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(demandCategoryType) ? "SP" : "LC");
                fnishedProductWorkOrderPO.setEndingInventoryMinSafeDiff(endingInventoryMinSafeDiff);
                mpsAnalysisContext.getUpdateWorkOrderList().add(fnishedProductWorkOrderPO);
                workOrder = fnishedProductWorkOrderPO;
            }
        }
        return workOrder;
    }

    private WorkOrderPO createWorkOrder(String workOrderId, BigDecimal qty, Date dueDate, String productId,
                                        Date operationDate, NewStockPointVO newStockPointVO,
                                        MpsAnalysisContext mpsAnalysisContext, RoutingVO routingVO, String productionIntervalId,
                                        RzzProductionIntervalOutput productionIntervalOutput, String bomType) {
        String workOrderCode = getWorkOrderCode(mpsAnalysisContext.getRuleEncodingsMap());
        WorkOrderPO workOrderPOInsert = new WorkOrderPO();
        workOrderPOInsert.setId(workOrderId);
        workOrderPOInsert.setOrderNo(workOrderCode);
        workOrderPOInsert.setQuantity(qty);
        workOrderPOInsert.setDueDate(dueDate);
        workOrderPOInsert.setProductId(productId);
        workOrderPOInsert.setProductStockPointId(productId);
        workOrderPOInsert.setCreateTime(operationDate);
        workOrderPOInsert.setModifyTime(operationDate);
        workOrderPOInsert.setStockPointId(null == newStockPointVO ? null : newStockPointVO.getId());
        workOrderPOInsert.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
        workOrderPOInsert.setKitStatus(KitStatusEnum.UNKIT.getCode());
        workOrderPOInsert.setCreator(mpsAnalysisContext.getAlgorithmLog().getCreator());
        workOrderPOInsert.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
        workOrderPOInsert.setCreateTime(operationDate);
        workOrderPOInsert.setModifyTime(operationDate);
        workOrderPOInsert.setEnabled(YesOrNoEnum.YES.getCode());
        workOrderPOInsert.setRoutingId(routingVO.getId());
        workOrderPOInsert.setRemark("日志id：" + mpsAnalysisContext.getAlgorithmLog().getId() + ";" + productionIntervalId);
        workOrderPOInsert.setBomType(bomType);
        workOrderPOInsert.setLatestEndTime(productionIntervalOutput.getLatestDemandTime());
        return workOrderPOInsert;
    }


    private void setOther(ProductionIntervalPO productionIntervalPO, MpsAnalysisContext mpsAnalysisContext,
                          RzzProductionIntervalOutput productionIntervalOutput, Date operationDate) {
        if (null == productionIntervalPO) {
            return;
        }
        productionIntervalPO.setStandardResourceId(productionIntervalOutput.getStandardResourceId());
        productionIntervalPO.setStartPeriod(productionIntervalOutput.getStartPeriod());
        productionIntervalPO.setEndPeriod(productionIntervalOutput.getEndPeriod());
        productionIntervalPO.setModifyTime(operationDate);
        productionIntervalPO.setWhetherFixed(productionIntervalOutput.getFixed());
        productionIntervalPO.setModifier(mpsAnalysisContext.getAlgorithmLog().getCreator());
    }


    private void databaseOperation(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        Date dateTime = new Date();
        List<WorkOrderPO> createWorkOrderList = mpsAnalysisContext.getCreateWorkOrderList();
        List<DemandPO> createDemandsList = mpsAnalysisContext.getCreateDemandsList();
        List<SupplyPO> createSupplyList = mpsAnalysisContext.getCreateSupplyList();
        List<ProductionIntervalPO> createProductionIntervalList = mpsAnalysisContext.getCreateProductionIntervalList();
        List<FulfillmentPO> createFulfillmentList = mpsAnalysisContext.getCreateFulfillmentList();
        Map<String, RuleEncodingsVO> ruleEncodingsMap = mpsAnalysisContext.getRuleEncodingsMap();

        List<WorkOrderPO> updateWorkOrderList = mpsAnalysisContext.getUpdateWorkOrderList();
        List<DemandPO> updateDemandsList = mpsAnalysisContext.getUpdateDemandsList();
        List<SupplyPO> updateSupplyList = mpsAnalysisContext.getUpdateSupplyList();
        List<FulfillmentPO> updateFulfillmentList = mpsAnalysisContext.getUpdateFulfillmentList();
        List<ProductionIntervalPO> updateProductionIntervalList = mpsAnalysisContext.getUpdateProductionIntervalList();
        List<String> waitingSyncWorkOrder = new ArrayList<>();
        log.info("创建workOrder数量：{}", createWorkOrderList.size());
        log.info("修改workOrder数量：{}", updateWorkOrderList.size());
        log.info("创建demand数量：{}", createDemandsList.size());
        log.info("修改demand数量：{}", updateDemandsList.size());
        log.info("创建supply数量：{}", createSupplyList.size());
        log.info("修改supply数量：{}", updateSupplyList.size());
        log.info("创建fulfillment数量：{}", createFulfillmentList.size());
        log.info("修改fulfillment数量：{}", updateFulfillmentList.size());
        log.info("创建ProductionInterval数量：{}", createProductionIntervalList.size());
        log.info("修改ProductionInterval数量：{}", updateProductionIntervalList.size());

        if (CollectionUtils.isNotEmpty(createWorkOrderList)) {
            waitingSyncWorkOrder.addAll(createWorkOrderList.stream().map(WorkOrderPO::getId).collect(Collectors.toList()));
            workOrderDao.insertBatch(createWorkOrderList);
            mpsAnalysisContext.getScheduleInfoMap().put("createWorkOrderList", createWorkOrderList);
        }
        if (CollectionUtils.isNotEmpty(createDemandsList)) {
            demandDao.insertBatch(createDemandsList);
        }
        if (CollectionUtils.isNotEmpty(createSupplyList)) {
            supplyDao.insertBatch(createSupplyList);
        }
        if (CollectionUtils.isNotEmpty(createFulfillmentList)) {
            fulfillmentDao.insertBatch(createFulfillmentList);
        }
        if (CollectionUtils.isNotEmpty(createProductionIntervalList)) {
            productionIntervalDao.insertBatch(createProductionIntervalList);
        }
        if (CollectionUtils.isNotEmpty(updateWorkOrderList)) {
            waitingSyncWorkOrder.addAll(updateWorkOrderList.stream().map(WorkOrderPO::getId).collect(Collectors.toList()));
            workOrderDao.updateBatch(updateWorkOrderList);
        }
        if (CollectionUtils.isNotEmpty(updateDemandsList)) {
            demandDao.updateBatch(updateDemandsList);
        }
        if (CollectionUtils.isNotEmpty(updateSupplyList)) {
            supplyDao.updateBatch(updateSupplyList);
        }
        if (CollectionUtils.isNotEmpty(updateFulfillmentList)) {
            fulfillmentDao.updateBatch(updateFulfillmentList);
        }
        if (CollectionUtils.isNotEmpty(updateProductionIntervalList)) {
            productionIntervalDao.updateBatch(updateProductionIntervalList);
        }
        mdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc()));
        mdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_DEMAND.getDesc()));
        mdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_SUPPLY.getDesc()));
        mpsAlgorithmOutput.getWorkOrderIds().addAll(waitingSyncWorkOrder);
        log.info("待二次同步制造订单数量：{}", waitingSyncWorkOrder.size());
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("MPS中间结果数据库操作完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime, new Date()));
    }


    protected String getWorkOrderCode(Map<String, RuleEncodingsVO> ruleEncodingsMap) {
        RuleEncodingsVO prsRulRuleEncodingsVO = ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc());
        String serialNumber = RuleEncodingsUtils.getSerialNumber(prsRulRuleEncodingsVO);
        String code = RuleEncodingsUtils.getCode(prsRulRuleEncodingsVO, null, serialNumber);
        prsRulRuleEncodingsVO.setSerialNumberMaxValue(serialNumber);
        ruleEncodingsMap.put(RuleEncodingsEnum.WORK_ORDER_NO.getDesc(), prsRulRuleEncodingsVO);
        return code;
    }

    private String getOrderCode(Map<String, RuleEncodingsVO> ruleEncodingsMap, String code) {
        RuleEncodingsVO customerOrderDemandRuleEncodingsVO = ruleEncodingsMap.get(code);
        String serialNumberCus = RuleEncodingsUtils.getSerialNumber(customerOrderDemandRuleEncodingsVO);
        String codeCus = RuleEncodingsUtils.getCode(customerOrderDemandRuleEncodingsVO, null, serialNumberCus);
        customerOrderDemandRuleEncodingsVO.setSerialNumberMaxValue(serialNumberCus);
        ruleEncodingsMap.put(code, customerOrderDemandRuleEncodingsVO);
        return codeCus;
    }


    private String getCustomerOrderDemandCode(Map<String, RuleEncodingsVO> ruleEncodingsMap) {
        RuleEncodingsVO customerOrderDemandRuleEncodingsVO = ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_DEMAND.getDesc());
        String serialNumberCus = RuleEncodingsUtils.getSerialNumber(customerOrderDemandRuleEncodingsVO);
        String codeCus = RuleEncodingsUtils.getCode(customerOrderDemandRuleEncodingsVO, null, serialNumberCus);
        customerOrderDemandRuleEncodingsVO.setSerialNumberMaxValue(serialNumberCus);
        ruleEncodingsMap.put(RuleEncodingsEnum.WORK_ORDER_DEMAND.getDesc(), customerOrderDemandRuleEncodingsVO);
        return codeCus;
    }


    /**
     * 1.取消（修改+待删除）的已计划制造订单
     * 2.删除被合批后的制造订单
     *
     * @param mpsAnalysisContext context
     */
    protected abstract void cancelPlan(MpsAnalysisContext mpsAnalysisContext);


}
