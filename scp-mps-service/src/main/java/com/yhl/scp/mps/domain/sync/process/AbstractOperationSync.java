package com.yhl.scp.mps.domain.sync.process;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.mds.basic.enums.FrozenStatusEnum;
import com.yhl.scp.mds.basic.routing.enums.ScrapStrategyEnum;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepDO;
import com.yhl.scp.mds.routing.domain.PreNextRelationRelation;
import com.yhl.scp.mps.domain.sync.model.SyncContext;
import com.yhl.scp.mps.sync.enmus.SynchronizeFailMsgEnum;
import com.yhl.scp.sds.basic.enums.ActionEnum;
import com.yhl.scp.sds.basic.enums.KitStatusEnum;
import com.yhl.scp.sds.basic.enums.SynchronizeStatusEnum;
import com.yhl.scp.sds.basic.order.enums.OrderTypeEnum;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.extension.order.domain.entity.OperationDO;
import com.yhl.scp.sds.extension.order.domain.entity.WorkOrderDO;
import com.yhl.scp.sds.extension.order.dto.OperationDTO;
import com.yhl.scp.sds.extension.order.dto.OperationExtendDTO;
import com.yhl.scp.sds.order.convertor.OperationConvertor;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertor;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>AbstractOperationSync</code>
 * <p>
 * 工序同步
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-11 14:37:59
 */
public abstract class AbstractOperationSync extends AbstractWorkOrderRoutingSync{

    @Resource
    private OperationService operationService;

    @Resource
    private OperationTaskService operationTaskService;

    @Resource
    private OperationResourceService operationResourceService;

    @Resource
    private OperationInputService operationInputService;

    @Resource
    private OperationOutputService operationOutputService;

    @Resource
    private OperationExtendService operationExtendService;

    @Resource
    private OperationDao operationDao;

    private static final BigDecimal FIVE = new BigDecimal("5");
    private static final BigDecimal HUNDRED = new BigDecimal("100");

    @Override
    protected void syncOperation(SyncContext syncContext) {
        // 工序同步
        Map<ActionEnum, List<OperationDO>> operationMap = generateOperation(syncContext);
        //处理工序(进行数据库操作)
        List<OperationDO> operationDOS = dealOperations(operationMap);
        //制造订单同步状态更新
        doBatchUpdateForStatus(WorkOrderConvertor.INSTANCE.do2Dtos(syncContext.getSyncWorkOrderList()));
        syncContext.setOperationDOS(operationDOS);
    }

    public BigDecimal roundToNearestFive(BigDecimal originalQuantity) {
        // 如果数量小于100，直接返回原值
        if (originalQuantity.compareTo(HUNDRED) < 0) {
            return originalQuantity;
        }

        // 计算除以5的余数
        BigDecimal remainder = originalQuantity.remainder(FIVE);

        // 如果余数为0，已经是5的倍数，直接返回
        if (remainder.compareTo(BigDecimal.ZERO) == 0) {
            return originalQuantity;
        }

        // 否则向上取整到最近的5的倍数
        return originalQuantity.add(FIVE.subtract(remainder));
    }

    private Map<ActionEnum, List<OperationDO>> generateOperation(SyncContext syncContext){
        List<OperationDO> insertOperations = new ArrayList<>();
        List<OperationDO> updateOperations = new ArrayList<>();
        List<OperationDO> deleteOperations = new ArrayList<>();
        // 获取所有需要同步的制造订单
        List<WorkOrderDO> needElectRoutingWorkOrderList = syncContext.getSyncWorkOrderList();
        //根据制造订单ID查询工序数据
        List<OperationDO> operationDOList = needElectRoutingWorkOrderList.stream().filter(k->CollectionUtils.isNotEmpty(k.getOperationDOList()))
                .map(WorkOrderDO::getOperationDOList).flatMap(List::stream).collect(Collectors.toList());
        List<OperationDO> operations = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(operationDOList)){
            operations.addAll(operationDOList);
        }
        Map<String, List<OperationDO>> operationGroupOfWorkOrderId = operations.stream().collect(Collectors.groupingBy(OperationDO::getOrderId));

        Map<String, BigDecimal> routingStepOfQtyMap = syncContext.getRoutingStepOfQtyMap();

        Map<String, BigDecimal> id2workOrderQuantityMap = getWorkOrderQuantity(needElectRoutingWorkOrderList);

        for (WorkOrderDO workOrderDO : needElectRoutingWorkOrderList) {
            boolean whetherSynchronizationFail = false;
            List<OperationDO> operationsOfWorkOrder = operationGroupOfWorkOrderId.get(workOrderDO.getId());
            RoutingDO routingDO = workOrderDO.getRoutingDO();
            if (null == routingDO) {
                continue;
            }
            // 获取工艺路径步骤信息
            List<RoutingStepDO> routingStepDOList = workOrderDO.getRoutingDO().getRoutingStepDOList();
            // 工艺路径步骤按照顺序号倒序排列
            List<RoutingStepDO> newSortedRoutingStepList = routingStepDOList.stream().sorted(Comparator.comparing(RoutingStepDO::getSequenceNo).reversed()).collect(Collectors.toList());
            //获取本次路径步骤中最大的顺序号
            Integer maxRoutingStepSequenceNo = newSortedRoutingStepList.get(0).getSequenceNo();
            //获取已同步工序的最大顺序号
            Integer maxOperationSequenceNo = null;
            if (CollectionUtils.isNotEmpty(operationsOfWorkOrder)) {
                maxOperationSequenceNo = operationsOfWorkOrder.stream().max(Comparator.comparing(OperationDO::getRoutingStepSequenceNo)).get().getRoutingStepSequenceNo();
            }
            Map<Integer, BigDecimal> sequenceAndQtyMaps = computeOperationQty(id2workOrderQuantityMap.get(workOrderDO.getId()), newSortedRoutingStepList);
            if (CollectionUtils.isEmpty(operationsOfWorkOrder)) {
                //没有同步过工序，生成新的工序
                for (RoutingStepDO routingStepDO : newSortedRoutingStepList) {
                    OperationDO operationDO = createOperationDTO(workOrderDO, routingStepDO, sequenceAndQtyMaps, routingStepOfQtyMap);
                    String standardStepId = operationDO.getStandardStepId();
                    if(Boolean.TRUE.equals(syncContext.getRoundToNearestFiveFlag())
                            && syncContext.getStandardStepIdMap().containsKey(standardStepId)){
                        BigDecimal quantity = operationDO.getQuantity();
                        operationDO.setQuantity(roundToNearestFive(quantity));
                    }
                    insertOperations.add(operationDO);
                }
            } else {
                //二次同步，同步过工序
                List<Integer> operationSequenceNoList = operationsOfWorkOrder.stream().map(OperationDO::getRoutingStepSequenceNo).collect(Collectors.toList());
                List<Integer> routingStepSequenceNoList = newSortedRoutingStepList.stream().map(RoutingStepDO::getSequenceNo).collect(Collectors.toList());
                Map<Integer, OperationDO> newOperationOfSequenceNrMap = operationsOfWorkOrder.stream().collect(Collectors.toMap(OperationDO::getRoutingStepSequenceNo, Function.identity(), (k1, k2) -> k2));
                for (int i = 0; i < newSortedRoutingStepList.size(); i++) {
                    RoutingStepDO routingStepDO = newSortedRoutingStepList.get(i);
                    OperationDO operationDO = newOperationOfSequenceNrMap.get(routingStepDO.getSequenceNo());
                    if (operationDO == null) {
                        // 1.对于某个RoutingStep,若没有相同顺序号的Operation，则需要新建Operation
                        operationDO = createOperationDTO(workOrderDO, routingStepDO, sequenceAndQtyMaps, routingStepOfQtyMap);
                        insertOperations.add(operationDO);
                    } else {

                        //已开始，已完工不允许修改
                        boolean flag = keepStatus.contains(operationDO.getPlanStatus());

                        // 2.对于某个RoutingStep,若存在相同顺序号的Operation
                        if (operationDO.getRoutingStepSequenceNo().equals(routingStepDO.getSequenceNo())) {
                            //上次同步是末道工序，这次同步非末道工序，无法更新时则 同步失败
                            if (Objects.equals(maxOperationSequenceNo, operationDO.getRoutingStepSequenceNo()) && null != maxOperationSequenceNo && maxOperationSequenceNo < maxRoutingStepSequenceNo && flag) {
                                whetherSynchronizationFail = true;
                                workOrderDO.setSyncStatus(SynchronizeStatusEnum.FAIL_SYNC.getCode());
                                workOrderDO.setSyncFailureReason(SynchronizeFailMsgEnum.STEP_NOT_UPDATE.getCode());
                                //同步失败，删除已经维护好的数据
                                removeOperationListByOrderId(insertOperations, updateOperations, deleteOperations, workOrderDO.getId());
                                break;
                            }
                            // b)若OperationCode(routingStepCode)相同，则需要更新Operation
                            if (!flag) {
                                // 允许修改
                                operationDO.setRoutingStepId(routingStepDO.getId());
                                operationDO.setStandardStepId(routingStepDO.getStandardStepId());
                                operationDO.setQuantity(sequenceAndQtyMaps.get(routingStepDO.getSequenceNo()));
                                operationDO.setProductId(workOrderDO.getProductId());
                                operationDO.setConnectionTask(routingStepDO.getConnectionTask());
                                operationDO.setConnectionType(routingStepDO.getConnectionType());
                                operationDO.setMaxConnectionDuration(routingStepDO.getMaxConnectionDuration());
                                operationDO.setMinConnectionDuration(routingStepDO.getMinConnectionDuration());
                                setQtyByLimit(routingStepDO, routingStepOfQtyMap, operationDO);
                                updateOperations.add(operationDO);
                            }
                        } else {
                            // a)若OperationCode(routingStepCode)不同，则需要删除Operation
                            // 若根据状态控制，无法删除，则约束显示缺乏对应的RoutingStep。
                            //上次同步是末道工序，这次同步非末道工序，无法更新时则 同步失败
                            if (Objects.equals(maxOperationSequenceNo, operationDO.getRoutingStepSequenceNo()) && null != maxOperationSequenceNo && maxOperationSequenceNo < maxRoutingStepSequenceNo && flag) {
                                whetherSynchronizationFail = true;
                                workOrderDO.setSyncStatus(SynchronizeStatusEnum.FAIL_SYNC.getCode());
                                workOrderDO.setSyncFailureReason(SynchronizeFailMsgEnum.STEP_NOT_DELETE.getCode());
                                //同步失败，删除已经维护好的数据
                                removeOperationListByOrderId(insertOperations, updateOperations, deleteOperations, workOrderDO.getId());
                                break;
                            }
                            // 允许删除
                            deleteOperations.add(operationDO);
                            // 新建operationDTO
                            OperationDO createOperationDO = createOperationDTO(workOrderDO, routingStepDO, sequenceAndQtyMaps, routingStepOfQtyMap);
                            insertOperations.add(createOperationDO);
                        }
                    }
                }

                //工序顺序号对比步骤顺序号，删除多余的工序
                operationSequenceNoList.removeAll(routingStepSequenceNoList);
                for (Integer operationSequenceNo : operationSequenceNoList) {
                    OperationDO operationDO = newOperationOfSequenceNrMap.get(operationSequenceNo);

                    //已开始，已完工不允许修改
                    boolean flag = keepStatus.contains(operationDO.getPlanStatus());
                    if (!flag) {
                        //不允许删除则直接同步失败
                        whetherSynchronizationFail = true;
                        workOrderDO.setSyncStatus(SynchronizeStatusEnum.FAIL_SYNC.getCode());
                        workOrderDO.setSyncFailureReason(SynchronizeFailMsgEnum.STEP_NOT_DELETE.getCode());
                        //同步失败，删除已经维护好的数据
                        removeOperationListByOrderId(insertOperations, updateOperations, deleteOperations, workOrderDO.getId());
                        break;
                    }
                    // 允许删除
                    deleteOperations.add(operationDO);
                }
            }
            workOrderDO.setRoutingId(routingDO.getId());
            //同步成功
            if (!whetherSynchronizationFail) {
                workOrderDO.setSyncStatus(SynchronizeStatusEnum.SUCCESS_SYNC.getCode());
                workOrderDO.setSyncFailureReason(null);
            }
        }

        Map<ActionEnum, List<OperationDO>> resultMap = new HashMap<>();
        resultMap.put(ActionEnum.INSERT, insertOperations);
        resultMap.put(ActionEnum.UPDATE, updateOperations);
        resultMap.put(ActionEnum.DELETE, deleteOperations);
        return resultMap;
    }

    private List<OperationDO> dealOperations(Map<ActionEnum, List<OperationDO>> operationMap){
        List<OperationDO> operationDTOS = new ArrayList<>();
        //新增
        List<OperationDO> insertOperations = operationMap.get(ActionEnum.INSERT);
        if (CollectionUtils.isNotEmpty(insertOperations)) {
            List<OperationDTO> insertOperationDTOS = OperationConvertor.INSTANCE.do2Dtos(insertOperations);
            operationService.doCreateBatch(insertOperationDTOS);
            //新增扩展信息
            doOperationExtendCreateBatch(insertOperationDTOS);
            operationDTOS.addAll(insertOperations);
        }
        //修改
        List<OperationDO> updateOperations = operationMap.get(ActionEnum.UPDATE);
        if (CollectionUtils.isNotEmpty(updateOperations)) {
            List<OperationDTO> updateOperationDTOS = OperationConvertor.INSTANCE.do2Dtos(updateOperations);
            updateOperationDTOS.forEach(t->t.setRemark("制造订单同步更新工序"));
            operationService.doUpdateBatch(updateOperationDTOS);
            //更新扩展信息
            doOperationExtendUpdateBatch(updateOperationDTOS);
            operationDTOS.addAll(updateOperations);
        }
        //删除
        List<OperationDO> deleteOperations = operationMap.get(ActionEnum.DELETE);
        if (CollectionUtils.isNotEmpty(deleteOperations)) {
            List<String> operationIds = deleteOperations.stream().map(OperationDO::getId).collect(Collectors.toList());
            doDeleteOperationByIds(operationIds);
        }
        return operationDTOS;
    }


    /**
     * 计算工序数量
     * @param mainProductOutQty
     * @param routingStepDOList
     * @return
     */
    public static Map<Integer, BigDecimal> computeOperationQty(BigDecimal mainProductOutQty, List<RoutingStepDO> routingStepDOList) {
        Map<Integer, BigDecimal> sequenceAndQtyMaps = new HashMap<>(16);
        Map<Integer, RoutingStepDO> routingStepSequenceNrMaps = routingStepDOList
                .stream().collect(Collectors.toMap(RoutingStepDO::getSequenceNo, v -> v));
        //工艺路径步骤按照顺序号倒序排列
        List<RoutingStepDO> sortedRoutingStepList = routingStepDOList.stream().sorted(Comparator.comparing(RoutingStepDO::getSequenceNo).reversed()).collect(Collectors.toList());
        //获取前后顺序关系
        List<PreNextRelationRelation> preNextRelationRelations = getPreNextSequenceRelation(sortedRoutingStepList);
        //最后一道工序
        RoutingStepDO routingStepDO = sortedRoutingStepList.get(0);
        //其中“单位输出量”、“成品率”、“固定报废数”来源于对应工艺步骤输出
        //获取对应工艺路径步骤输出
        //筛选主产物输出
        BigDecimal yield = routingStepDO.getYield() == null ? BigDecimal.ONE : routingStepDO.getYield();
        BigDecimal operationQty = mainProductOutQty.divide(yield, 6);
        sequenceAndQtyMaps.put(routingStepDO.getSequenceNo(), operationQty);
        calculatePreOperationQtyByNextOperation(routingStepDO.getSequenceNo(), preNextRelationRelations, sequenceAndQtyMaps, routingStepSequenceNrMaps);
        sequenceAndQtyMaps = sequenceAndQtyMaps.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().setScale(0, RoundingMode.CEILING)
                ));
        return sequenceAndQtyMaps;
    }

    private  static List<PreNextRelationRelation> getPreNextSequenceRelation(List<RoutingStepDO> routingSteps) {
        if (org.springframework.util.CollectionUtils.isEmpty(routingSteps)) {
            return new ArrayList<>();
        }
        Set<PreNextRelationRelation> resultList = new HashSet<>();
        // 按工序顺序号从小到大排列
        List<RoutingStepDO> ascSequenceRoutingSteps = routingSteps.stream().sorted(Comparator.comparing(RoutingStepDO::getSequenceNo)).collect(Collectors.toList());
        // Map kay:工序顺序号 value: 前工序号集合
        Map<Integer, List<Integer>> routingStepSequenceToPreSequenceListMaps = ascSequenceRoutingSteps.stream().filter(k -> StringUtils.isNotBlank(k.getPreRoutingStepSequenceNo())).collect(Collectors.toMap(RoutingStepDO::getSequenceNo, v -> Arrays.stream(v.getPreRoutingStepSequenceNo().split(";")).map(Integer::parseInt).collect(Collectors.toList())));
        for (int i = 0; i < ascSequenceRoutingSteps.size(); i++) {
            RoutingStepDO routingStep = ascSequenceRoutingSteps.get(i);
            Integer routingStepSequenceNr = routingStep.getSequenceNo();
            // 获取当前工序的后顺序号
            String nextRoutingStepSequenceNr = routingStep.getNextRoutingStepSequenceNo();
            if (StringUtils.isNotBlank(nextRoutingStepSequenceNr)) {
                // 如果后顺序号不为空
                String[] splits = nextRoutingStepSequenceNr.split(";");
                for (String split : splits) {
                    Integer nextSequence = Integer.parseInt(split);
                    PreNextRelationRelation preNextRelationRelation = new PreNextRelationRelation();
                    preNextRelationRelation.setPreSequence(routingStep.getSequenceNo());
                    preNextRelationRelation.setNextSequence(nextSequence);
                    resultList.add(preNextRelationRelation);
                }
            } else {
                // 如果后顺序号为空
                // 判断当前工序是否作为其他工序的前顺序号
                List<Integer> preSequenceContainsCurrentSequences = new ArrayList<>();
                routingStepSequenceToPreSequenceListMaps.forEach((k, v) -> {
                    if (v.contains(routingStepSequenceNr)) {
                        preSequenceContainsCurrentSequences.add(k);
                    }
                });
                if (!org.springframework.util.CollectionUtils.isEmpty(preSequenceContainsCurrentSequences)) {
                    // 找到包含前工序中包含此工序的工艺路径
                    for (Integer preSequenceContainsCurrentSequence : preSequenceContainsCurrentSequences) {
                        PreNextRelationRelation preNextRelationRelation = new PreNextRelationRelation();
                        preNextRelationRelation.setPreSequence(preSequenceContainsCurrentSequence);
                        preNextRelationRelation.setNextSequence(routingStepSequenceNr);
                        resultList.add(preNextRelationRelation);
                    }
                } else {
                    // 如果当前工序不是其他工序指定的前顺序号，那么默认将第一个比此顺序号大的顺序号作为下一道工序
                    if ((i + 1) < ascSequenceRoutingSteps.size()) {
                        // 如果存在第一个顺序号比当前顺序号大的工序，判断是否是当前工序的下一道工序
                        RoutingStepDO nextSequenceRoutingStep = ascSequenceRoutingSteps.get(i + 1);
                        Integer biggerSequenceNr = nextSequenceRoutingStep.getSequenceNo();
                        PreNextRelationRelation preNextRelationRelation = new PreNextRelationRelation();
                        preNextRelationRelation.setPreSequence(routingStepSequenceNr);
                        preNextRelationRelation.setNextSequence(biggerSequenceNr);
                        resultList.add(preNextRelationRelation);
                    }
                }
            }
        }
        return new ArrayList<>(resultList);
    }

    /**
     * 递归计算工序链上相关数量
     *
     * @param nextSequence
     * @param preNextRelationRelations
     * @param sequenceAndQtyMaps
     * @param routingStepSequenceNrMaps
     */
    private  static void calculatePreOperationQtyByNextOperation(int nextSequence, List<PreNextRelationRelation> preNextRelationRelations, Map<Integer, BigDecimal> sequenceAndQtyMaps, Map<Integer, RoutingStepDO> routingStepSequenceNrMaps) {
        List<PreNextRelationRelation> preSequences = preNextRelationRelations.stream().filter(k -> k.getNextSequence() != null && k.getNextSequence() == nextSequence).collect(Collectors.toList());
        //如果前工序或者前工序就是自己
        if (CollectionUtils.isEmpty(preSequences)) {
            return;
        } else {
            for (int i = 0; i < preSequences.size(); i++) {
                Integer preSequence = preSequences.get(i).getPreSequence();
                //计算前工序数量
                //计算前工序的数量
                //（前）工序的数量 = （后）工序数量*后工序与前工序数量比 /成品率+固定报废数
                //“与前工序数量比”来源于后工序对应工艺步骤，“成品率”、“固定报废数”来源于前工序对应工艺步骤
                RoutingStepDO nextRoutingStep = routingStepSequenceNrMaps.get(nextSequence);
                if (nextRoutingStep == null) {
                    throw new BusinessException("未找到对应后工序顺序号的工序，后工序顺序号为：" + nextSequence);
                }
                RoutingStepDO preRoutingStep = routingStepSequenceNrMaps.get(preSequence);
                if (preRoutingStep == null) {
                    throw new BusinessException("未找到对应前工序顺序号的工序，前工序顺序号为：" + preSequence);
                }
                BigDecimal nextOperationQty = sequenceAndQtyMaps.get(nextSequence);
                BigDecimal preOperationQty = calculatePreOperationQty(nextOperationQty, preRoutingStep, nextRoutingStep);
                sequenceAndQtyMaps.put(preSequence, preOperationQty);
                calculatePreOperationQtyByNextOperation(preSequence, preNextRelationRelations, sequenceAndQtyMaps, routingStepSequenceNrMaps);
                List<PreNextRelationRelation> nextSequences = preNextRelationRelations.stream().filter(k -> k.getPreSequence() != null && k.getPreSequence() == preSequence).collect(Collectors.toList());
                if (nextSequences.size() > 1) {
                    //如果当前工序的前工序的后工序不止有自己
                    List<Integer> otherNextSequences = nextSequences.stream().filter(k -> k.getNextSequence() != null && k.getNextSequence() != nextSequence).map(PreNextRelationRelation::getNextSequence).collect(Collectors.toList());
                    for (int j = 0; j < otherNextSequences.size(); j++) {
                        Integer nextPreSequence = otherNextSequences.get(j);
                        //根据前工序算后工序数量
                        BigDecimal continueNextOperationQty = calculateNextOperationQty(preOperationQty, routingStepSequenceNrMaps.get(preSequence), routingStepSequenceNrMaps.get(nextPreSequence));
                        sequenceAndQtyMaps.put(nextPreSequence, continueNextOperationQty);
                        calculateNextOperationQtyByPreOperation(nextPreSequence, preNextRelationRelations, sequenceAndQtyMaps, routingStepSequenceNrMaps);
                    }
                }
            }
        }
    }
    /**
     * 计算前工序数量
     *
     * @param nextOperationQty 后工序数量
     * @param preRoutingStep
     * @param nextRoutingStep
     */
    private  static BigDecimal calculatePreOperationQty(BigDecimal nextOperationQty, RoutingStepDO preRoutingStep, RoutingStepDO nextRoutingStep) {
        // 计算前工序的数量
        //（前）工序的数量 = （后）工序数量*后工序与前工序数量比 /成品率+固定报废数
        //“与前工序数量比”来源于后工序对应工艺步骤，“成品率”、“固定报废数”来源于前工序对应工艺步骤
        // 成品率
        BigDecimal yield = preRoutingStep.getYield() == null ? BigDecimal.ONE : preRoutingStep.getYield();
        // 与前工序数量比
        BigDecimal preProcessRatio = nextRoutingStep.getPreProcessRatio() == null ? BigDecimal.ONE : nextRoutingStep.getPreProcessRatio();
        //固定报废数
        BigDecimal scrap = preRoutingStep.getScrap() == null ? BigDecimal.ZERO : preRoutingStep.getScrap();
        // 百分比损耗率
        BigDecimal percentageScrapRate = preRoutingStep.getPercentageScrapRate() == null ? BigDecimal.ZERO : preRoutingStep.getPercentageScrapRate();
        BigDecimal result = nextOperationQty.multiply(preProcessRatio).divide(yield, 6);
        if (ScrapStrategyEnum.FIXED_SCRAP.getCode().equals(preRoutingStep.getScrapStrategy())) {
            result = nextOperationQty.multiply(preProcessRatio).divide(yield, 4, RoundingMode.UP).add(scrap).setScale(0, RoundingMode.UP);
        } else if (ScrapStrategyEnum.PERCENTAGE_SCRAP.getCode().equals(preRoutingStep.getScrapStrategy())) {
            result = nextOperationQty.multiply(preProcessRatio).divide(yield, 4, RoundingMode.UP).divide(BigDecimal.ONE.subtract(percentageScrapRate), 0, RoundingMode.UP);
        }
        return result;
    }

    /**
     * 计算后工序数量
     *
     * @param preOperationQty 前工序数量
     * @param preRoutingStep
     * @param nextRoutingStep
     */
    private static BigDecimal calculateNextOperationQty(BigDecimal preOperationQty, RoutingStepDO preRoutingStep, RoutingStepDO nextRoutingStep) {
        //计算后工序的数量
        //后工序的数量 = （前工序数量-固定报废数）*成品率/后工序与前工序数量比
        //“与前工序数量比”来源于后工序对应工艺步骤，“成品率”、“固定报废数”来源于前工序对应工艺步骤
        return preOperationQty.subtract(preRoutingStep.getScrap() == null ? BigDecimal.ZERO : preRoutingStep.getScrap()).multiply(preRoutingStep.getYield()).divide(nextRoutingStep.getPercentageScrapRate()).setScale(0, BigDecimal.ROUND_UP);
    }

    /**
     * 根据前工序计算后工序数量
     *
     * @param preSequence
     * @param preNextRelationRelations
     * @param sequenceAndQtyMaps
     * @param routingStepSequenceNrMaps
     */
    private static void calculateNextOperationQtyByPreOperation(int preSequence, List<PreNextRelationRelation> preNextRelationRelations, Map<Integer, BigDecimal> sequenceAndQtyMaps, Map<Integer, RoutingStepDO> routingStepSequenceNrMaps) {
        List<PreNextRelationRelation> nextSenquences = preNextRelationRelations.stream().filter(k -> k.getPreSequence() != null && k.getPreSequence() == preSequence).collect(Collectors.toList());
        //如果前工序或者前工序就是自己
        if (CollectionUtils.isEmpty(nextSenquences)) {
            return;
        } else {
            for (int i = 0; i < nextSenquences.size(); i++) {
                Integer nextSequence = nextSenquences.get(i).getPreSequence();
                BigDecimal preOperationQty = sequenceAndQtyMaps.get(preSequence);
                BigDecimal qty = calculateNextOperationQty(preOperationQty, routingStepSequenceNrMaps.get(preSequence), routingStepSequenceNrMaps.get(nextSequence));
                sequenceAndQtyMaps.put(nextSequence, qty);
                calculateNextOperationQtyByPreOperation(nextSequence, preNextRelationRelations, sequenceAndQtyMaps, routingStepSequenceNrMaps);
            }
        }
    }

    private OperationDO createOperationDTO(WorkOrderDO workOrderDO, RoutingStepDO routingStepDO, Map<Integer, BigDecimal> sequenceAndQtyMaps,
                                           Map<String, BigDecimal> routingStepOfQtyMap) {
        OperationDO operationDO = new OperationDO();
        operationDO.setId(UUIDUtil.getUUID());
        String operationOwnCode = workOrderDO.getOrderNo() + "-" + routingStepDO.getSequenceNo();
        operationDO.setOperationCode(operationOwnCode);
        operationDO.setOrderId(workOrderDO.getId());
        operationDO.setRoutingStepId(routingStepDO.getId());
        operationDO.setStandardStepId(routingStepDO.getStandardStepId());
        operationDO.setRoutingStepSequenceNo(routingStepDO.getSequenceNo());
        operationDO.setPreRoutingStepSequenceNo(routingStepDO.getPreRoutingStepSequenceNo());
        operationDO.setNextRoutingStepSequenceNo(routingStepDO.getNextRoutingStepSequenceNo());
        operationDO.setQuantity(sequenceAndQtyMaps.get(routingStepDO.getSequenceNo()));
        operationDO.setFrozen(FrozenStatusEnum.UNFROZEN.getCode());
        operationDO.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
        operationDO.setOrderType(OrderTypeEnum.WORK_ORDER.getCode());
        operationDO.setKitStatus(KitStatusEnum.UNKIT.getCode());
        operationDO.setStockPointId(workOrderDO.getStockPointId());
        operationDO.setProductId(workOrderDO.getProductId());
        operationDO.setProductStockPointId(workOrderDO.getProductStockPointId());
        operationDO.setConnectionTask(routingStepDO.getConnectionTask());
        operationDO.setConnectionType(routingStepDO.getConnectionType());
        operationDO.setMaxConnectionDuration(routingStepDO.getMaxConnectionDuration());
        operationDO.setMinConnectionDuration(routingStepDO.getMinConnectionDuration());
        setQtyByLimit(routingStepDO, routingStepOfQtyMap, operationDO);
        //生成id
        operationDO.setId(UUIDUtil.getUUID());
        operationDO.setWorkOrderDO(workOrderDO);
        return operationDO;
    }

    /**
     * 根据外部限制设置工序数量
     * @param routingStepDO
     * @param routingStepOfQtyMap
     * @param operationDO
     */
    private static void setQtyByLimit(RoutingStepDO routingStepDO, Map<String, BigDecimal> routingStepOfQtyMap, OperationDO operationDO) {
        if (routingStepOfQtyMap != null && routingStepOfQtyMap.containsKey(routingStepDO.getId())) {
            operationDO.setQuantity(routingStepOfQtyMap.get(String.join("#", operationDO.getOrderId(), routingStepDO.getId())));
        }
    }

    public Map<String, BigDecimal> getWorkOrderQuantity(List<WorkOrderDO> workOrderDOList) {
        Map<String, BigDecimal> map = new HashMap<>();
        workOrderDOList.forEach(WorkOrderDO -> map.put(WorkOrderDO.getId(), WorkOrderDO.getQuantity()));
        return map;
    }

    private void removeOperationListByOrderId(List<OperationDO> insertOperations, List<OperationDO> updateOperations, List<OperationDO> deleteOperations, String orderId) {
        insertOperations.removeIf(p -> p.getOrderId().equals(orderId));
        updateOperations.removeIf(p -> p.getOrderId().equals(orderId));
        deleteOperations.removeIf(p -> p.getOrderId().equals(orderId));
    }

    private void doDeleteOperationByIds(List<String> waitingRemoveOperationIds) {
        if (CollectionUtils.isNotEmpty(waitingRemoveOperationIds)) {
            this.doDelete(waitingRemoveOperationIds);
            operationInputService.doDeleteByOperationIds(waitingRemoveOperationIds);
            operationOutputService.doDeleteByOperationIds(waitingRemoveOperationIds);
            operationResourceService.doDeleteByOperationIds(waitingRemoveOperationIds);
            operationTaskService.doDeleteByOperationIds(waitingRemoveOperationIds);
        }
    }

    private void doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        if (idList.size() > 1) {
            // 删除扩展表数据
            operationExtendService.doDeleteByOperationIds(idList);
            operationDao.deleteBatch(idList);
        }
        // 删除扩展表数据
        operationExtendService.doDeleteByOperationIds(idList);
        operationDao.deleteByPrimaryKey(idList.get(0));
    }

    private void operationDtoSplit(OperationDTO operationDTO, OperationExtendDTO operationExtendDTO) {
        operationExtendDTO.setOperationId(operationDTO.getId());
        operationExtendDTO.setPlannedMainResourceId(operationDTO.getPlannedResourceId());
        operationExtendDTO.setProductionStartTime(operationDTO.getStartTime());
        operationExtendDTO.setProductionEndTime(operationDTO.getEndTime());
    }

    private void doOperationExtendCreateBatch(List<OperationDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<OperationExtendDTO> operationExtendDTOS = new ArrayList<>();
        for (OperationDTO operationDTO : list) {
            OperationExtendDTO operationExtendDTO = new OperationExtendDTO();
            operationDtoSplit(operationDTO, operationExtendDTO);
            operationExtendDTO.setId(UUIDUtil.getUUID());
            operationExtendDTOS.add(operationExtendDTO);
        }
        //新增扩展信息
        List<List<OperationExtendDTO>> operationExtendList = com.yhl.platform.common.utils.CollectionUtils.splitList(operationExtendDTOS, 2000);
        for (List<OperationExtendDTO> operationExtendDTOList : operationExtendList) {
            operationExtendService.doCreateBatch(operationExtendDTOList);
        }
    }

    private void doOperationExtendUpdateBatch(List<OperationDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<OperationExtendDTO> operationExtendDTOList = new ArrayList<>();
        // 修改扩展信息
        for (OperationDTO operationDTO : list) {
            OperationExtendDTO operationExtendDTO = new OperationExtendDTO();
            operationDtoSplit(operationDTO, operationExtendDTO);
            operationExtendDTOList.add(operationExtendDTO);
        }
        //修改扩展表信息
        List<List<OperationExtendDTO>> operationExtendList = com.yhl.platform.common.utils.CollectionUtils.splitList(operationExtendDTOList, 2000);
        for (List<OperationExtendDTO> operationExtendDTOS : operationExtendList) {
            operationExtendService.doUpdateBatch(operationExtendDTOS);
        }
    }
}
