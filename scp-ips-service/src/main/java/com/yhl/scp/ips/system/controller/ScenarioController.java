package com.yhl.scp.ips.system.controller;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.scp.common.vo.LabelValueX;
import com.yhl.scp.dps.extension.forecast.vo.DemandForecastVersionVO;
import com.yhl.scp.dps.feign.DpsFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.rbac.entity.TenantModule;
import com.yhl.scp.ips.rbac.service.TenantModuleService;
import com.yhl.scp.ips.system.ScenarioParam;
import com.yhl.scp.ips.system.dto.CompleteScenarioConfigDTO;
import com.yhl.scp.ips.system.dto.ScenarioDTO;
import com.yhl.scp.ips.system.dto.ScenarioDropdown;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.entity.ScenarioConfigListVO;
import com.yhl.scp.ips.system.service.ScenarioService;
import com.yhl.scp.mds.feign.MdsFeign;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ScenarioController</code>
 * <p>
 * 场景管理控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-09-12 10:42:32
 */
@Api(tags = "场景管理控制器")
@Slf4j
@RestController
@RequestMapping("scenario")
public class ScenarioController extends BaseController {

    @Resource
    private ScenarioService scenarioService;

    @Resource
    private TenantModuleService tenantModuleService;

    @Resource
    private DpsFeign dpsFeign;

    @Resource
    private MdsFeign mdsFeign;

    @ApiOperation("创建(克隆)场景")
    @PostMapping(value = "create")
    public BaseResponse<Scenario> create(ScenarioDTO scenarioDTO) throws SQLException {
        return BaseResponse.success("创建成功", scenarioService.doCreate(scenarioDTO));
    }

    @ApiOperation("修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(Scenario scenario) {
        Scenario newScenario = scenarioService.selectById(scenario.getId());
        newScenario.setScenarioName(scenario.getScenarioName());
        return scenarioService.doUpdate(newScenario);
    }

    @ApiOperation("删除")
    @PostMapping(value = "delete/{id}")
    public BaseResponse<Void> delete(@PathVariable("id") String id) {
        return scenarioService.doDelete(id);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        scenarioService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation("分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<Scenario>> page() {
        List<Scenario> scenarios = scenarioService.selectPage(getPagination(), getSortParam(), getQueryCriteriaParam());
        PageInfo<Scenario> pageInfo = new PageInfo<>(scenarios);
        return new BaseResponse<>(true, pageInfo);
    }

    @ApiOperation("根据获取id获取详情")
    @GetMapping(value = "/{id}")
    public BaseResponse<Scenario> getById(@PathVariable("id") String id) {
        return new BaseResponse<>(true, scenarioService.selectById(id));
    }

    @ApiOperation("场景克隆弹出框数据（SYSTEM_ADMIN）")
    @GetMapping(value = "clone/data")
    public BaseResponse<Map<String, Object>> getCloneData(@RequestParam("tenantId") String tenantId) {
        Map<String, Object> result = new HashMap<>();
        List<TenantModule> tenantModules = tenantModuleService.selectByTenantId(tenantId);
        List<Scenario> scenarios = scenarioService.selectAll();
        Map<String, List<Scenario>> map = scenarios.stream().collect(Collectors.groupingBy(Scenario::getModuleCode));
        for (TenantModule tenantModule : tenantModules) {
            List<LabelValue<String>> labelValues = new ArrayList<>();
            List<Scenario> subScenarios = map.get(tenantModule.getModuleCode());
            if (CollectionUtils.isEmpty(subScenarios)) {
                continue;
            }
            for (Scenario scenario : subScenarios) {
                labelValues.add(new LabelValue<>(scenario.getScenarioName(), scenario.getId()));
            }
            result.put(tenantModule.getModuleCode(), labelValues);
        }
        return new BaseResponse<>(true, result);
    }

    /**
     * 系统管理克隆或者修改租户场景数据来源
     */
    @ApiOperation("保存租户克隆（SYSTEM_ADMIN）")
    @PostMapping(value = "save")
    public BaseResponse<Void> save(@RequestBody ScenarioParam scenarioParam) throws SQLException {
        return scenarioService.doSave(scenarioParam);
    }

    @ApiOperation("获取模块列表")
    @PostMapping(value = "modules")
    public BaseResponse<List<LabelValue<String>>> modules() {
        List<LabelValue<String>> enumValueList = EnumUtils.getEnumValueList(RzzSystemModuleEnum.class);
        return BaseResponse.success(enumValueList);
    }

    @ApiOperation("获取场景下拉")
    @PostMapping(value = "scenarios")
    public BaseResponse<List<Scenario>> scenarios() {
        List<Scenario> scenarios = scenarioService.selectByTenantIdAndUserId(SystemHolder.getTenantId(),
                SystemHolder.getUserId());
        return BaseResponse.success(scenarios);
    }

    @ApiOperation("根据租户获取模块场景下拉")
    @GetMapping(value = "scenariosByTenantId")
    public BaseResponse<List<Scenario>> scenariosByTenantId(@RequestParam(value = "tenantId") String tenantId) {
        List<Scenario> scenarios = scenarioService.selectByTenantIdAndUserId(tenantId, null);
        return BaseResponse.success(scenarios);
    }

    @ApiOperation("需求版本列表")
    @GetMapping(value = "/dps/confirmedDropdown")
    public BaseResponse<ScenarioDropdown> confirmedDropdown() {
        ScenarioDropdown scenarioDropdown = new ScenarioDropdown();
        scenarioDropdown.setDpsVersion(dpsFeign.demandForecastVersionConfirmedDropdown());
        scenarioDropdown.setFinishedProducts(mdsFeign.finishedProductDropdown());
        scenarioDropdown.setSalesSegmentTree(mdsFeign.salesSegmentTreeDropdownFeign(-1, null, null));
        return BaseResponse.success(scenarioDropdown);
    }

    @ApiOperation("需求版本详情")
    @GetMapping(value = "/dps/detailQuery")
    public BaseResponse<DemandForecastVersionVO> dpsDetail(@RequestParam("versionId") String versionId) {
        return BaseResponse.success(dpsFeign.demandForecastVersionDetailQuery(versionId));
    }

    @ApiOperation("时序列表")
    @GetMapping(value = "/timePeriod/indexDropdown")
    public BaseResponse<List<LabelValue<Integer>>> timePeriodIndexDropdown(@RequestParam("timePeriodGroupId") String timePeriodGroupId) {
        return BaseResponse.success(mdsFeign.timePeriodIndexDropdown(timePeriodGroupId));
    }

    @ApiOperation("场景对比-子场景查询")
    @GetMapping(value = "list")
    public BaseResponse<List<LabelValueX<String>>> scenarioList(@RequestHeader("tenant") String tenant,
                                                                @RequestHeader("module") String module) {
        if (StringUtils.isBlank(tenant)) {
            return BaseResponse.error("租户不能为空");
        }
        if (StringUtils.isBlank(module)) {
            return BaseResponse.error("模块不能为空");
        }
        Map<String, Object> params = ImmutableMap.of("tenantId", tenant, "moduleCode", module,
                "master", YesOrNoEnum.NO.getCode());
        return new BaseResponse<>(true, scenarioService.selectByParams(params).stream().map(item ->
                new LabelValueX<>(item.getScenarioName(), item.getDataBaseName(), item.getTimePeriodGroupId(),
                        item.getDemandForecastVersionId())).collect(Collectors.toList()));
    }

    @ApiOperation(value = "保存")
    @PostMapping(value = "saveCompleteScenario")
    public BaseResponse<Void> saveCompleteScenario(@RequestBody CompleteScenarioConfigDTO completeScenarioConfigDTO) throws SQLException {
        scenarioService.doSaveCompleteScenario(completeScenarioConfigDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "updateCompleteScenario")
    public BaseResponse<Void> updateCompleteScenario(@RequestBody CompleteScenarioConfigDTO completeScenarioConfigDTO) {
        scenarioService.doUpdateCompleteScenario(completeScenarioConfigDTO, null);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation("场景参数列表分页查询")
    @GetMapping(value = "/pageWithScenarioConfig")
    public BaseResponse<PageInfo<ScenarioConfigListVO>> pageWithScenarioConfig() {
        List<ScenarioConfigListVO> result = scenarioService.pageWithScenarioConfig(getPagination(), getQueryCriteriaParam());
        PageInfo<ScenarioConfigListVO> pageInfo = new PageInfo<>(result);
        return new BaseResponse<>(true, pageInfo);
    }

    @ApiOperation("场景参数列表查看详情")
    @PostMapping(value = "/scenarioConfigDetail")
    public BaseResponse<CompleteScenarioConfigDTO> scenarioConfigDetail(@RequestParam("scenarioId") String scenarioId) {
        CompleteScenarioConfigDTO completeScenarioConfigDTO = scenarioService.getScenarioConfigDetail(scenarioId);
        return new BaseResponse<>(true, completeScenarioConfigDTO);
    }

    @ApiOperation("场景选中")
    @PostMapping(value = "scenarioSelect")
    public BaseResponse<Void> scenarioSelect(@RequestParam("moduleCode") String moduleCode,
                                             @RequestParam(value = "tenantId", required = false) String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            tenantId = SystemHolder.getTenantId();
        }
        String userId = SystemHolder.getUserId();
        String scenario = scenarioService.selectDefaultScenario(tenantId, userId, moduleCode);
        response.setHeader("module", moduleCode);
        response.setHeader("scenario", scenario);
        return BaseResponse.success();
    }

}