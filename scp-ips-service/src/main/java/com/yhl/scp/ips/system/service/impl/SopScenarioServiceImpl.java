package com.yhl.scp.ips.system.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.*;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.rbac.service.UserService;
import com.yhl.scp.ips.system.dto.SopScenarioDTO;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.enums.ScenarioConfigEnum;
import com.yhl.scp.ips.system.service.IScenarioService;
import com.yhl.scp.ips.system.service.ScenarioService;
import com.yhl.scp.ips.system.vo.SopScenarioVO;
import com.yhl.scp.sop.extension.scenario.dto.ScenarioConfigDTO;
import com.yhl.scp.sop.extension.scenario.vo.ScenarioConfigVO;
import com.yhl.scp.sop.feign.SopFeign;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>SopScenarioServiceImpl</code>
 * <p>
 * S&OP场景策略接口实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-12 18:27:43
 */
@Service
public class SopScenarioServiceImpl implements IScenarioService<SopScenarioDTO, SopScenarioVO> {

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.driverClassName}")
    private String driver;

    @Value("${yhl.datasource.uri}")
    private String uri;

    @Value("${yhl.datasource.params}")
    private String databaseParams;

    @Resource
    private ScenarioService scenarioService;

    @Resource
    private SopFeign sopFeign;

    @Resource
    private UserService userService;

    @Override
    public BaseResponse<Void> doCreate(SopScenarioDTO dto) {
        // 数据校验
        postRequestValidation(dto);
        // 查询源场景
        String sourceScenarioId = dto.getSourceScenarioId();
        Scenario sourceScenario = scenarioService.selectById(sourceScenarioId);
        String sourceDataBaseName = sourceScenario.getDataBaseName();
        String moduleCode = sourceScenario.getModuleCode();
        // 初始化需求版本 & 时段序列
        String demandForecastVersionId = sourceScenario.getDemandForecastVersionId();
        String timePeriodGroupId = sourceScenario.getTimePeriodGroupId();
        boolean duplicated = dto.getDuplicated();
        if (!duplicated) {
            demandForecastVersionId = dto.getDemandForecastVersionId();
            timePeriodGroupId = dto.getTimePeriodGroupId();
        }
        // 查询目标场景
        String targetScenarioId = dto.getTargetScenarioId();
        String tenantId = SystemHolder.getTenantId();
        Scenario targetScenario = scenarioService.selectByName(targetScenarioId, tenantId, moduleCode);
        // 初始化变量

        String scenarioId = UUIDUtil.getUUID();
        String targetDataBaseName;
        if (null == targetScenario) {
            // 新建场景
            targetDataBaseName = generateTargetDataBaseName(moduleCode);
            createScenario(sourceScenario, scenarioId, targetScenarioId, targetDataBaseName, demandForecastVersionId,
                    timePeriodGroupId);
            // 建库DDL
            DatabaseUtils.createDatabase(url, username, password, driver, targetDataBaseName);
            userService.doInsertUserScenario(SystemHolder.getUserId(), scenarioId);
        } else {
            // 覆盖场景
            scenarioId = targetScenario.getId();
            targetDataBaseName = targetScenario.getDataBaseName();
            updateScenario(targetScenario, sourceScenarioId, demandForecastVersionId, timePeriodGroupId);
        }
        // 拷贝表
        scenarioService.doTablesCopy(sourceDataBaseName, targetDataBaseName);
        // 拷贝视图
        scenarioService.doViewsCopy(sourceDataBaseName, targetDataBaseName);
        // 拷贝数据
        if (!duplicated) {
            scenarioService.doSopDataCopy(targetDataBaseName, demandForecastVersionId, timePeriodGroupId);
        }
        // 处理场景参数
        doResolveScenarioConfig(dto, scenarioId, targetDataBaseName, duplicated, sourceDataBaseName);
        return BaseResponse.success();
    }

    /**
     * 请求校验
     *
     * @param dto S&OP场景DTO
     */
    private void postRequestValidation(SopScenarioDTO dto) {
        // 非空校验
        String sourceScenarioId = dto.getSourceScenarioId();
        String targetScenarioId = dto.getTargetScenarioId();
        if (StringUtils.isEmpty(sourceScenarioId)) {
            throw new BusinessException("源场景不能为空");
        }
        if (StringUtils.isEmpty(targetScenarioId)) {
            throw new BusinessException("目标场景不能为空");
        }
        boolean duplicated = dto.getDuplicated();
        if (!duplicated) {
            if (StringUtils.isEmpty(dto.getDemandForecastVersionId())) {
                throw new BusinessException("需求版本不能为空");
            }
            if (StringUtils.isEmpty(dto.getTimePeriodRange())) {
                throw new BusinessException("时段范围不能为空");
            }
            if (StringUtils.isEmpty(dto.getProductRange())) {
                throw new BusinessException("产品范围不能为空");
            }
        }
        // 不相等校验
        if (StringUtils.equals(sourceScenarioId, targetScenarioId)) {
            throw new BusinessException("目标场景不能是源场景");
        }
    }

    /**
     * 生成目标数据库名
     *
     * @param moduleCode 模块代码
     * @return java.lang.String
     */
    public static String generateTargetDataBaseName(String moduleCode) {
        return Constants.SCENARIO_PREFIX + (SystemModuleEnum.SOP.getCode().equals(moduleCode) ? "sop" : moduleCode.toLowerCase())
                + "_" + FormatConversionUtils.getCharAndNumber(8);
    }

    /**
     * 新增场景
     *
     * @param sourceScenario          源场景
     * @param scenarioId              场景ID
     * @param scenarioName            场景名
     * @param databaseName            数据库名
     * @param demandForecastVersionId 业务预测版本ID
     * @param timePeriodGroupId       时段序列ID
     */
    private void createScenario(Scenario sourceScenario, String scenarioId, String scenarioName, String databaseName,
                                String demandForecastVersionId, String timePeriodGroupId) {
        String userId = SystemHolder.getUserId();
        Date date = new Date();
        String tenantId = SystemHolder.getTenantId();
        Map<String, Object> params = new HashMap<>(4);
        params.put("moduleCode", sourceScenario.getModuleCode());
        params.put("tenantId", tenantId);
        List<Scenario> scenarios = scenarioService.selectByParams(params);
        List<String> databaseNames = scenarios.stream().map(Scenario::getDataBaseName).distinct().collect(Collectors.toList());
        Scenario scenario = new Scenario();
        scenario.setId(scenarioId);
        scenario.setModuleCode(sourceScenario.getModuleCode());
        scenario.setScenarioName(scenarioName);
        scenario.setDataBaseName(databaseName);
        // 校验数据库名
        while (databaseNames.contains(databaseName)) {
            databaseName = generateTargetDataBaseName(sourceScenario.getModuleCode());
        }
        scenario.setUrl(uri + databaseName + databaseParams);
        scenario.setTenantId(tenantId);
        scenario.setSourceScenarioId(sourceScenario.getId());
        scenario.setMaster(YesOrNoEnum.NO.getCode());
        scenario.setDemandForecastVersionId(demandForecastVersionId);
        scenario.setTimePeriodGroupId(timePeriodGroupId);
        scenario.setEnabled(YesOrNoEnum.YES.getCode());
        scenario.setCreator(userId);
        scenario.setCreateTime(date);
        scenario.setModifier(userId);
        scenario.setModifyTime(date);
        scenarioService.doCreate(scenario);
    }

    /**
     * 修改场景
     *
     * @param targetScenario          目标场景
     * @param sourceScenarioId        源场景ID
     * @param demandForecastVersionId 欲求预测版本ID
     * @param timePeriodGroupId       时段序列ID
     */
    private void updateScenario(Scenario targetScenario, String sourceScenarioId, String demandForecastVersionId,
                                String timePeriodGroupId) {
        targetScenario.setSourceScenarioId(sourceScenarioId);
        targetScenario.setDemandForecastVersionId(demandForecastVersionId);
        targetScenario.setTimePeriodGroupId(timePeriodGroupId);
        targetScenario.setModifier(SystemHolder.getUserId());
        targetScenario.setModifyTime(new Date());
        scenarioService.doUpdate(targetScenario);
    }

    /**
     * 处理场景参数
     *
     * @param dto                S&OP场景DTO
     * @param scenarioId         场景ID
     * @param targetDataBaseName 目标数据库名
     * @param duplicated         是否复制
     * @param sourceDataBaseName 来源数据库名
     */
    private void doResolveScenarioConfig(SopScenarioDTO dto, String scenarioId, String targetDataBaseName,
                                         boolean duplicated, String sourceDataBaseName) {
        List<ScenarioConfigDTO> configList = new ArrayList<>();
        if (duplicated) {
            // 复制
            List<ScenarioConfigVO> sourceConfigs = sopFeign.getScenarioConfig(sourceDataBaseName);
            if (CollectionUtils.isNotEmpty(sourceConfigs)) {
                sourceConfigs.forEach(source -> {
                    source.setId(null);
                    source.setScenarioId(scenarioId);
                    ScenarioConfigDTO target = new ScenarioConfigDTO();
                    BeanUtils.copyProperties(source, target);
                    configList.add(target);
                });
                sopFeign.saveScenarioConfig(targetDataBaseName, configList);
            }
            return;
        }
        // 需求范围
        String timePeriodRange = dto.getTimePeriodRange();
        if (StringUtils.isNotEmpty(timePeriodRange)) {
            configList.add(ScenarioConfigDTO.builder().scenarioId(scenarioId)
                    .configCategory(ScenarioConfigEnum.REQUIREMENT_RANGE.getCode())
                    .configType(ScenarioConfigEnum.TIME_PERIOD_RANGE.getCode())
                    .configData(timePeriodRange).build());
        }
        String productRange = dto.getProductRange();
        if (StringUtils.isNotEmpty(productRange)) {
            configList.add(ScenarioConfigDTO.builder().scenarioId(scenarioId)
                    .configCategory(ScenarioConfigEnum.REQUIREMENT_RANGE.getCode())
                    .configType(ScenarioConfigEnum.PRODUCT_RANGE.getCode())
                    .configData(productRange).build());
        }
        String salesSegmentRange = dto.getSalesSegmentRange();
        if (StringUtils.isNotEmpty(salesSegmentRange)) {
            configList.add(ScenarioConfigDTO.builder().scenarioId(scenarioId)
                    .configCategory(ScenarioConfigEnum.REQUIREMENT_RANGE.getCode())
                    .configType(ScenarioConfigEnum.SALES_SEGMENT_RANGE.getCode())
                    .configData(salesSegmentRange).build());
        }
        // 供应链模型设置
        String timeLimit = dto.getTimeLimit();
        if (StringUtils.isNotEmpty(timeLimit)) {
            configList.add(ScenarioConfigDTO.builder().scenarioId(scenarioId)
                    .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                    .configType(ScenarioConfigEnum.TIME_LIMIT.getCode())
                    .configData(timeLimit).build());
        }
        String productionSupplyAndDemandCouple = dto.getProductionSupplyAndDemandCouple();
        if (StringUtils.isNotEmpty(productionSupplyAndDemandCouple)) {
            configList.add(ScenarioConfigDTO.builder().scenarioId(scenarioId)
                    .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                    .configType(ScenarioConfigEnum.PRODUCTION_SUPPLY_AND_DEMAND_COUPLE.getCode())
                    .configData(productionSupplyAndDemandCouple).build());
        }

        String onlyBottleneckMaterial = dto.getOnlyBottleneckMaterial();
        if (StringUtils.isNotEmpty(onlyBottleneckMaterial)) {
            configList.add(ScenarioConfigDTO.builder().scenarioId(scenarioId)
                    .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                    .configType(ScenarioConfigEnum.ONLY_BOTTLENECK_MATERIAL.getCode())
                    .configData(onlyBottleneckMaterial).build());
        }
        String onlyBottleneckResource = dto.getOnlyBottleneckResource();
        if (StringUtils.isNotEmpty(onlyBottleneckResource)) {
            configList.add(ScenarioConfigDTO.builder().scenarioId(scenarioId)
                    .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                    .configType(ScenarioConfigEnum.ONLY_BOTTLENECK_RESOURCE.getCode())
                    .configData(onlyBottleneckResource).build());
        }
        Map<String, String> disableConstraints = dto.getDisableConstraints();
        if (null != disableConstraints) {
            String configData = JacksonUtils.toJson(disableConstraints);
            configList.add(ScenarioConfigDTO.builder().scenarioId(scenarioId)
                    .configCategory(ScenarioConfigEnum.ALGORITHM_CONFIG.getCode())
                    .configType(ScenarioConfigEnum.DISABLE_CONSTRAINTS.getCode())
                    .configData(configData).build());
        }
        sopFeign.saveScenarioConfig(targetDataBaseName, configList);
    }

    @Override
    public BaseResponse<Void> doUpdate(SopScenarioDTO dto) {
        // 数据校验
        postRequestValidation(dto);
        String targetScenarioId = dto.getTargetScenarioId();
        Scenario targetScenario = scenarioService.selectById(targetScenarioId);
        // 处理场景参数
        doResolveScenarioConfig(dto, targetScenarioId, targetScenario.getDataBaseName(), false, null);
        return BaseResponse.success();
    }

    @Override
    @SuppressWarnings({"rawtypes", "unchecked"})
    public SopScenarioVO selectByPrimaryKey(String id) {
        Scenario scenario = scenarioService.selectById(id);
        SopScenarioVO sopScenarioVO = SopScenarioVO.builder().build();
        BeanUtils.copyProperties(scenario, sopScenarioVO);
        sopScenarioVO.setTargetScenarioId(scenario.getId());
        sopScenarioVO.setDuplicated(false);
        List<ScenarioConfigVO> configs = sopFeign.getScenarioConfig(scenario.getDataBaseName());
        if (CollectionUtils.isNotEmpty(configs)) {
            Map<String, String> configMap = configs.stream().collect(Collectors
                    .toMap(ScenarioConfigVO::getConfigType, ScenarioConfigVO::getConfigData, (t1, t2) -> t2));
            sopScenarioVO.setTimePeriodRange(configMap.get(ScenarioConfigEnum.TIME_PERIOD_RANGE.getCode()));
            sopScenarioVO.setProductRange(configMap.get(ScenarioConfigEnum.PRODUCT_RANGE.getCode()));
            sopScenarioVO.setSalesSegmentRange(configMap.get(ScenarioConfigEnum.SALES_SEGMENT_RANGE.getCode()));
            sopScenarioVO.setProductionCapacity(configMap.get(ScenarioConfigEnum.PRODUCTION_CAPACITY.getCode()));
            sopScenarioVO.setStockPointCapacity(configMap.get(ScenarioConfigEnum.STOCK_POINT_CAPACITY.getCode()));
            sopScenarioVO.setTransportCapacity(configMap.get(ScenarioConfigEnum.TRANSPORT_CAPACITY.getCode()));
            sopScenarioVO.setTimeLimit(configMap.get(ScenarioConfigEnum.TIME_LIMIT.getCode()));
            sopScenarioVO.setProductionSupplyAndDemandCouple(configMap
                    .get(ScenarioConfigEnum.PRODUCTION_SUPPLY_AND_DEMAND_COUPLE.getCode()));
            sopScenarioVO.setOnlyBottleneckMaterial(configMap.get(ScenarioConfigEnum.ONLY_BOTTLENECK_MATERIAL.getCode()));
            sopScenarioVO.setOnlyBottleneckResource(configMap.get(ScenarioConfigEnum.ONLY_BOTTLENECK_RESOURCE.getCode()));
            String disableConstraints = configMap.get(ScenarioConfigEnum.DISABLE_CONSTRAINTS.getCode());
            if (StringUtils.isNotEmpty(disableConstraints)) {
                Map map = JacksonUtils.toObj(disableConstraints, new TypeReference<HashMap>() {
                });
                sopScenarioVO.setDisableConstraints(map);
            }
        }
        return sopScenarioVO;
    }

}