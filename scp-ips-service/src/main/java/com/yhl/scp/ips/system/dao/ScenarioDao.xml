<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.dao.ScenarioDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.entity.Scenario">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode"/>
        <result column="scenario_name" jdbcType="VARCHAR" property="scenarioName"/>
        <result column="data_base_name" jdbcType="VARCHAR" property="dataBaseName"/>
        <result column="master" jdbcType="VARCHAR" property="master"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="alternative_column" jdbcType="VARCHAR" property="alternativeColumn"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="source_scenario_id" jdbcType="VARCHAR" property="sourceScenarioId"/>
        <result column="demand_forecast_version_id" jdbcType="VARCHAR" property="demandForecastVersionId"/>
        <result column="time_period_group_id" jdbcType="VARCHAR" property="timePeriodGroupId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.system.entity.Scenario">
        <result column="starred" jdbcType="VARCHAR" property="starred"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, module_code, scenario_name, data_base_name, master, tenant_id, alternative_column,
        url, user_name, password, source_scenario_id, demand_forecast_version_id, time_period_group_id,
        remark, enabled, creator, create_time, modifier, modify_time
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,starred
    </sql>
    <sql id="Base_Where_Condition">
        where 1=1
        <if test="params.ids!=null and params.ids.size()>0">
            and id in
            <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.tenantId!=null and params.tenantId!=''">
            and tenant_id = #{params.tenantId,jdbcType=VARCHAR}
        </if>
        <if test="params.moduleCode!=null and params.moduleCode!=''">
            and (module_code = #{params.moduleCode,jdbcType=VARCHAR} or module_code = 'UNION')
        </if>
        <if test="params.scenarioName!=null and params.scenarioName!=''">
            and scenario_name = #{params.scenarioName,jdbcType=VARCHAR}
        </if>
        <if test="params.dataBaseName!=null and params.dataBaseName!=''">
            and data_base_name = #{params.dataBaseName,jdbcType=VARCHAR}
        </if>
        <if test="params.master!=null and params.master!=''">
            and master = #{params.master,jdbcType=VARCHAR}
        </if>
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_scenario
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_scenario
        where scenario_name = #{scenarioName,jdbcType=VARCHAR}
        and tenant_id = #{tenantId,jdbcType=VARCHAR}
        and enabled = 'YES'
        and module_code = #{moduleCode}
    </select>
    <select id="selectByPage" parameterType="java.lang.String" resultMap="BaseResultMap">
        select s.*,t.tenant_name
        from auth_scenario s
        left join auth_rbac_tenant t
        on s.tenant_id = t.id
        where 1=1 and s.enabled = 'YES'
        <if test="tenantId!=null and tenantId!=''">
            and tenant_id = #{tenantId}
        </if>
        <if test="queryCriteriaParam!=null and queryCriteriaParam!=''">
            ${queryCriteriaParam}
        </if>
        <if test="sortParam!=null and sortParam!=''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectByTenantId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_scenario
        where enabled = 'YES' and tenant_id = #{tenantId}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_scenario
        where enabled = 'YES'
    </select>
    <select id="selectByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_scenario
        <include refid="Base_Where_Condition"/>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from auth_scenario
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.yhl.scp.ips.system.entity.Scenario">
        insert into auth_scenario (id, module_code, scenario_name,
                                   data_base_name, master, tenant_id,
                                   alternative_column, url, user_name,
                                   password, source_scenario_id,
                                   demand_forecast_version_id, time_period_group_id,
                                   remark, enabled,
                                   creator, create_time,
                                   modifier, modify_time)
        values (#{id,jdbcType=VARCHAR}, #{moduleCode,jdbcType=VARCHAR}, #{scenarioName,jdbcType=VARCHAR},
                #{dataBaseName,jdbcType=VARCHAR}, #{master,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR},
                #{alternativeColumn,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR},
                #{password,jdbcType=VARCHAR}, #{sourceScenarioId,jdbcType=VARCHAR},
                #{demandForecastVersionId,jdbcType=VARCHAR}, #{timePeriodGroupId,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <select id="tablesCopy" parameterType="java.lang.String" statementType="CALLABLE">
        {call proc_tables_copy(#{sourceDatabase}, #{targetDatabase})}
    </select>
    <select id="viewsCopy" parameterType="java.lang.String" statementType="CALLABLE">
        {call proc_views_copy(#{sourceDatabase,mode=IN,jdbcType=VARCHAR}, #{targetDatabase,mode=IN,jdbcType=VARCHAR})}
    </select>
    <insert id="sopDataCopy" parameterType="java.lang.String">
        {call proc_sop_data_copy(#{sourceDatabase}, #{targetDatabase}, #{timePeriodGroupId}, #{demandForecastVersionId})}
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.yhl.scp.ips.system.entity.Scenario">
        update auth_scenario
        set module_code                = #{moduleCode,jdbcType=VARCHAR},
            scenario_name              = #{scenarioName,jdbcType=VARCHAR},
            data_base_name             = #{dataBaseName,jdbcType=VARCHAR},
            master                     = #{master,jdbcType=VARCHAR},
            tenant_id                  = #{tenantId,jdbcType=VARCHAR},
            alternative_column         = #{alternativeColumn,jdbcType=VARCHAR},
            url                        = #{url,jdbcType=VARCHAR},
            user_name                  = #{userName,jdbcType=VARCHAR},
            password                   = #{password,jdbcType=VARCHAR},
            source_scenario_id         = #{sourceScenarioId,jdbcType=VARCHAR},
            demand_forecast_version_id = #{demandForecastVersionId,jdbcType=VARCHAR},
            time_period_group_id       = #{timePeriodGroupId,jdbcType=VARCHAR},
            remark                     = #{remark,jdbcType=VARCHAR},
            enabled                    = #{enabled,jdbcType=VARCHAR},
            creator                    = #{creator,jdbcType=VARCHAR},
            create_time                = #{createTime,jdbcType=TIMESTAMP},
            modifier                   = #{modifier,jdbcType=VARCHAR},
            modify_time                = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectByDataBaseName" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_scenario
        where data_base_name in
        <foreach collection="dataBaseNames" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectByTenantIdAndUserId" parameterType="java.lang.String" resultMap="VOResultMap">
        select distinct
            s.id,
            s.module_code,
            s.scenario_name,
            s.data_base_name,
            s.master,
            s.tenant_id,
            s.source_scenario_id,
            s.remark,
            s.enabled,
            s.creator,
            s.create_time,
            s.modifier,
            s.modify_time,
            'YES' as starred
        from auth_scenario s left join auth_rbac_user_scenario us on s.id = us.scenario_id
        <where>
            s.enabled = 'YES'
            <if test="tenantId != null and tenantId != ''">
                and s.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="userId != null and userId != ''">
                and us.user_id = #{userId,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY s.scenario_name ASC
    </select>
</mapper>