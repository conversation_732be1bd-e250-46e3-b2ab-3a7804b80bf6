<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.system.dao.AlgorithmLogDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.system.entity.AlgorithmLog">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="execution_number" jdbcType="VARCHAR" property="executionNumber"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="scenario" jdbcType="VARCHAR" property="scenario"/>
        <result column="calculation_type" jdbcType="VARCHAR" property="calculationType"/>
        <result column="ip_address" jdbcType="VARCHAR" property="ipAddress"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="kpi_data" jdbcType="VARCHAR" property="kpiData"/>
        <result column="calculate_module_id" jdbcType="VARCHAR" property="calculateModuleId"/>
        <result column="algorithm_version" jdbcType="VARCHAR" property="algorithmVersion"/>
        <result column="production_planner" jdbcType="VARCHAR" property="productionPlanner"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="line_group" jdbcType="VARCHAR" property="lineGroup"/>
        <result column="product_line" jdbcType="VARCHAR" property="productLine"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.yhl.scp.ips.system.entity.AlgorithmLog">
        <result column="fail_msg" jdbcType="LONGVARCHAR" property="failMsg"/>
        <result column="scenario_name" jdbcType="LONGVARCHAR" property="scenarioName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        module_code,
        status,
        start_time,
        end_time,
        execution_number,
        parent_id,
        scenario,
        calculation_type,
        ip_address,
        file_path,
        kpi_data,
        calculate_module_id,
        creator,
        create_time,
        modifier,
        modify_time,
        algorithm_version,
        production_planner,
        remark,
        line_group,
        product_line
    </sql>
    <sql id="Blob_Column_List">
        fail_msg
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from auth_algorithm_log
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectByStatus" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from auth_algorithm_log
        where status = #{status,jdbcType=VARCHAR}
    </select>
    <select id="selectByExecutionNumber" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from auth_algorithm_log
        where execution_number = #{executionNumber,jdbcType=VARCHAR}
    </select>

    <select id="selectByParams" resultMap="ResultMapWithBLOBs">
        select *
        from v_auth_algorithm_log
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>

    <select id="getLatestByScenario" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from auth_algorithm_log
        where scenario = #{scenario,jdbcType=VARCHAR}
        order by start_time desc
        limit 1
    </select>
    <select id="getScheduleLogsForGantt" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from auth_algorithm_log
        where scenario = #{scenario,jdbcType=VARCHAR}
        order by create_time desc
        limit 30
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from auth_algorithm_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.yhl.scp.ips.system.entity.AlgorithmLog">
        insert into auth_algorithm_log (id, module_code, status,
                                        start_time, end_time, execution_number,
                                        parent_id, scenario, calculation_type,
                                        ip_address, file_path, kpi_data, calculate_module_id,
                                        creator, create_time, modifier, modify_time,
                                        fail_msg, algorithm_version, production_planner,
                                        remark, line_group, product_line)
        values (#{id,jdbcType=VARCHAR}, #{moduleCode,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
                #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{executionNumber,jdbcType=VARCHAR},
                #{parentId,jdbcType=VARCHAR}, #{scenario,jdbcType=VARCHAR}, #{calculationType,jdbcType=VARCHAR},
                #{ipAddress,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR}, #{kpiData,jdbcType=VARCHAR},
                #{calculateModuleId,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP},
                #{failMsg,jdbcType=LONGVARCHAR}, #{algorithmVersion,jdbcType=VARCHAR},
                #{productionPlanner,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{lineGroup,jdbcType=VARCHAR}, #{productLine,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.yhl.scp.ips.system.entity.AlgorithmLog">
        update auth_algorithm_log
        set module_code         = #{moduleCode,jdbcType=VARCHAR},
            status              = #{status,jdbcType=VARCHAR},
            start_time          = #{startTime,jdbcType=TIMESTAMP},
            end_time            = #{endTime,jdbcType=TIMESTAMP},
            execution_number    = #{executionNumber,jdbcType=VARCHAR},
            parent_id           = #{parentId,jdbcType=VARCHAR},
            scenario            = #{scenario,jdbcType=VARCHAR},
            calculation_type    = #{calculationType,jdbcType=VARCHAR},
            ip_address          = #{ipAddress,jdbcType=VARCHAR},
            file_path           = #{filePath,jdbcType=VARCHAR},
            kpi_data            = #{kpiData,jdbcType=VARCHAR},
            calculate_module_id = #{calculateModuleId,jdbcType=VARCHAR},
            creator             = #{creator,jdbcType=VARCHAR},
            create_time         = #{createTime,jdbcType=TIMESTAMP},
            modifier            = #{modifier,jdbcType=VARCHAR},
            modify_time         = #{modifyTime,jdbcType=TIMESTAMP},
            fail_msg            = #{failMsg,jdbcType=LONGVARCHAR},
            algorithm_version   = #{algorithmVersion,jdbcType=VARCHAR},
            production_planner  = #{productionPlanner,jdbcType=VARCHAR},
            remark              = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.yhl.scp.ips.system.entity.AlgorithmLog">
        update auth_algorithm_log
        <set>
            <if test="moduleCode != null and moduleCode != ''">
                module_code = #{moduleCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            fail_msg = #{failMsg,jdbcType=LONGVARCHAR},
            <if test="executionNumber != null and executionNumber != ''">
                execution_number = #{executionNumber,jdbcType=VARCHAR},
            </if>
            <if test="scenario != null and scenario != ''">
                scenario = #{scenario,jdbcType=VARCHAR},
            </if>
            <if test="calculationType != null and calculationType != ''">
                calculation_type = #{calculationType,jdbcType=VARCHAR},
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                ip_address = #{ipAddress,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null and filePath != ''">
                file_path = #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="kpiData != null and kpiData != ''">
                kpi_data = #{kpiData,jdbcType=VARCHAR},
            </if>
            <if test="calculateModuleId != null and kpiData != ''">
                calculate_module_id = #{calculateModuleId,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != ''">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifier != null and modifier != ''">
                modifier = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="algorithmVersion != null and algorithmVersion != ''">
                algorithm_version = #{algorithmVersion,jdbcType=VARCHAR},
            </if>
            <if test="productionPlanner != null and productionPlanner != ''">
                production_planner = #{productionPlanner,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="cleanTempData">
        DELETE
        FROM sds_ord_work_order
        WHERE order_no like 'M%';
        DELETE
        FROM sds_ord_purchase_order
        WHERE order_no like 'M%';
        DELETE
        FROM sds_ord_transport_order
        WHERE order_no like 'M%';
        TRUNCATE TABLE sds_ord_plan_unit;
        TRUNCATE TABLE sds_ord_operation;
        TRUNCATE TABLE sds_ord_operation_input;
        TRUNCATE TABLE sds_ord_operation_output;
        TRUNCATE TABLE sds_ord_operation_input;
        TRUNCATE TABLE sds_ord_operation_resource;
        TRUNCATE TABLE sds_ord_operation_task;
        DELETE
        FROM sds_peg_demand
        WHERE demand_code like 'DMM%';
        TRUNCATE TABLE sds_peg_supply;
        TRUNCATE TABLE sds_peg_fulfillment;
    </update>

    <select id="selectByModule" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_algorithm_log
        where module_code = #{moduleCode,jdbcType=VARCHAR}
    </select>

    <select id="selectDailyLog" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_algorithm_log
        where parent_id is not null
    </select>

    <select id="selectByModuleAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_algorithm_log
        where module_code = #{code,jdbcType=VARCHAR}
          and creator = #{userId,jdbcType=VARCHAR}
          and status = 'SUCCESS'
        order by create_time desc
        limit 1
    </select>

    <select id="selectMpsIsRunning" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_algorithm_log
        where module_code = #{moduleCode,jdbcType=VARCHAR}
          and status in ('RUNNING', 'PULLING', 'WAITING')
    </select>

    <select id="selectTaskByUserIdAndModuleCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_algorithm_log
        where creator = #{userId,jdbcType=VARCHAR}
          and module_code = #{code,jdbcType=VARCHAR}
          and status in
        <foreach collection="runList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectTaskIsNotFail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_algorithm_log
        where module_code in
        <foreach collection="moduleCodeList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
          and status != 'FAIL'
          and status != 'SUCCESS'
    </select>

    <select id="selectByStatusList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_algorithm_log
        where status in
        <foreach collection="statusList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>