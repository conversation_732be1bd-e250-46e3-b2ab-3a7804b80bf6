package com.yhl.scp.ips.system.dao;

import com.yhl.scp.ips.system.entity.AlgorithmLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>AlgorithmLogDao</code>
 * <p>
 * AlgorithmLogDao
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-11 16:11:06
 */
public interface AlgorithmLogDao {
    int deleteByPrimaryKey(String id);

    int insert(AlgorithmLog record);

    AlgorithmLog selectByPrimaryKey(String id);


    int updateByPrimaryKeyWithBLOBs(AlgorithmLog record);

    int updateByPrimaryKey(AlgorithmLog record);

    List<AlgorithmLog> selectByParams(@Param("sortParam") String sortParam, @Param("queryCriteriaParam") String queryCriteriaParam);

    List<AlgorithmLog> selectByStatus(@Param("status") String status);

    AlgorithmLog selectByExecutionNumber(@Param("executionNumber") String executionNumber);

    /**
     * 根据场景获取算法最后一次执行记录
     *
     * @param scenario
     * @return
     */
    AlgorithmLog getLatestByScenario(String scenario);

    /**
     * 根据场景获取算法执行步骤集合
     *
     * @param scenario
     * @return
     */
    List<AlgorithmLog> getScheduleLogsForGantt(String scenario);

    /**
     * 清理算法临时数据
     */
    void cleanTempData();

    List<AlgorithmLog> selectByModule(@Param("moduleCode") String moduleCode);

    List<AlgorithmLog> selectDailyLog();

    AlgorithmLog selectByModuleAndUserId(@Param("userId") String userId, @Param("code") String code);

    List<AlgorithmLog> selectMpsIsRunning(@Param("moduleCode") String moduleCode);

    List<AlgorithmLog> selectTaskByUserIdAndModuleCode(@Param("userId") String userId, @Param("code") String code, @Param("runList") List<String> runList);

    List<AlgorithmLog> selectTaskIsNotFail(@Param("moduleCodeList") List<String> moduleCodeList);

    List<AlgorithmLog> selectByStatusList(@Param("statusList") List<String> statusList);
}