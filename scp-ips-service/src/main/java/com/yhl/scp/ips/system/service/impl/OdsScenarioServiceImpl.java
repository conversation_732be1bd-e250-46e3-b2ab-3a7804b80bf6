package com.yhl.scp.ips.system.service.impl;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DatabaseUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.rbac.service.UserService;
import com.yhl.scp.ips.system.dto.OdsScenarioDTO;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.service.IScenarioService;
import com.yhl.scp.ips.system.service.ScenarioService;
import com.yhl.scp.ips.system.vo.OdsScenarioVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>MpsScenarioServiceImpl</code>
 * <p>
 * ODS场景策略接口实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-13 09:52:14
 */
@Service
public class OdsScenarioServiceImpl implements IScenarioService<OdsScenarioDTO, OdsScenarioVO> {

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.driverClassName}")
    private String driver;

    @Value("${yhl.datasource.uri}")
    private String uri;

    @Value("${yhl.datasource.params}")
    private String databaseParams;

    @Resource
    private ScenarioService scenarioService;

    @Resource
    private UserService userService;

    @Override
    public BaseResponse<Void> doCreate(OdsScenarioDTO dto) {
        // 数据校验
        postRequestValidation(dto);
        // 查询源场景
        String sourceScenarioId = dto.getSourceScenarioId();
        Scenario sourceScenario = scenarioService.selectById(sourceScenarioId);
        String sourceDataBaseName = sourceScenario.getDataBaseName();
        String moduleCode = sourceScenario.getModuleCode();
        // 查询目标场景
        String targetScenarioId = dto.getTargetScenarioId();
        String tenantId = SystemHolder.getTenantId();
        Scenario targetScenario = scenarioService.selectByName(targetScenarioId, tenantId, moduleCode);
        // 初始化变量
        String scenarioId = UUIDUtil.getUUID();
        String targetDataBaseName;
        if (null == targetScenario) {
            // 新建场景
            targetDataBaseName = SopScenarioServiceImpl.generateTargetDataBaseName(moduleCode);
            createScenario(sourceScenario, scenarioId, targetScenarioId, targetDataBaseName);
            // 建库DDL
            DatabaseUtils.createDatabase(url, username, password, driver, targetDataBaseName);
            userService.doInsertUserScenario(SystemHolder.getUserId(), scenarioId);
        } else {
            // 覆盖场景
            targetDataBaseName = targetScenario.getDataBaseName();
            updateScenario(targetScenario, sourceScenarioId);
        }
        // 拷贝表
        scenarioService.doTablesCopy(sourceDataBaseName, targetDataBaseName);
        // 拷贝视图
        scenarioService.doViewsCopy(sourceDataBaseName, targetDataBaseName);
        return BaseResponse.success();
    }

    /**
     * 请求校验
     *
     * @param dto S&OP场景DTO
     */
    private void postRequestValidation(OdsScenarioDTO dto) {
        // 非空校验
        String sourceScenarioId = dto.getSourceScenarioId();
        String targetScenarioId = dto.getTargetScenarioId();
        if (StringUtils.isEmpty(sourceScenarioId)) {
            throw new BusinessException("源场景不能为空");
        }
        if (StringUtils.isEmpty(targetScenarioId)) {
            throw new BusinessException("目标场景不能为空");
        }
        // 不相等校验
        if (StringUtils.equals(sourceScenarioId, targetScenarioId)) {
            throw new BusinessException("目标场景不能是源场景");
        }
    }

    /**
     * 新增场景
     *
     * @param sourceScenario 源场景
     * @param scenarioId     场景ID
     * @param scenarioName   场景名
     * @param databaseName   数据库名
     */
    private void createScenario(Scenario sourceScenario, String scenarioId, String scenarioName, String databaseName) {
        String userId = SystemHolder.getUserId();
        Date date = new Date();
        String tenantId = SystemHolder.getTenantId();
        Map<String, Object> params = new HashMap<>(4);
        params.put("moduleCode", sourceScenario.getModuleCode());
        params.put("tenantId", tenantId);
        List<Scenario> scenarios = scenarioService.selectByParams(params);
        List<String> databaseNames = scenarios.stream().map(Scenario::getDataBaseName).distinct().collect(Collectors.toList());
        Scenario scenario = new Scenario();
        scenario.setId(scenarioId);
        scenario.setModuleCode(sourceScenario.getModuleCode());
        scenario.setScenarioName(scenarioName);
        scenario.setDataBaseName(databaseName);
        // 校验数据库名
        while (databaseNames.contains(databaseName)) {
            databaseName = SopScenarioServiceImpl.generateTargetDataBaseName(sourceScenario.getModuleCode());
        }
        scenario.setUrl(uri + databaseName + databaseParams);
        scenario.setTenantId(tenantId);
        scenario.setSourceScenarioId(sourceScenario.getId());
        scenario.setMaster(YesOrNoEnum.NO.getCode());
        scenario.setEnabled(YesOrNoEnum.YES.getCode());
        scenario.setCreator(userId);
        scenario.setCreateTime(date);
        scenario.setModifier(userId);
        scenario.setModifyTime(date);
        scenarioService.doCreate(scenario);
    }

    /**
     * 修改场景
     *
     * @param targetScenario   目标场景
     * @param sourceScenarioId 源场景ID
     */
    private void updateScenario(Scenario targetScenario, String sourceScenarioId) {
        targetScenario.setSourceScenarioId(sourceScenarioId);
        targetScenario.setModifier(SystemHolder.getUserId());
        targetScenario.setModifyTime(new Date());
        scenarioService.doUpdate(targetScenario);
    }

    @Override
    public BaseResponse<Void> doUpdate(OdsScenarioDTO dto) {
        // 数据校验
        postRequestValidation(dto);
        return BaseResponse.success();
    }

    @Override
    public OdsScenarioVO selectByPrimaryKey(String id) {
        Scenario scenario = scenarioService.selectById(id);
        OdsScenarioVO sopScenarioVO = OdsScenarioVO.builder().build();
        BeanUtils.copyProperties(scenario, sopScenarioVO);
        sopScenarioVO.setTargetScenarioId(scenario.getId());
        return sopScenarioVO;
    }

}