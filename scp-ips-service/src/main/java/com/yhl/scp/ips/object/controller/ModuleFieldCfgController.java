package com.yhl.scp.ips.object.controller;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.config.ModuleObjectProperties;
import com.yhl.scp.ips.object.entity.ModuleFieldCfg;
import com.yhl.scp.ips.object.service.ModuleFieldCfgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ModuleFieldCfgController</code>
 * <p>
 * 对象字段（是否显示）配置
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-27 14:03:43
 */
@Api(tags = "对象字段（是否显示）配置")
@RestController
@RequestMapping("/modulefieldcfg")
public class ModuleFieldCfgController extends BaseController {

    @Autowired
    private ModuleObjectProperties moduleObjectProperties;

    @Autowired
    private ModuleFieldCfgService moduleFieldCfgService;

    @ApiOperation("需要配置产品模块")
    @GetMapping(value = "/modules")
    public BaseResponse modules() {
        List<LabelValue> labelValues = new ArrayList<>();
        List<String> moduleCodes = moduleObjectProperties.getMasters();
        for (String moduleCode : moduleCodes) {
            LabelValue labelValue = new LabelValue<>();
            labelValue.setLabel(EnumUtils.getDescByCode(SystemModuleEnum.class, moduleCode));
            labelValue.setValue(moduleCode);
            labelValues.add(labelValue);
        }
        return new BaseResponse<>(true, labelValues);
    }

    @ApiOperation("获取对象列表1")
    @GetMapping(value = "/objects1")
    public BaseResponse objects1(@RequestParam("moduleCode") String moduleCode) throws ClassNotFoundException {
        if (SystemModuleEnum.SOP.getCode().equals(moduleCode)) {
            moduleCode = "SOP";
        }
        String objectTypeEnumKey = moduleObjectProperties.getObjectType().get(moduleCode);
        Class clazz = Class.forName(objectTypeEnumKey);
        return new BaseResponse<>(true, EnumUtils.getEnumValueList(clazz));
    }

    @ApiOperation("获取对象列表")
    @GetMapping(value = "/objects")
    public BaseResponse<List<LabelValue<String>>> objects(@RequestParam("moduleCode") String moduleCode) {
        if (SystemModuleEnum.SOP.getCode().equals(moduleCode)) {
            moduleCode = "SOP";
        }
        List<LabelValue<String>> dataList = ObjectTypeEnum.getDropdownByModuleCode(moduleCode);
        return BaseResponse.success(dataList);
    }

    @ApiOperation("对象属性列表(包含设置信息)")
    @GetMapping(value = "/objects/{targetModule}/{moduleCode}/{objectType}")
    public BaseResponse fields(
            @PathVariable("targetModule") String targetModule,
            @PathVariable("moduleCode") String moduleCode,
            @PathVariable("objectType") String objectType) throws ClassNotFoundException {
        // ObjectType枚举类路径
        String objectTypeEnumKey = moduleObjectProperties.getObjectType().get(moduleCode.replace("&", ""));
        Class objectTypeClazz = Class.forName(objectTypeEnumKey);

        // 对象路径
        String objectName = EnumUtils.getMappingValueByCode(objectTypeClazz, objectType);
        Class objectClazz = Class.forName(objectName);
        List<Field> fields = new ArrayList<>(Arrays.asList(objectClazz.getDeclaredFields()));
        List<ModuleFieldCfg> moduleFieldCfgs = new ArrayList<>();

        // 已配置的对象字段信息
        List<ModuleFieldCfg> cfgedFields = moduleFieldCfgService.selectByModuleCodeAndObjectType(moduleCode, objectType);
        cfgedFields = cfgedFields.stream().filter(item -> targetModule.equals(item.getTargetModule()) && YesOrNoEnum.YES.getCode().equals(item.getHidden())).collect(Collectors.toList());
        Map<String, ModuleFieldCfg> fieldCfgMap = cfgedFields.stream().collect(Collectors.toMap(ModuleFieldCfg::getFieldCode, v -> v));
        for (Field field : fields) {
            if ("serialVersionUID".equals(field.getName())) {
                continue;
            }

            ModuleFieldCfg cfgedField = fieldCfgMap.get(field.getName());

            ModuleFieldCfg fieldCfg = new ModuleFieldCfg();
            fieldCfg.setFieldCode(field.getName());
            FieldInterpretation fieldInterpretation = field.getAnnotation(FieldInterpretation.class);
            fieldCfg.setFieldName(null == fieldInterpretation ? field.getName() : fieldInterpretation.value());
            fieldCfg.setObjectType(objectType);
            fieldCfg.setModuleCode(moduleCode);
            // cfgedField为null 说明没有设置不显示
            fieldCfg.setHidden(null == cfgedField ? YesOrNoEnum.NO.getCode() : cfgedField.getHidden());
            moduleFieldCfgs.add(fieldCfg);
        }
        return new BaseResponse<>(true, moduleFieldCfgs);
    }

    @ApiOperation("保存对象属性显示设置")
    @PostMapping(value = "")
    public BaseResponse save(@RequestBody List<ModuleFieldCfg> moduleFieldCfgs) {
        moduleFieldCfgService.doSave(moduleFieldCfgs);
        return new BaseResponse<>(true, null);
    }

    @ApiOperation("模块对象隐藏字段列表")
    @GetMapping(value = "/targetmodule/objectfields")
    public BaseResponse targetModuleHiddenFields() throws ClassNotFoundException {
        List<ModuleFieldCfg> cfgedFields = moduleFieldCfgService.selectHiddenFieldsByTargetModule(SystemHolder.getModuleCode());
        Map<String, List<ModuleFieldCfg>> objectFieldsMap = cfgedFields.stream().collect(Collectors.groupingBy(ModuleFieldCfg::getObjectType));
        return new BaseResponse<>(true, objectFieldsMap);
    }


}
