package com.yhl.scp.ips.rbac.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.i18n.service.UserDefaultLanguageService;
import com.yhl.scp.common.enums.UserTypeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.object.entity.ObjectI18n;
import com.yhl.scp.ips.object.service.ObjectI18nService;
import com.yhl.scp.ips.rbac.dao.ResourceDao;
import com.yhl.scp.ips.rbac.dao.RoleDao;
import com.yhl.scp.ips.rbac.dao.RoleResourceDao;
import com.yhl.scp.ips.rbac.entity.Resource;
import com.yhl.scp.ips.rbac.entity.Role;
import com.yhl.scp.ips.rbac.entity.RoleResource;
import com.yhl.scp.ips.rbac.service.ResourceService;
import com.yhl.scp.ips.rbac.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>ResourceServiceImpl</code>
 * <p>
 * ResourceServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Slf4j
@Service
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    private ResourceDao resourceDao;
    @Autowired
    private RoleResourceDao roleResourceDao;
    @Autowired
    private ObjectI18nService objectI18nService;
    @Autowired
    private RoleService roleService;

    @Autowired
    private UserDefaultLanguageService defaultLanguageService;

    @Autowired
    private RoleDao roleDao;


    @Override
    public BaseResponse doCreate(Resource resource) {

        if (StringUtils.isEmpty(resource.getResourceType())) {
            return BaseResponse.error("资源类型不能为空:菜单/控件");
        }
        // 租户创建菜单
        if (StringUtils.isNotEmpty(SystemHolder.getTenantId())) {
            resource.setTenantId(SystemHolder.getTenantId());
        }
        if (StringUtils.isEmpty(resource.getId())) {
            resource.setId(UUIDUtil.getUUID());
        }
        Resource parentResource = resourceDao.selectByPrimaryKey(resource.getParentId());
        if (StringUtils.isEmpty(parentResource.getParentId())) {
            return BaseResponse.error("用户不能创建二级菜单");
        }
        resource.setModuleCode(parentResource.getModuleCode());
        resource.setEnabled(YesOrNoEnum.YES.getCode());
        resourceDao.insert(resource);
        return BaseResponse.success();
    }

    @Override
    public BaseResponse doUpdate(Resource resource) {

        if (StringUtils.isEmpty(resource.getResourceType())) {
            return BaseResponse.error("资源类型不能为空:菜单/控件");
        }

        Resource checkResource = resourceDao.selectByPrimaryKey(resource.getId());
        if (!UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType()) && StringUtils.isEmpty(checkResource.getTenantId())) {
            return BaseResponse.error("非租户私有菜单不允许租户修改");
        }
        Resource parentResource = resourceDao.selectByPrimaryKey(resource.getParentId());
        if (!StringUtils.isEmpty(parentResource.getModuleCode())) {
            resource.setModuleCode(parentResource.getModuleCode());
        } else {
            resource.setModuleCode(checkResource.getModuleCode());
        }
        resource.setEnabled(YesOrNoEnum.YES.getCode());
        resourceDao.updateByPrimaryKey(resource);
        return BaseResponse.success("菜单修改成功");
    }

    @Override
    public BaseResponse doDelete(String resourceId) {
        Resource checkResource = resourceDao.selectByPrimaryKey(resourceId);
        if (!UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType()) && StringUtils.isEmpty(checkResource.getTenantId())) {
            return BaseResponse.error("非租户私有菜单不允许租户删除");
        }
        roleResourceDao.deleteByResourceId(resourceId);
        Resource resource = resourceDao.selectByPrimaryKey(resourceId);
        resource.setEnabled(YesOrNoEnum.NO.getCode());
        resourceDao.updateByPrimaryKey(resource);

        return BaseResponse.success();
    }

    @Override
    public Resource selectById(String resourceId) {
        return resourceDao.selectByPrimaryKey(resourceId);
    }

    @Override
    public List<Resource> selectPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return resourceDao.selectByCondition(sortParam, queryCriteriaParam, SystemHolder.getTenantId());
    }

    @Override
    public List<Resource> selectByUserId(String userId) {

        if (UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType())) {
            return resourceDao.selectForSystemAdmin();
        }

        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("tenantId", SystemHolder.getTenantId());
        // 有权限的菜单资源
        List<Resource> resources = resourceDao.selectByParams(params);
        List<Role> roles = roleService.selectByUserId(userId);
        log.info("用户角色有： {}, 大小有 {}", roles.stream().map(Role::getRoleName).collect(Collectors.joining(", ")), roles.size());
        List<Resource> roleWidgets = selectWidgetResourcesByRoleIds(roles.stream().map(Role::getId).collect(Collectors.toList()));
        // 角色对应的按钮组件id
        List<String> resourceIds = roleWidgets.stream().map(Resource::getId).collect(Collectors.toList());
        log.info("角色对应的按钮组件id 是： {}, 大小有 {}", resourceIds, resourceIds.size());
        // 有权限菜单下所有按钮组件
        List<Resource> resourceWidgets = selectWidgetResourcesByParentIds(resources.stream().map(Resource::getId).collect(Collectors.toList()));
        log.info("用户角色有： {}, 大小有 {}", resourceWidgets.stream().map(Resource::toString).collect(Collectors.joining(", ")), resourceWidgets.size());
        for (Resource resource : resourceWidgets) {
            if (resourceIds.contains(resource.getId())) {
                resource.setWidgetAuthority(YesOrNoEnum.YES.getCode());
            } else {
                resource.setWidgetAuthority(YesOrNoEnum.NO.getCode());
            }
            resources.add(resource);
        }

//        String language = defaultLanguageService.getDefaultLanguageByUserId(SystemHolder.getUserId());
//        log.info("1 selectByUserId language is " + language);
//	    if(StringUtils.isEmpty(language)) {
//	    	language = LocaleContextHolder.getLocale().toString();
//	    	log.info("2 selectByUserId language is " + language);
//	    }
        List<ObjectI18n> objectI18ns = objectI18nService.selectByObjectType("RESOURCE");
        Map<String, ObjectI18n> i18nMap = objectI18ns.stream().collect(Collectors.toMap(item -> item.getObjectId() + item.getLanguageCode(), v -> v));
        for (Resource resource : resources) {
            ObjectI18n objectI18n = i18nMap.get(resource.getId() + SystemHolder.getLanguage());
            if (null != objectI18n && StringUtils.isNotEmpty(objectI18n.getDesc())) {
                resource.setResourceName(objectI18n.getDesc());
            }
        }
        return resources;
    }

    private List<Resource> selectWidgetResourcesByRoleIds(List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        return resourceDao.selectWidgetResourcesByRoleIds(roleIds);
    }

    private List<Resource> selectWidgetResourcesByParentIds(List<String> parentIds) {
        if (CollectionUtils.isEmpty(parentIds)) {
            return new ArrayList<>();
        }
        return resourceDao.selectWidgetResourcesByParentIds(parentIds);
    }

    @Override
    public List<Resource> selectTreeById(String id) {
        return resourceDao.selectTreeById(id, SystemHolder.getTenantId());
    }

    @Override
    public List<Resource> selectAll() {
        return resourceDao.selectAll();
    }

    @Override
    public List<Resource> selectByIds(List<String> resourceIds) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return new ArrayList<>();
        }
        return resourceDao.selectByIds(resourceIds);
    }

    @Override
    public void completion() {
        //1、获取所有角色
        List<Role> roles = roleDao.selectAllRole();
        log.info("当前角色{}个", roles.size());
        //2、获取所有菜单
        List<Resource> resources = resourceDao.selectAll();
        Map<String, String> sonToParentIdMap = new HashMap<>();
        for (Resource resource : resources) {
            sonToParentIdMap.put(resource.getId(), resource.getParentId());
        }
        roles.forEach(role -> {
            //3、获取该角色对应的资源
            List<RoleResource> roleResources = roleResourceDao.selectByRoleId(role.getId());
            if (roleResources.isEmpty()){
                log.info("角色{}没有绑定资源", role.getRoleName());
                return;
            }
            //获取该角色对应的菜单id集合
            Set<String> currentBindingIds = roleResources.stream()
                    .map(RoleResource::getResourceId)
                    .collect(Collectors.toSet());

            //4、分类，判断有效资源和无效资源
            Set<String> validSet = new HashSet<>();
            Set<String> unValidSet = new HashSet<>();
            currentBindingIds.forEach(resourceId -> {
                if (canReachTop(resourceId, "1", sonToParentIdMap)) {
                    validSet.add(resourceId);
                } else {
                    unValidSet.add(resourceId);
                }
            });
            //5、对有效资源补齐所有父类
            Set<String> allParentIdsToBind = new HashSet<>();
            validSet.forEach(item -> collectAllParents(item, allParentIdsToBind, sonToParentIdMap));
            log.info("或许补齐的资源个数{}", allParentIdsToBind.size());
            //去除已经存在的
            allParentIdsToBind.removeIf(currentBindingIds::contains);
            log.info("补齐的资源{}个,角色id为{}，资源id为{}", allParentIdsToBind.size(), role.getId(), allParentIdsToBind);
            //批量插入父资源
            for (String parentId : allParentIdsToBind) {
                RoleResource roleResource = new RoleResource();
                roleResource.setRoleId(role.getId());
                roleResource.setResourceId(parentId);
                roleResourceDao.insert(roleResource);
            }
            //6、对无效资源进行删除
            log.info("删除的资源{}个，角色id为{}，删除资源id为{}", unValidSet.size(), role.getId(), unValidSet);
            for (String resourceId : unValidSet) {
                roleResourceDao.deleteByTwoId(role.getId(), resourceId);
            }
        });
    }

    /**
     * 判断某个资源是否能向上递归到达顶层资源（如 id="1"）
     */
    private boolean canReachTop(String resourceId, String topId, Map<String, String> sonToParentIdMap) {
        if (resourceId == null || resourceId.isEmpty()) {
            return false;
        }
        if (resourceId.equals(topId)) {
            return true;
        }
        String parentId = sonToParentIdMap.get(resourceId);
        return canReachTop(parentId, topId, sonToParentIdMap); // 递归
    }

    /**
     * 收集从当前资源到顶层的所有父资源 ID（不包含自己，只收集父级）
     */
    private void collectAllParents(String resourceId, Set<String> parentIds, Map<String, String> sonToParentIdMap) {
        String parentId = sonToParentIdMap.get(resourceId);
        if (parentId != null && !parentId.isEmpty()) {
            parentIds.add(parentId);
            collectAllParents(parentId, parentIds, sonToParentIdMap);
        }
    }

}
