package com.yhl.scp.ips.system.dao;

import com.yhl.scp.ips.system.entity.Scenario;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>ScenarioDao</code>
 * <p>
 * ScenarioDao
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-11 16:12:47
 */
public interface ScenarioDao {
    int deleteByPrimaryKey(String id);

    int insert(Scenario record);

    Scenario selectByPrimaryKey(String id);

    Scenario selectByName(@Param("scenarioName") String scenarioName, @Param("tenantId") String tenantId, @Param("moduleCode") String moduleCode);

    int updateByPrimaryKey(Scenario record);

    List<Scenario> selectAll();

    List<Scenario> selectByTenantId(String tenantId);

    List<Scenario> selectByPage(@Param("sortParam") String sortParam, @Param("queryCriteriaParam") String queryCriteriaParam,
                                @Param("tenantId") String tenantId);

    List<Scenario> selectByParams(@Param("params") Map<String, Object> params);

    void tablesCopy(@Param("sourceDatabase") String sourceDatabase, @Param("targetDatabase") String targetDatabase);

    void viewsCopy(@Param("sourceDatabase") String sourceDatabase, @Param("targetDatabase") String targetDatabase);

    void sopDataCopy(@Param("sourceDatabase") String sourceDatabase, @Param("targetDatabase") String targetDatabase,
                     @Param("timePeriodGroupId") String timePeriodGroupId, @Param("demandForecastVersionId") String demandForecastVersionId);
//    void viewsCopy(@Param("params") Map<String,Object> params);

    List<Scenario> selectByDataBaseName(@Param("dataBaseNames") List<String> scenarios);

    List<Scenario> selectByTenantIdAndUserId(@Param("tenantId") String tenantId, @Param("userId") String userId);

}