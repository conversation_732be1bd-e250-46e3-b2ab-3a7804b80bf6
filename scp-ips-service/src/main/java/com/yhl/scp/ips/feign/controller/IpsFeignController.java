package com.yhl.scp.ips.feign.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.common.vo.LabelValueX;
import com.yhl.scp.ips.algorithm.convertor.AlgorithmStepLogConvertor;
import com.yhl.scp.ips.algorithm.dto.AlgorithmStepLogDTO;
import com.yhl.scp.ips.algorithm.infrastructure.dao.AlgorithmStepLogDao;
import com.yhl.scp.ips.algorithm.infrastructure.po.AlgorithmStepLogPO;
import com.yhl.scp.ips.algorithm.service.AlgorithmServerService;
import com.yhl.scp.ips.algorithm.vo.AlgorithmServerVO;
import com.yhl.scp.ips.bpm.dto.BpmVariablesSetDTO;
import com.yhl.scp.ips.collection.service.CollectionValueService;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.conf.service.UrlTableService;
import com.yhl.scp.ips.conf.vo.UrlTableVO;
import com.yhl.scp.ips.config.CollectionCacheInit;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.log.dto.ExcelImportLogDTO;
import com.yhl.scp.ips.log.service.ExcelImportLogService;
import com.yhl.scp.ips.rbac.dao.RoleDao;
import com.yhl.scp.ips.rbac.vo.WorkBenchResourceVO;
import com.yhl.scp.ips.redisKeyManage.infrastructure.dao.RedisKeyManageDao;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO;
import com.yhl.scp.ips.system.dao.AlgorithmLogDao;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.service.AlgorithmLogService;
import com.yhl.scp.ips.system.service.FavoriteService;
import com.yhl.scp.ips.system.service.ScenarioService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>IpsFeignController</code>
 * <p>
 * IpsFeign控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-11-06 18:55:14
 */
@Slf4j
@Api(tags = "Feign")
@RestController
public class IpsFeignController implements IpsFeign {

    @Resource
    AlgorithmLogDao algorithmLogDao;
    @Resource
    private AlgorithmLogService algorithmLogService;
    @Resource
    private ScenarioService scenarioService;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private TaskService taskService;
    @Resource
    private ExcelImportLogService excelImportLogService;
    @Resource
    private AlgorithmServerService algorithmServerService;
    @Resource
    private CollectionCacheInit collectionCacheInit;
    @Resource
    private AlgorithmStepLogDao algorithmStepLogDao;
    @Resource
    private FavoriteService favoriteService;
    @Resource
    private RedisKeyManageDao redisKeyManageDao;
    @Resource
    private UrlTableService urlTableService;
    @Resource
    private RoleDao roleDao;
    @Resource
    private CollectionValueService collectionValueService;


    @Override
    public BaseResponse createAlgorithmLog(AlgorithmLog algorithmLog) {
        algorithmLogService.doInsert(algorithmLog);
        return BaseResponse.success("", algorithmLog.getId());
    }

    @Override
    public BaseResponse createAlgorithmLogNew(AlgorithmLog algorithmLogMap) {
        AlgorithmLog algorithmLog = new AlgorithmLog();
        String taskId = UUIDUtil.getUUID();
        algorithmLog.setId(taskId);
        algorithmLog.setCalculationType(null);
        algorithmLog.setStartTime(new Date());
        algorithmLog.setModuleCode(algorithmLogMap.getModuleCode());
        algorithmLog.setCreateTime(new Date());
        algorithmLog.setExecutionNumber(taskId);
        algorithmLog.setModifier(SystemHolder.getUserId());
        algorithmLog.setModifyTime(new Date());
        algorithmLog.setStatus(AlgorithmLogStatusEnum.WAITING.getCode());
        algorithmLog.setFilePath(null);
        algorithmLog.setScenario(SystemHolder.getScenario());
        algorithmLog.setIpAddress(null);
        algorithmLog.setCalculateModuleId(algorithmLogMap.getCalculateModuleId());
        algorithmLog.setAlgorithmVersion(algorithmLogMap.getAlgorithmVersion());
        algorithmLog.setCreator(algorithmLogMap.getCreator());
        algorithmLog.setProductionPlanner(algorithmLogMap.getProductionPlanner());
        algorithmLog.setRemark(algorithmLogMap.getRemark());
        algorithmLog.setLineGroup(algorithmLogMap.getLineGroup());
        algorithmLog.setProductLine(algorithmLogMap.getProductLine());
        algorithmLogService.doInsert(algorithmLog);
        return BaseResponse.success("", algorithmLog.getId());
    }

    private long checkRunQty(List<AlgorithmLog> algorithmLogs) {
        if (CollectionUtils.isEmpty(algorithmLogs)) {
            return 0L;
        }

        String userId = SystemHolder.getUserId();
        return algorithmLogs.stream()
                .filter(log -> userId.equals(log.getCreator()))
                .filter(log -> AlgorithmLogStatusEnum.RUNNING.getCode().equals(log.getStatus())
                        || AlgorithmLogStatusEnum.PULLING.getCode().equals(log.getStatus()))
                .count();
    }

    @Override
    public BaseResponse updateAlgorithmLog(AlgorithmLog algorithmLog) {
        algorithmLogService.doUpdate(algorithmLog);
        return new BaseResponse(true, "更新log成功");
    }

    @Override
    public BaseResponse<AlgorithmLog> selectAlgorithmLogById(String id) {
        return new BaseResponse(Boolean.TRUE, algorithmLogService.selectById(id));
    }

    /**
     * 根据场景获取算法最后一次执行记录
     *
     * @param scenario 场景
     * @return
     */
    @Override
    public AlgorithmLog getLatestByScenario(String scenario) {
        return algorithmLogService.getLatestByScenario(scenario);
    }

    /**
     * 根据场景获取算法执行步骤集合
     *
     * @param scenario
     * @return
     */
    @Override
    public List<AlgorithmLog> getScheduleLogsForGantt(String scenario) {
        return algorithmLogService.getScheduleLogsForGantt(scenario);
    }

    @Override
    public Scenario getByScenario(String moduleCode, String scenario) {
        List<Scenario> scenarios = scenarioService.selectByParams(ImmutableMap.of("moduleCode", moduleCode, "dataBaseName", scenario));
        if (CollectionUtils.isNotEmpty(scenarios)) {
            return scenarios.get(0);
        }
        return null;
    }

    @Override
    public List<Scenario> getByScenarios(List<String> scenarios) {
        return scenarioService.selectByScenarios(scenarios);
    }

    @Override
    public BaseResponse<List<LabelValueX<String>>> scenariosList(String tenant, String module) {
        if (StringUtils.isBlank(tenant)) {
            return BaseResponse.error("租户不能为空");
        }
        if (StringUtils.isBlank(module)) {
            return BaseResponse.error("模块不能为空");
        }
        Map<String, Object> params = ImmutableMap.of("tenantId", tenant, "moduleCode", module,
                "master", YesOrNoEnum.NO.getCode());
        return new BaseResponse<>(true, scenarioService.selectByParams(params).stream().map(item ->
                new LabelValueX<>(item.getScenarioName(), item.getDataBaseName(), item.getTimePeriodGroupId(),
                        item.getDemandForecastVersionId())).collect(Collectors.toList()));
    }

    @Override
    public void setBpmVariables(BpmVariablesSetDTO bpmVariablesSetDTO) {
        String processInstanceId = bpmVariablesSetDTO.getProcessInstanceId();
        String taskId = bpmVariablesSetDTO.getTaskId();
        assert StringUtils.isNotBlank(processInstanceId) || StringUtils.isNotBlank(taskId);
        String variableKey = bpmVariablesSetDTO.getVariableKey();
        Object variableValue = bpmVariablesSetDTO.getVariableValue();
        Map<String, Object> variableValues = bpmVariablesSetDTO.getVariableMap();
        if (StringUtils.isNotBlank(taskId)) {
            if (StringUtils.isNotBlank(variableKey) && Objects.nonNull(variableValue)) {
                taskService.setVariable(taskId, variableKey, variableValue);
            } else if (MapUtils.isNotEmpty(variableValues)) {
                taskService.setVariables(taskId, variableValues);
            }
        } else {
            if (StringUtils.isNotBlank(variableKey) && Objects.nonNull(variableValue)) {
                runtimeService.setVariable(processInstanceId, variableKey, variableValue);
            } else if (MapUtils.isNotEmpty(variableValues)) {
                runtimeService.setVariables(processInstanceId, variableValues);
            }
        }
    }

    @Override
    public Object getBpmVariables(String processInstanceId, String taskId, String variableKey) {
        if (StringUtils.isNotBlank(taskId)) {
            if (StringUtils.isNotBlank(variableKey)) {
                return taskService.getVariable(taskId, variableKey);
            } else {
                return taskService.getVariables(taskId);
            }
        } else {
            if (StringUtils.isNotBlank(variableKey)) {
                return runtimeService.getVariable(processInstanceId, variableKey);
            } else {
                return runtimeService.getVariables(processInstanceId);
            }
        }
    }

    @Override
    public void cleanTempData() {
        algorithmLogService.cleanTempData();
    }

    @Override
    public void createExcelImportLog(List<ExcelImportLogDTO> list) {
        excelImportLogService.doCreateBatch(list);
    }

    @Override
    public List<CollectionValueVO> getByCollectionCode(String collection) {
        // List<CollectionValueVO> collectionValueVOList = new ArrayList<>();
        // Map<String, Object> params = new HashMap<>(4);
        // params.put("collectionCode", collection);
        // params.put("tenantId", SystemHolder.getTenantId());
        // List<CollectionManagementVO> collectionManagements = collectionManagementService.selectByParams(params);
        //
        // if (CollectionUtils.isNotEmpty(collectionManagements)) {
        //     String collectionId = collectionManagements.get(0).getId();
        //     collectionValueVOList = collectionValueService.selectByParams(ImmutableMap.of("collectionId", collectionId));
        // }
        BaseResponse<List<CollectionValueVO>> byCollectionCode = collectionCacheInit.getByCollectionCode(collection);
        return byCollectionCode.getData();
    }

    @Override
    public void saveKpiData(String logId, String json) {
        AlgorithmLog algorithmLog = algorithmLogService.selectById(logId);
        algorithmLog.setKpiData(json);
        algorithmLogService.doUpdate(algorithmLog);
    }

    @Override
    public List<AlgorithmServerVO> selectAllAlgorithmServerVOS() {
        return algorithmServerService.selectAll();
    }

    @Override
    public List<AlgorithmLog> selectAlgorithmLogsByStatus(String status) {
        return algorithmLogService.selectByStatus(status);
    }

    @Override
    public AlgorithmLog getLogById(String logId) {
        return algorithmLogService.selectById(logId);
    }

    @Override
    public boolean operationCheck(List<String> arrayList) {
        List<AlgorithmLog> mpsIsRunning = algorithmLogDao.selectMpsIsRunning(ModuleCodeEnum.MPS.getCode());
        if (arrayList.isEmpty()) {
            return CollectionUtils.isNotEmpty(mpsIsRunning);
        } else {
            List<String> combinedList = ListUtils.newArrayList();
            mpsIsRunning.stream().forEach(item -> {
                if (StringUtils.isNotBlank(item.getRemark())) {
                    combinedList.addAll(JSON.parseArray(item.getRemark(), String.class));
                }
            });
            List<String> interSection = CollectionUtils.getInterSection(combinedList, arrayList);
            return CollectionUtils.isNotEmpty(interSection);
        }
    }

    @Override
    public boolean adJustPlanCheck(String userId) {
        List<AlgorithmLog> algorithmLogs = algorithmLogDao.selectTaskByUserIdAndModuleCode(userId,
                ModuleCodeEnum.AMS.getCode(), ListUtil.of(AlgorithmLogStatusEnum.RUNNING.getCode()));
        return algorithmLogs.isEmpty();
    }

    @Override
    public List<AlgorithmLog> selectTaskIsNotFail(List<String> moduleCodeList) {
        return algorithmLogDao.selectTaskIsNotFail(moduleCodeList);
    }

    @Override
    public void handleRunningTask() {
        List<String> module = ListUtil.of(ModuleCodeEnum.AMS.getCode(), ModuleCodeEnum.MPS.getCode(), ModuleCodeEnum.FEEDBACK.getCode());
        List<AlgorithmLog> algorithmLogs = algorithmLogDao.selectByStatusList(ListUtil.of(AlgorithmLogStatusEnum.RUNNING.getCode(),
                AlgorithmLogStatusEnum.WAITING.getCode(), AlgorithmLogStatusEnum.PULLING.getCode()));
        if (CollectionUtils.isNotEmpty(algorithmLogs)) {
            for (AlgorithmLog algorithmLog : algorithmLogs) {
                String moduleCode = algorithmLog.getModuleCode();
                if (!module.contains(moduleCode)) {
                    continue;
                }
                algorithmLog.setStatus(AlgorithmLogStatusEnum.FAIL.getCode());
                algorithmLog.setEndTime(new Date());
                algorithmLog.setFailMsg("sds-mps服务异常，运行终止");
                algorithmLogService.doUpdate(algorithmLog);
            }
        }
    }

    @Override
    public void batchSaveStepLog(List<AlgorithmStepLogDTO> list) {
        log.info("保存运行日志行数：{}", list.size());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<AlgorithmStepLogPO> algorithmStepLogPOS = AlgorithmStepLogConvertor.INSTANCE.dto2Pos(list);
        algorithmStepLogDao.insertBatch(algorithmStepLogPOS);
    }

    @Override
    public List<RedisKeyManageVO> selectRedisKeyManageByModuleCode(String moduleCode) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("moduleCode", moduleCode);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        return redisKeyManageDao.selectVOByParams(params);
    }

    @Override
    public List<WorkBenchResourceVO> selectWorkBenchResource(String userId) {
        return favoriteService.selectWorkBenchResource(userId);
    }

    @Override
    public String getTableNameByUrlTable(String requestUrl) {
        String scenario = SystemHolder.getScenario();
        DynamicDataSourceContextHolder.clearDataSource();
        Map<String, Object> param = MapUtil.newHashMap();
        param.put("requestUrl", requestUrl);
        List<UrlTableVO> urlTables = urlTableService.selectByParams(param);
        DynamicDataSourceContextHolder.setDataSource(scenario);
        if (CollectionUtils.isEmpty(urlTables)) {
            return null;
        }
        return urlTables.get(0).getDataTableName();
    }

    @Override
    public List<String> selectUserByRoleNames(List<String> roleNames) {
        return roleDao.selectUserByRoleNames(roleNames);
    }

    @Override
    public List<LabelValue<String>> dropdownX(String collectionCode, Integer recursiveTimes, Boolean includePrevious) {
        List<CollectionValueVO> valueList = new ArrayList<>();
        collectionValueService.getCollectionValuesRecursive(valueList, collectionCode, 0, recursiveTimes, includePrevious);
        List<LabelValue<String>> data = valueList.stream().map(item ->
                new LabelValue<>(item.getValueMeaning(), item.getCollectionValue())).collect(Collectors.toList());
        return data;
    }
}