package com.yhl.scp.ips.requestLog.service.impl;

import com.yhl.platform.common.Pagination;
import com.yhl.scp.ips.requestLog.dao.RequestLogRepository;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import com.yhl.scp.ips.requestLog.service.RequestLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class RequestLogServiceImpl implements RequestLogService {

    @Resource
    private RequestLogRepository requestLogRepository;

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void saveData(RequestLogDTO requestLogDTO) {
        requestLogRepository.save(requestLogDTO);
    }

    private static final String FIELD_REQUEST_TIME = "requestTime";
    @Override
    public Page<RequestLogDTO> selectByPage(Pagination pagination, RequestLogQueryDTO queryParams) {
        Criteria criteria = new Criteria();
        List<Criteria> criteriaList = new ArrayList<>();
        if (queryParams != null) {
            if (StringUtils.isNotEmpty(queryParams.getUserId())) {
                criteriaList.add(Criteria.where("userId").is(queryParams.getUserId()));
            }
            if (StringUtils.isNotEmpty(queryParams.getResponseStatus())) {
                criteriaList.add(Criteria.where("responseStatus").is(queryParams.getResponseStatus()));
            }
            if (StringUtils.isNotEmpty(queryParams.getRequestUri())) {
                criteriaList.add(Criteria.where("requestUri").regex(queryParams.getRequestUri(), "i"));
            }
            if (StringUtils.isNotEmpty(queryParams.getUserAgent())) {
                criteriaList.add(Criteria.where("userAgent").regex(queryParams.getUserAgent(), "i"));
            }
            if (queryParams.getBeginTime() != null) {
                criteriaList.add(Criteria.where(FIELD_REQUEST_TIME).gte(queryParams.getBeginTime()));
            }
            if (queryParams.getEndTime() != null) {
                criteriaList.add(Criteria.where(FIELD_REQUEST_TIME).lte(queryParams.getEndTime()));
            }
        }
        if (!criteriaList.isEmpty()) {
            criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        }
        Query mongoQuery = new Query(criteria);
        long total = mongoTemplate.count(mongoQuery, RequestLogDTO.class);
        mongoQuery.with(Sort.by(Sort.Direction.DESC, FIELD_REQUEST_TIME));
        mongoQuery.skip((long) (pagination.getPageNum() - 1) * pagination.getPageSize())
                .limit(pagination.getPageSize());
        List<RequestLogDTO> result = mongoTemplate.find(mongoQuery, RequestLogDTO.class);
        return new PageImpl<>(result, PageRequest.of(pagination.getPageNum() - 1, pagination.getPageSize()), total);
    }
}
