<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO">
        <!--@Table mds_product_stock_point-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="measurement_unit" jdbcType="VARCHAR" property="measurementUnit"/>
        <result column="sale_type" jdbcType="VARCHAR" property="saleType"/>
        <result column="loading_position" jdbcType="VARCHAR" property="loadingPosition"/>
        <result column="loading_position_sub" jdbcType="VARCHAR" property="loadingPositionSub"/>
        <result column="vehicle_model_type" jdbcType="VARCHAR" property="vehicleModelType"/>
        <result column="business_special" jdbcType="VARCHAR" property="businessSpecial"/>
        <result column="core_process" jdbcType="VARCHAR" property="coreProcess"/>
        <result column="product_special" jdbcType="VARCHAR" property="productSpecial"/>
        <result column="product_length" jdbcType="VARCHAR" property="productLength"/>
        <result column="product_width" jdbcType="VARCHAR" property="productWidth"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_area" jdbcType="VARCHAR" property="productArea"/>
        <result column="product_weight" jdbcType="VARCHAR" property="productWeight"/>
        <result column="weight_unit" jdbcType="VARCHAR" property="weightUnit"/>
        <result column="product_quantity" jdbcType="VARCHAR" property="productQuantity"/>
        <result column="expire_date" jdbcType="INTEGER" property="expireDate"/>
        <result column="special_desc" jdbcType="VARCHAR" property="specialDesc"/>
        <result column="min_order_quantity" jdbcType="VARCHAR" property="minOrderQuantity"/>
        <result column="transport_cycle" jdbcType="VARCHAR" property="transportCycle"/>
        <result column="purchase_process_pre" jdbcType="VARCHAR" property="purchaseProcessPre"/>
        <result column="purchase_process_ing" jdbcType="VARCHAR" property="purchaseProcessIng"/>
        <result column="purchase_process_after" jdbcType="VARCHAR" property="purchaseProcessAfter"/>
        <result column="purchase_lock_period" jdbcType="VARCHAR" property="purchaseLockPeriod"/>
        <result column="product_plan_user" jdbcType="VARCHAR" property="productPlanUser"/>
        <result column="product_user" jdbcType="VARCHAR" property="productUser"/>
        <result column="order_planner" jdbcType="VARCHAR" property="orderPlanner"/>
        <result column="production_planner" jdbcType="VARCHAR" property="productionPlanner"/>
        <result column="material_planner" jdbcType="VARCHAR" property="materialPlanner"/>
        <result column="purchase_planner" jdbcType="VARCHAR" property="purchasePlanner"/>
        <result column="product_sop" jdbcType="TIMESTAMP" property="productSop"/>
        <result column="product_eop" jdbcType="TIMESTAMP" property="productEop"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="planner_code" jdbcType="VARCHAR" property="plannerCode"/>
        <result column="po_category" jdbcType="VARCHAR" property="poCategory"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="inventory_item_id" jdbcType="VARCHAR" property="inventoryItemId"/>
        <result column="inventory_item_status_code" jdbcType="VARCHAR" property="inventoryItemStatusCode"/>
        <result column="product_category" jdbcType="VARCHAR" property="productCategory"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="material_demand_status" jdbcType="VARCHAR" property="materialDemandStatus"/>
        <result column="edi_flag" jdbcType="VARCHAR" property="ediFlag"/>
        <result column="item_flag" jdbcType="VARCHAR" property="itemFlag"/>
        <result column="isbj" jdbcType="VARCHAR" property="isbj"/>
        <result column="mold_quantity_limit" jdbcType="VARCHAR" property="moldQuantityLimit"/>
        <result column="pick_up_type" jdbcType="VARCHAR" property="pickUpType"/>
        <result column="tally_order_mode" jdbcType="VARCHAR" property="tallyOrderMode"/>
        <result column="full_box_flag" jdbcType="VARCHAR" property="fullBoxFlag"/>
        <result column="item_cost" jdbcType="VARCHAR" property="itemCost"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO">
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="part_number" jdbcType="VARCHAR" property="partNumber"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel"/>
        <!--<result column="order_planner_name" jdbcType="VARCHAR" property="orderPlannerName"/>
        <result column="production_planner_name" jdbcType="VARCHAR" property="productionPlannerName"/>
        <result column="material_planner_name" jdbcType="VARCHAR" property="materialPlannerName"/>-->
    </resultMap>
    <sql id="Base_Column_List">
        id,stock_point_code,product_code,product_name,product_type,product_classify,classify_desc,vehicle_model_code,
        supply_type,measurement_unit,sale_type,loading_position,loading_position_sub,vehicle_model_type,business_special,
        core_process,product_special,product_length,product_width,product_thickness,product_color,product_area,
        product_weight,weight_unit,product_quantity,expire_date,special_desc,min_order_quantity,transport_cycle,
        purchase_process_pre,purchase_process_ing,purchase_process_after,purchase_lock_period,product_plan_user,
        product_user,order_planner,production_planner,material_planner,purchase_planner,product_sop,product_eop,
        remark,enabled,creator,create_time,modifier,modify_time,version_value,planner_code,po_category,
        organization_id,inventory_item_id,inventory_item_status_code,product_category,last_update_date,
        material_demand_status,edi_flag,item_flag,isbj,mold_quantity_limit,pick_up_type,tally_order_mode,full_box_flag,item_cost
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>
        ,stock_point_name,part_number,part_name,risk_level/*,order_planner_name,production_planner_name,material_planner_name*/
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodes != null and params.stockPointCodes.size > 0">
                <choose>
                    <when test="params.stockPointCodes.size == 1">
                        and stock_point_code = #{params.stockPointCodes[0],jdbcType=VARCHAR}
                    </when>
                    <otherwise>
                        and stock_point_code in
                        <foreach collection="params.stockPointCodes" item="item" index="index" open="(" separator=","
                                 close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeLike != null and params.productCodeLike != ''">
                and product_code like #{params.productCodeLike,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCodeLike != null and params.materialCodeLike != ''">
                and product_code like #{params.materialCodeLike,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.productType != null and params.productType != ''">
                and product_type = #{params.productType,jdbcType=VARCHAR}
            </if>
            <if test="params.productTypes != null and params.productTypes.size() > 0">
                and product_type in
                <foreach collection="params.productTypes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productClassify != null and params.productClassify != ''">
                and product_classify = #{params.productClassify,jdbcType=VARCHAR}
            </if>
            <if test="params.classifyDesc != null and params.classifyDesc != ''">
                and classify_desc = #{params.classifyDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleCodeList != null and params.vehicleCodeList.size() > 0">
                and vehicle_model_code in
                <foreach collection="params.vehicleCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.vehicleModelCodeLike != null and params.vehicleModelCodeLike != ''">
                and vehicle_model_code like #{params.vehicleModelCodeLike,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.supplyType != null and params.supplyType != ''">
                and supply_type = #{params.supplyType,jdbcType=VARCHAR}
            </if>
            <if test="params.measurementUnit != null and params.measurementUnit != ''">
                and measurement_unit = #{params.measurementUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.saleType != null and params.saleType != ''">
                and sale_type = #{params.saleType,jdbcType=VARCHAR}
            </if>
            <if test="params.loadingPosition != null and params.loadingPosition != ''">
                and loading_position = #{params.loadingPosition,jdbcType=VARCHAR}
            </if>
            <if test="params.loadingPositionSub != null and params.loadingPositionSub != ''">
                and loading_position_sub = #{params.loadingPositionSub,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelType != null and params.vehicleModelType != ''">
                and vehicle_model_type = #{params.vehicleModelType,jdbcType=VARCHAR}
            </if>
            <if test="params.businessSpecial != null and params.businessSpecial != ''">
                and business_special = #{params.businessSpecial,jdbcType=VARCHAR}
            </if>
            <if test="params.coreProcess != null and params.coreProcess != ''">
                and core_process = #{params.coreProcess,jdbcType=VARCHAR}
            </if>
            <if test="params.productSpecial != null and params.productSpecial != ''">
                and product_special = #{params.productSpecial,jdbcType=VARCHAR}
            </if>
            <if test="params.productLength != null">
                and product_length = #{params.productLength,jdbcType=VARCHAR}
            </if>
            <if test="params.productWidth != null">
                and product_width = #{params.productWidth,jdbcType=VARCHAR}
            </if>
            <if test="params.productThickness != null">
                and product_thickness = #{params.productThickness,jdbcType=VARCHAR}
            </if>
            <if test="params.productColor != null and params.productColor != ''">
                and product_color = #{params.productColor,jdbcType=VARCHAR}
            </if>
            <if test="params.productArea != null">
                and product_area = #{params.productArea,jdbcType=VARCHAR}
            </if>
            <if test="params.productWeight != null">
                and product_weight = #{params.productWeight,jdbcType=VARCHAR}
            </if>
            <if test="params.weightUnit != null and params.weightUnit != ''">
                and weight_unit = #{params.weightUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.productQuantity != null">
                and product_quantity = #{params.productQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.expireDate != null">
                and expire_date = #{params.expireDate,jdbcType=INTEGER}
            </if>
            <if test="params.specialDesc != null and params.specialDesc != ''">
                and special_desc = #{params.specialDesc,jdbcType=VARCHAR}
            </if>
            <if test="params.minOrderQuantity != null">
                and min_order_quantity = #{params.minOrderQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.transportCycle != null">
                and transport_cycle = #{params.transportCycle,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseProcessPre != null and params.purchaseProcessPre != ''">
                and purchase_process_pre = #{params.purchaseProcessPre,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseProcessIng != null and params.purchaseProcessIng != ''">
                and purchase_process_ing = #{params.purchaseProcessIng,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseProcessAfter != null and params.purchaseProcessAfter != ''">
                and purchase_process_after = #{params.purchaseProcessAfter,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseLockPeriod != null">
                and purchase_lock_period = #{params.purchaseLockPeriod,jdbcType=VARCHAR}
            </if>
            <if test="params.productPlanUser != null and params.productPlanUser != ''">
                and product_plan_user = #{params.productPlanUser,jdbcType=VARCHAR}
            </if>
            <if test="params.productUser != null and params.productUser != ''">
                and product_user = #{params.productUser,jdbcType=VARCHAR}
            </if>
            <if test="params.orderPlanner != null and params.orderPlanner != ''">
                and order_planner = #{params.orderPlanner,jdbcType=VARCHAR}
            </if>
            <if test="params.productionPlanner != null and params.productionPlanner != ''">
                and find_in_set(#{params.productionPlanner,jdbcType=VARCHAR}, production_planner) > 0
            </if>
            <if test="params.materialPlanner != null and params.materialPlanner != ''">
                and find_in_set(#{params.materialPlanner,jdbcType=VARCHAR}, material_planner) > 0
            </if>
            <if test="params.purchasePlanner != null and params.purchasePlanner != ''">
                and find_in_set(#{params.purchasePlanner,jdbcType=VARCHAR}, purchase_planner) > 0
            </if>
            <if test="params.productSop != null">
                and product_sop = #{params.productSop,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productEop != null">
                and product_eop = #{params.productEop,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.plannerCode != null and params.plannerCode != ''">
                and planner_code = #{params.plannerCode,jdbcType=VARCHAR}
            </if>
            <if test="params.poCategory != null and params.poCategory != ''">
                and po_category = #{params.poCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationId != null and params.organizationId != ''">
                and organization_id = #{params.organizationId,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryItemId != null and params.inventoryItemId != ''">
                and inventory_item_id = #{params.inventoryItemId,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryItemIds != null and params.inventoryItemIds.size() > 0">
                and inventory_item_id in
                <foreach collection="params.inventoryItemIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.inventoryItemStatusCode != null and params.inventoryItemStatusCode != ''">
                and inventory_item_status_code = #{params.inventoryItemStatusCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCategory != null and params.productCategory != ''">
                and product_category = #{params.productCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.materialDemandStatus != null">
                and material_demand_status = #{params.materialDemandStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.ediFlag != null and params.ediFlag != ''">
                and edi_flag = #{params.ediFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.productColorIsNotNull != null and params.productColorIsNotNull != '' and params.productColorIsNotNull == 'YES'">
                and product_color is not null
            </if>
            <if test="params.itemFlag != null and params.itemFlag != ''">
                and item_flag = #{params.itemFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.isbj != null and params.isbj != ''">
                and isbj = #{params.isbj,jdbcType=VARCHAR}
            </if>
            <if test="params.moldQuantityLimit != null and params.moldQuantityLimit != ''">
                and mold_quantity_limit = #{params.moldQuantityLimit,jdbcType=INTEGER}
            </if>
            <if test="params.pickUpType != null and params.pickUpType != ''">
                and pick_up_type = #{params.pickUpType,jdbcType=VARCHAR}
            </if>
            <if test="params.materialPlannerNotNull != null and params.materialPlannerNotNull == 'YES'">
                and material_planner is not null
            </if>
            <if test="params.tallyOrderMode != null and params.tallyOrderMode != ''">
                and tally_order_mode = #{params.tallyOrderMode,jdbcType=VARCHAR}
            </if>
            <if test="params.fullBoxFlag != null and params.fullBoxFlag != ''">
                and full_box_flag = #{params.fullBoxFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.productColorList != null and params.productColorList.size() > 0">
                and product_color in
                <foreach collection="params.productColorList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productThicknessList != null and params.productThicknessList.size() > 0">
                and product_thickness in
                <foreach collection="params.productThicknessList" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productClassifyLikeOr != null and params.productClassifyLikeOr != ''">
                or (product_classify like concat('%', 'BB', '%')
                or product_classify like concat('%', 'BG', '%')
                or product_classify like concat('%', 'BJ', '%'))
            </if>
        </where>
    </sql>
    <!-- 下拉字段查询 -->
    <select id="selectFieldsByField" resultType="java.lang.String">
        select ${field}
        from mds_product_stock_point
        group by ${field}
        having ${field} is not null
    </select>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mds_product_stock_point
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_product_stock_point
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectByProductCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where product_code in
        <foreach collection="codeList" item="productCode" index="index" open="(" separator="," close=")">
            #{productCode,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByVehicleModelCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where vehicle_model_code in
        <foreach collection="vehicleModelCodeList" item="vehicleModelCode" index="index" open="(" separator=","
                 close=")">
            #{vehicleModelCode,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectProductCodeLikeByVehicleByStock" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where vehicle_model_code in
        <foreach collection="vehicleModelCodeList" item="vehicleModelCode" index="index" open="(" separator=","
                 close=")">
            #{vehicleModelCode,jdbcType=VARCHAR}
        </foreach>
        <if test="productCode != null and productCode != ''">
            and product_code like #{productCode,jdbcType=VARCHAR}
        </if>
        <if test="stockPointCode != null and stockPointCode != ''">
            and stock_point_code = #{stockPointCode,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectByStockPointCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where
        <choose>
            <when test="stockPointCodes.size == 1">
                stock_point_code = #{stockPointCodes[0],jdbcType=VARCHAR}
            </when>
            <otherwise>
                stock_point_code in
                <foreach collection="stockPointCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </otherwise>
        </choose>
    </select>

    <select id="selectProductCodeLike" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point where 1 = 1
        <if test="productCode != null and productCode != ''">
            and product_code like #{productCode,jdbcType=VARCHAR}
        </if>
        <if test="stockPointCode != null and stockPointCode != ''">
            and stock_point_code like #{stockPointCode,jdbcType=VARCHAR}
        </if>
        <if test="productTypes != null and productTypes.size > 0">
            and product_type in
            <foreach collection="productTypes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid())
            from dual
        </selectKey>
        insert into mds_product_stock_point(id,
        stock_point_code,
        product_code,
        product_name,
        product_type,
        product_classify,
        classify_desc,
        vehicle_model_code,
        supply_type,
        measurement_unit,
        sale_type,
        loading_position,
        loading_position_sub,
        vehicle_model_type,
        business_special,
        core_process,
        product_special,
        product_length,
        product_width,
        product_thickness,
        product_color,
        product_area,
        product_weight,
        weight_unit,
        product_quantity,
        expire_date,
        special_desc,
        min_order_quantity,
        transport_cycle,
        purchase_process_pre,
        purchase_process_ing,
        purchase_process_after,
        purchase_lock_period,
        product_plan_user,
        product_user,
        order_planner,
        production_planner,
        material_planner,
        purchase_planner,
        product_sop,
        product_eop,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        planner_code,
        po_category,
        product_category,
        organization_id,
        inventory_item_id,
        inventory_item_status_code,
        last_update_date,
        material_demand_status,
        edi_flag,
        item_flag,
        isbj,
        mold_quantity_limit,
        pick_up_type,
        tally_order_mode,
        full_box_flag,
        item_cost)
        values (#{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{productType,jdbcType=VARCHAR},
        #{productClassify,jdbcType=VARCHAR},
        #{classifyDesc,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{supplyType,jdbcType=VARCHAR},
        #{measurementUnit,jdbcType=VARCHAR},
        #{saleType,jdbcType=VARCHAR},
        #{loadingPosition,jdbcType=VARCHAR},
        #{loadingPositionSub,jdbcType=VARCHAR},
        #{vehicleModelType,jdbcType=VARCHAR},
        #{businessSpecial,jdbcType=VARCHAR},
        #{coreProcess,jdbcType=VARCHAR},
        #{productSpecial,jdbcType=VARCHAR},
        #{productLength,jdbcType=VARCHAR},
        #{productWidth,jdbcType=VARCHAR},
        #{productThickness,jdbcType=VARCHAR},
        #{productColor,jdbcType=VARCHAR},
        #{productArea,jdbcType=VARCHAR},
        #{productWeight,jdbcType=VARCHAR},
        #{weightUnit,jdbcType=VARCHAR},
        #{productQuantity,jdbcType=VARCHAR},
        #{expireDate,jdbcType=INTEGER},
        #{specialDesc,jdbcType=VARCHAR},
        #{minOrderQuantity,jdbcType=VARCHAR},
        #{transportCycle,jdbcType=VARCHAR},
        #{purchaseProcessPre,jdbcType=VARCHAR},
        #{purchaseProcessIng,jdbcType=VARCHAR},
        #{purchaseProcessAfter,jdbcType=VARCHAR},
        #{purchaseLockPeriod,jdbcType=VARCHAR},
        #{productPlanUser,jdbcType=VARCHAR},
        #{productUser,jdbcType=VARCHAR},
        #{orderPlanner,jdbcType=VARCHAR},
        #{productionPlanner,jdbcType=VARCHAR},
        #{materialPlanner,jdbcType=VARCHAR},
        #{purchasePlanner,jdbcType=VARCHAR},
        #{productSop,jdbcType=TIMESTAMP},
        #{productEop,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{plannerCode,jdbcType=VARCHAR},
        #{poCategory,jdbcType=VARCHAR},
        #{productCategory,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{inventoryItemId,jdbcType=VARCHAR},
        #{inventoryItemStatusCode,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{materialDemandStatus,jdbcType=VARCHAR},
        #{ediFlag,jdbcType=VARCHAR},
        #{itemFlag,jdbcType=VARCHAR},
        #{isbj,jdbcType=VARCHAR},
        #{moldQuantityLimit,jdbcType=INTEGER},
        #{pickUpType,jdbcType=VARCHAR},
        #{tallyOrderMode,jdbcType=VARCHAR},
        #{fullBoxFlag,jdbcType=VARCHAR},
        #{itemCost,jdbcType=VARCHAR}
        )
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO">
        insert into mds_product_stock_point(id,
                                            stock_point_code,
                                            product_code,
                                            product_name,
                                            product_type,
                                            product_classify,
                                            classify_desc,
                                            vehicle_model_code,
                                            supply_type,
                                            measurement_unit,
                                            sale_type,
                                            loading_position,
                                            loading_position_sub,
                                            vehicle_model_type,
                                            business_special,
                                            core_process,
                                            product_special,
                                            product_length,
                                            product_width,
                                            product_thickness,
                                            product_color,
                                            product_area,
                                            product_weight,
                                            weight_unit,
                                            product_quantity,
                                            expire_date,
                                            special_desc,
                                            min_order_quantity,
                                            transport_cycle,
                                            purchase_process_pre,
                                            purchase_process_ing,
                                            purchase_process_after,
                                            purchase_lock_period,
                                            product_plan_user,
                                            product_user,
                                            order_planner,
                                            production_planner,
                                            material_planner,
                                            purchase_planner,
                                            product_sop,
                                            product_eop,
                                            remark,
                                            enabled,
                                            creator,
                                            create_time,
                                            modifier,
                                            modify_time,
                                            version_value,
                                            planner_code,
                                            po_category,
                                            product_category,
                                            organization_id,
                                            inventory_item_id,
                                            inventory_item_status_code,
                                            last_update_date,
                                            material_demand_status,
                                            edi_flag,
                                            item_flag,
                                            isbj,
                                            mold_quantity_limit,
                                            pick_up_type,
                                            tally_order_mode,
                                            full_box_flag,
                                            item_cost)
            )
        values (#{id,jdbcType=VARCHAR}, #{stockPointCode,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{productClassify,jdbcType=VARCHAR}, #{classifyDesc,jdbcType=VARCHAR}, #{vehicleModelCode,jdbcType=VARCHAR}, #{supplyType,jdbcType=VARCHAR}, #{measurementUnit,jdbcType=VARCHAR}, #{saleType,jdbcType=VARCHAR}, #{loadingPosition,jdbcType=VARCHAR}, #{loadingPositionSub,jdbcType=VARCHAR}, #{vehicleModelType,jdbcType=VARCHAR}, #{businessSpecial,jdbcType=VARCHAR}, #{coreProcess,jdbcType=VARCHAR}, #{productSpecial,jdbcType=VARCHAR}, #{productLength,jdbcType=VARCHAR}, #{productWidth,jdbcType=VARCHAR}, #{productThickness,jdbcType=VARCHAR}, #{productColor,jdbcType=VARCHAR}, #{productArea,jdbcType=VARCHAR}, #{productWeight,jdbcType=VARCHAR}, #{weightUnit,jdbcType=VARCHAR}, #{productQuantity,jdbcType=VARCHAR}, #{expireDate,jdbcType=INTEGER}, #{specialDesc,jdbcType=VARCHAR}, #{minOrderQuantity,jdbcType=VARCHAR}, #{transportCycle,jdbcType=VARCHAR}, #{purchaseProcessPre,jdbcType=VARCHAR}, #{purchaseProcessIng,jdbcType=VARCHAR}, #{purchaseProcessAfter,jdbcType=VARCHAR}, #{purchaseLockPeriod,jdbcType=VARCHAR}, #{productPlanUser,jdbcType=VARCHAR}, #{productUser,jdbcType=VARCHAR}, #{orderPlanner,jdbcType=VARCHAR}, #{productionPlanner,jdbcType=VARCHAR}, #{materialPlanner,jdbcType=VARCHAR}, #{purchasePlanner,jdbcType=VARCHAR}, #{productSop,jdbcType=TIMESTAMP}, #{productEop,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{enabled,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}, #{versionValue,jdbcType=INTEGER}, #{plannerCode,jdbcType=VARCHAR}, #{poCategory,jdbcType=VARCHAR}, #{productCategory,jdbcType=VARCHAR}, #{organizationId,jdbcType=VARCHAR}, #{inventoryItemId,jdbcType=VARCHAR}, #{inventoryItemStatusCode,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{materialDemandStatus,jdbcType=VARCHAR}, #{ediFlag,jdbcType=VARCHAR}, #{itemFlag,jdbcType=VARCHAR}, #{isbj,jdbcType=VARCHAR}, #{moldQuantityLimit,jdbcType=INTEGER}, #{pickUpType,jdbcType=VARCHAR}, #{tallyOrderMode,jdbcType=VARCHAR}, #{fullBoxFlag,jdbcType=VARCHAR}, #{itemCost,jdbcType=VARCHAR}
            )
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_product_stock_point(id,
        stock_point_code,
        product_code,
        product_name,
        product_type,
        product_classify,
        classify_desc,
        vehicle_model_code,
        supply_type,
        measurement_unit,
        sale_type,
        loading_position,
        loading_position_sub,
        vehicle_model_type,
        business_special,
        core_process,
        product_special,
        product_length,
        product_width,
        product_thickness,
        product_color,
        product_area,
        product_weight,
        weight_unit,
        product_quantity,
        expire_date,
        special_desc,
        min_order_quantity,
        transport_cycle,
        purchase_process_pre,
        purchase_process_ing,
        purchase_process_after,
        purchase_lock_period,
        product_plan_user,
        product_user,
        order_planner,
        production_planner,
        material_planner,
        purchase_planner,
        product_sop,
        product_eop,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        planner_code,
        po_category,
        product_category,
        organization_id,
        inventory_item_id,
        inventory_item_status_code,
        last_update_date,
        material_demand_status,
        edi_flag,
        item_flag,
        isbj,
        mold_quantity_limit,
        pick_up_type,
        tally_order_mode,
        full_box_flag,
        item_cost)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.productType,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.classifyDesc,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.measurementUnit,jdbcType=VARCHAR},
            #{entity.saleType,jdbcType=VARCHAR},
            #{entity.loadingPosition,jdbcType=VARCHAR},
            #{entity.loadingPositionSub,jdbcType=VARCHAR},
            #{entity.vehicleModelType,jdbcType=VARCHAR},
            #{entity.businessSpecial,jdbcType=VARCHAR},
            #{entity.coreProcess,jdbcType=VARCHAR},
            #{entity.productSpecial,jdbcType=VARCHAR},
            #{entity.productLength,jdbcType=VARCHAR},
            #{entity.productWidth,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productArea,jdbcType=VARCHAR},
            #{entity.productWeight,jdbcType=VARCHAR},
            #{entity.weightUnit,jdbcType=VARCHAR},
            #{entity.productQuantity,jdbcType=VARCHAR},
            #{entity.expireDate,jdbcType=INTEGER},
            #{entity.specialDesc,jdbcType=VARCHAR},
            #{entity.minOrderQuantity,jdbcType=VARCHAR},
            #{entity.transportCycle,jdbcType=VARCHAR},
            #{entity.purchaseProcessPre,jdbcType=VARCHAR},
            #{entity.purchaseProcessIng,jdbcType=VARCHAR},
            #{entity.purchaseProcessAfter,jdbcType=VARCHAR},
            #{entity.purchaseLockPeriod,jdbcType=VARCHAR},
            #{entity.productPlanUser,jdbcType=VARCHAR},
            #{entity.productUser,jdbcType=VARCHAR},
            #{entity.orderPlanner,jdbcType=VARCHAR},
            #{entity.productionPlanner,jdbcType=VARCHAR},
            #{entity.materialPlanner,jdbcType=VARCHAR},
            #{entity.purchasePlanner,jdbcType=VARCHAR},
            #{entity.productSop,jdbcType=TIMESTAMP},
            #{entity.productEop,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.plannerCode,jdbcType=VARCHAR},
            #{entity.poCategory,jdbcType=VARCHAR},
            #{entity.productCategory,jdbcType=VARCHAR},
            #{entity.organizationId,jdbcType=VARCHAR},
            #{entity.inventoryItemId,jdbcType=VARCHAR},
            #{entity.inventoryItemStatusCode,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.materialDemandStatus,jdbcType=VARCHAR},
            #{entity.ediFlag,jdbcType=VARCHAR},
            #{entity.itemFlag,jdbcType=VARCHAR},
            #{entity.isbj,jdbcType=VARCHAR},
            #{entity.moldQuantityLimit,jdbcType=INTEGER},
            #{entity.pickUpType,jdbcType=VARCHAR},
            #{entity.tallyOrderMode,jdbcType=VARCHAR},
            #{entity.fullBoxFlag,jdbcType=VARCHAR},
            #{entity.itemCost,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_product_stock_point(id,
        stock_point_code,
        product_code,
        product_name,
        product_type,
        product_classify,
        classify_desc,
        vehicle_model_code,
        supply_type,
        measurement_unit,
        sale_type,
        loading_position,
        loading_position_sub,
        vehicle_model_type,
        business_special,
        core_process,
        product_special,
        product_length,
        product_width,
        product_thickness,
        product_color,
        product_area,
        product_weight,
        weight_unit,
        product_quantity,
        expire_date,
        special_desc,
        min_order_quantity,
        transport_cycle,
        purchase_process_pre,
        purchase_process_ing,
        purchase_process_after,
        purchase_lock_period,
        product_plan_user,
        product_user,
        order_planner,
        production_planner,
        material_planner,
        purchase_planner,
        product_sop,
        product_eop,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        planner_code,
        po_category,
        product_category,
        organization_id,
        inventory_item_id,
        inventory_item_status_code,
        last_update_date,
        material_demand_status,
        edi_flag,
        item_flag,
        isbj,
        mold_quantity_limit,
        pick_up_type,
        tally_order_mode,
        full_box_flag,
        item_cost)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.productType,jdbcType=VARCHAR},
            #{entity.productClassify,jdbcType=VARCHAR},
            #{entity.classifyDesc,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.measurementUnit,jdbcType=VARCHAR},
            #{entity.saleType,jdbcType=VARCHAR},
            #{entity.loadingPosition,jdbcType=VARCHAR},
            #{entity.loadingPositionSub,jdbcType=VARCHAR},
            #{entity.vehicleModelType,jdbcType=VARCHAR},
            #{entity.businessSpecial,jdbcType=VARCHAR},
            #{entity.coreProcess,jdbcType=VARCHAR}
            #{entity.productSpecial,jdbcType=VARCHAR},
            #{entity.productLength,jdbcType=VARCHAR},
            #{entity.productWidth,jdbcType=VARCHAR},
            #{entity.productThickness,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productArea,jdbcType=VARCHAR},
            #{entity.productWeight,jdbcType=VARCHAR},
            #{entity.weightUnit,jdbcType=VARCHAR},
            #{entity.productQuantity,jdbcType=VARCHAR},
            #{entity.expireDate,jdbcType=INTEGER},
            #{entity.specialDesc,jdbcType=VARCHAR},
            #{entity.minOrderQuantity,jdbcType=VARCHAR},
            #{entity.transportCycle,jdbcType=VARCHAR},
            #{entity.purchaseProcessPre,jdbcType=VARCHAR},
            #{entity.purchaseProcessIng,jdbcType=VARCHAR},
            #{entity.purchaseProcessAfter,jdbcType=VARCHAR},
            #{entity.purchaseLockPeriod,jdbcType=VARCHAR},
            #{entity.productPlanUser,jdbcType=VARCHAR},
            #{entity.productUser,jdbcType=VARCHAR},
            #{entity.orderPlanner,jdbcType=VARCHAR},
            #{entity.productionPlanner,jdbcType=VARCHAR},
            #{entity.materialPlanner,jdbcType=VARCHAR},
            #{entity.purchasePlanner,jdbcType=VARCHAR},
            #{entity.productSop,jdbcType=TIMESTAMP},
            #{entity.productEop,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.plannerCode,jdbcType=VARCHAR},
            #{entity.poCategory,jdbcType=VARCHAR},
            #{entity.productCategory,jdbcType=VARCHAR},
            #{entity.organizationId,jdbcType=VARCHAR},
            #{entity.inventoryItemId,jdbcType=VARCHAR},
            #{entity.inventoryItemStatusCode,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.materialDemandStatus,jdbcType=VARCHAR},
            #{entity.ediFlag,jdbcType=VARCHAR},
            #{entity.itemFlag,jdbcType=VARCHAR},
            #{entity.isbj,jdbcType=VARCHAR},
            #{entity.moldQuantityLimit,jdbcType=INTEGER},
            #{entity.pickUpType,jdbcType=VARCHAR},
            #{entity.tallyOrderMode,jdbcType=VARCHAR},
            #{entity.fullBoxFlag,jdbcType=VARCHAR},
            #{entity.itemCost,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO">
        update mds_product_stock_point
        set stock_point_code           = #{stockPointCode,jdbcType=VARCHAR},
            product_code               = #{productCode,jdbcType=VARCHAR},
            product_name               = #{productName,jdbcType=VARCHAR},
            product_type               = #{productType,jdbcType=VARCHAR},
            product_classify           = #{productClassify,jdbcType=VARCHAR},
            classify_desc              = #{classifyDesc,jdbcType=VARCHAR},
            vehicle_model_code         = #{vehicleModelCode,jdbcType=VARCHAR},
            supply_type                = #{supplyType,jdbcType=VARCHAR},
            measurement_unit           = #{measurementUnit,jdbcType=VARCHAR},
            sale_type                  = #{saleType,jdbcType=VARCHAR},
            loading_position           = #{loadingPosition,jdbcType=VARCHAR},
            loading_position_sub       = #{loadingPositionSub,jdbcType=VARCHAR},
            vehicle_model_type         = #{vehicleModelType,jdbcType=VARCHAR},
            business_special           = #{businessSpecial,jdbcType=VARCHAR},
            core_process               = #{coreProcess,jdbcType=VARCHAR},
            product_special            = #{productSpecial,jdbcType=VARCHAR},
            product_length             = #{productLength,jdbcType=VARCHAR},
            product_width              = #{productWidth,jdbcType=VARCHAR},
            product_thickness          = #{productThickness,jdbcType=VARCHAR},
            product_color              = #{productColor,jdbcType=VARCHAR},
            product_area               = #{productArea,jdbcType=VARCHAR},
            product_weight             = #{productWeight,jdbcType=VARCHAR},
            weight_unit                = #{weightUnit,jdbcType=VARCHAR},
            product_quantity           = #{productQuantity,jdbcType=VARCHAR},
            expire_date                = #{expireDate,jdbcType=INTEGER},
            special_desc               = #{specialDesc,jdbcType=VARCHAR},
            min_order_quantity         = #{minOrderQuantity,jdbcType=VARCHAR},
            transport_cycle            = #{transportCycle,jdbcType=VARCHAR},
            purchase_process_pre       = #{purchaseProcessPre,jdbcType=VARCHAR},
            purchase_process_ing       = #{purchaseProcessIng,jdbcType=VARCHAR},
            purchase_process_after     = #{purchaseProcessAfter,jdbcType=VARCHAR},
            purchase_lock_period       = #{purchaseLockPeriod,jdbcType=VARCHAR},
            product_plan_user          = #{productPlanUser,jdbcType=VARCHAR},
            product_user               = #{productUser,jdbcType=VARCHAR},
            order_planner              = #{orderPlanner,jdbcType=VARCHAR},
            production_planner         = #{productionPlanner,jdbcType=VARCHAR},
            material_planner           = #{materialPlanner,jdbcType=VARCHAR},
            purchase_planner           = #{purchasePlanner,jdbcType=VARCHAR},
            product_sop                = #{productSop,jdbcType=TIMESTAMP},
            product_eop                = #{productEop,jdbcType=TIMESTAMP},
            remark                     = #{remark,jdbcType=VARCHAR},
            enabled                    = #{enabled,jdbcType=VARCHAR},
            modifier                   = #{modifier,jdbcType=VARCHAR},
            modify_time                = #{modifyTime,jdbcType=TIMESTAMP},
            planner_code               = #{plannerCode,jdbcType=VARCHAR},
            po_category                = #{poCategory,jdbcType=VARCHAR},
            product_category           = #{productCategory,jdbcType=VARCHAR},
            organization_id            = #{organizationId,jdbcType=VARCHAR},
            inventory_item_id          = #{inventoryItemId,jdbcType=VARCHAR},
            inventory_item_status_code = #{inventoryItemStatusCode,jdbcType=VARCHAR},
            last_update_date           = #{lastUpdateDate,jdbcType=TIMESTAMP},
            material_demand_status     = #{materialDemandStatus,jdbcType=VARCHAR},
            edi_flag                   = #{ediFlag,jdbcType=VARCHAR},
            item_flag                  = #{itemFlag,jdbcType=VARCHAR},
            isbj                       = #{isbj,jdbcType=VARCHAR},
            mold_quantity_limit        = #{moldQuantityLimit,jdbcType=INTEGER},
            pick_up_type               = #{pickUpType,jdbcType=VARCHAR},
            tally_order_mode           = #{tallyOrderMode,jdbcType=VARCHAR},
            full_box_flag              = #{fullBoxFlag,jdbcType=VARCHAR},
            item_cost                  = #{itemCost,jdbcType=VARCHAR},
            version_value              = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO">
        update mds_product_stock_point
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.productType != null and item.productType != ''">
                product_type = #{item.productType,jdbcType=VARCHAR},
            </if>
            <if test="item.productClassify != null and item.productClassify != ''">
                product_classify = #{item.productClassify,jdbcType=VARCHAR},
            </if>
            <if test="item.classifyDesc != null and item.classifyDesc != ''">
                classify_desc = #{item.classifyDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyType != null and item.supplyType != ''">
                supply_type = #{item.supplyType,jdbcType=VARCHAR},
            </if>
            <if test="item.measurementUnit != null and item.measurementUnit != ''">
                measurement_unit = #{item.measurementUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.saleType != null and item.saleType != ''">
                sale_type = #{item.saleType,jdbcType=VARCHAR},
            </if>
            <if test="item.loadingPosition != null and item.loadingPosition != ''">
                loading_position = #{item.loadingPosition,jdbcType=VARCHAR},
            </if>
            <if test="item.loadingPositionSub != null and item.loadingPositionSub != ''">
                loading_position_sub = #{item.loadingPositionSub,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelType != null and item.vehicleModelType != ''">
                vehicle_model_type = #{item.vehicleModelType,jdbcType=VARCHAR},
            </if>
            <if test="item.businessSpecial != null and item.businessSpecial != ''">
                business_special = #{item.businessSpecial,jdbcType=VARCHAR},
            </if>
            <if test="item.coreProcess != null and item.coreProcess != ''">
                core_process = #{item.coreProcess,jdbcType=VARCHAR},
            </if>
            <if test="item.productSpecial != null and item.productSpecial != ''">
                product_special = #{item.productSpecial,jdbcType=VARCHAR},
            </if>
            <if test="item.productLength != null">
                product_length = #{item.productLength,jdbcType=VARCHAR},
            </if>
            <if test="item.productWidth != null">
                product_width = #{item.productWidth,jdbcType=VARCHAR},
            </if>
            <if test="item.productThickness != null">
                product_thickness = #{item.productThickness,jdbcType=VARCHAR},
            </if>
            <if test="item.productColor != null and item.productColor != ''">
                product_color = #{item.productColor,jdbcType=VARCHAR},
            </if>
            <if test="item.productArea != null">
                product_area = #{item.productArea,jdbcType=VARCHAR},
            </if>
            <if test="item.productWeight != null">
                product_weight = #{item.productWeight,jdbcType=VARCHAR},
            </if>
            <if test="item.weightUnit != null and item.weightUnit != ''">
                weight_unit = #{item.weightUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.productQuantity != null">
                product_quantity = #{item.productQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.expireDate != null">
                expire_date = #{item.expireDate,jdbcType=INTEGER},
            </if>
            <if test="item.specialDesc != null and item.specialDesc != ''">
                special_desc = #{item.specialDesc,jdbcType=VARCHAR},
            </if>
            <if test="item.minOrderQuantity != null">
                min_order_quantity = #{item.minOrderQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.transportCycle != null">
                transport_cycle = #{item.transportCycle,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseProcessPre != null and item.purchaseProcessPre != ''">
                purchase_process_pre = #{item.purchaseProcessPre,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseProcessIng != null and item.purchaseProcessIng != ''">
                purchase_process_ing = #{item.purchaseProcessIng,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseProcessAfter != null and item.purchaseProcessAfter != ''">
                purchase_process_after = #{item.purchaseProcessAfter,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseLockPeriod != null">
                purchase_lock_period = #{item.purchaseLockPeriod,jdbcType=VARCHAR},
            </if>
            <if test="item.productPlanUser != null and item.productPlanUser != ''">
                product_plan_user = #{item.productPlanUser,jdbcType=VARCHAR},
            </if>
            <if test="item.productUser != null and item.productUser != ''">
                product_user = #{item.productUser,jdbcType=VARCHAR},
            </if>
            <if test="item.orderPlanner != null and item.orderPlanner != ''">
                order_planner = #{item.orderPlanner,jdbcType=VARCHAR},
            </if>
            <if test="item.productionPlanner != null and item.productionPlanner != ''">
                production_planner = #{item.productionPlanner,jdbcType=VARCHAR},
            </if>
            <if test="item.materialPlanner != null and item.materialPlanner != ''">
                material_planner = #{item.materialPlanner,jdbcType=VARCHAR},
            </if>
            <if test="item.purchasePlanner != null and item.purchasePlanner != ''">
                purchase_planner = #{item.purchasePlanner,jdbcType=VARCHAR},
            </if>
            <if test="item.productSop != null">
                product_sop = #{item.productSop,jdbcType=TIMESTAMP},
            </if>
            <if test="item.productEop != null">
                product_eop = #{item.productEop,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.plannerCode != null and item.plannerCode != ''">
                planner_ode = #{item.plannerCode,jdbcType=VARCHAR},
            </if>
            <if test="item.poCategory != null and item.poCategory != ''">
                po_category = #{item.poCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.productCategory != null and item.productCategory != ''">
                product_category = #{item.productCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationId != null and item.organizationId != ''">
                organization_id = #{item.organizationId,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryItemId != null and item.inventoryItemId != ''">
                inventory_item_id = #{item.inventoryItemId,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryItemStatusCode != null and item.inventoryItemStatusCode != ''">
                inventory_item_status_code = #{item.inventoryItemStatusCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialDemandStatus != null and item.materialDemandStatus != ''">
                material_demand_status = #{item.materialDemandStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.ediFlag != null and item.ediFlag != ''">
                edi_flag = #{item.ediFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.itemFlag != null and item.itemFlag != ''">
                item_flag = #{item.itemFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.moldQuantityLimit != null and item.moldQuantityLimit != ''">
                isbj = #{item.moldQuantityLimit,jdbcType=VARCHAR},
            </if>
            <if test="item.isbj != null and item.isbj != ''">
                mold_quantity_limit = #{item.isbj,jdbcType=INTEGER},
            </if>
            <if test="item.pickUpType != null and item.pickUpType != ''">
                pick_up_type = #{item.pickUpType,jdbcType=VARCHAR},
            </if>
            <if test="item.tallyOrderMode != null and item.tallyOrderMode != ''">
                tally_order_mode = #{item.tallyOrderMode,jdbcType=VARCHAR},
            </if>
            <if test="item.fullBoxFlag != null and item.fullBoxFlag != ''">
                full_box_flag = #{item.fullBoxFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.itemCost != null and item.itemCost != ''">
                item_cost = #{item.itemCost,jdbcType=VARCHAR},
            </if>
            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_product_stock_point
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_classify = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productClassify,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="classify_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.classifyDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.supplyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="measurement_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.measurementUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sale_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.saleType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="loading_position = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.loadingPosition,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="loading_position_sub = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.loadingPositionSub,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.vehicleModelType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="business_special = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.businessSpecial,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="core_process = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.coreProcess,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_special = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productSpecial,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_length = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productLength,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_width = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productWidth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productThickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productColor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productArea,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productWeight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weight_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.weightUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="expire_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.expireDate,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="special_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.specialDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_order_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.minOrderQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transport_cycle = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.transportCycle,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_process_pre = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.purchaseProcessPre,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_process_ing = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.purchaseProcessIng,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_process_after = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.purchaseProcessAfter,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_lock_period = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.purchaseLockPeriod,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_plan_user = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productPlanUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_user = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_planner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.orderPlanner,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_planner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productionPlanner,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_planner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.materialPlanner,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_planner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.purchasePlanner,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_sop = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productSop,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="product_eop = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productEop,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    version_value + 1
                </foreach>
            </trim>
            <trim prefix="planner_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.plannerCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="po_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.poCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.productCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organization_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.organizationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.inventoryItemId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_item_status_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.inventoryItemStatusCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="edi_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ediFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_demand_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.materialDemandStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="isbj = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.isbj,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mold_quantity_limit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.moldQuantityLimit,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="pick_up_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.pickUpType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tally_order_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tallyOrderMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="full_box_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fullBoxFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_cost = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    #{item.itemCost,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_product_stock_point
            <set>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productName != null and item.productName != ''">
                    product_name = #{item.productName,jdbcType=VARCHAR},
                </if>
                <if test="item.productType != null and item.productType != ''">
                    product_type = #{item.productType,jdbcType=VARCHAR},
                </if>
                <if test="item.productClassify != null and item.productClassify != ''">
                    product_classify = #{item.productClassify,jdbcType=VARCHAR},
                </if>
                <if test="item.classifyDesc != null and item.classifyDesc != ''">
                    classify_desc = #{item.classifyDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyType != null and item.supplyType != ''">
                    supply_type = #{item.supplyType,jdbcType=VARCHAR},
                </if>
                <if test="item.measurementUnit != null and item.measurementUnit != ''">
                    measurement_unit = #{item.measurementUnit,jdbcType=VARCHAR},
                </if>
                <if test="item.saleType != null and item.saleType != ''">
                    sale_type = #{item.saleType,jdbcType=VARCHAR},
                </if>
                <if test="item.loadingPosition != null and item.loadingPosition != ''">
                    loading_position = #{item.loadingPosition,jdbcType=VARCHAR},
                </if>
                <if test="item.loadingPositionSub != null and item.loadingPositionSub != ''">
                    loading_position_sub = #{item.loadingPositionSub,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelType != null and item.vehicleModelType != ''">
                    vehicle_model_type = #{item.vehicleModelType,jdbcType=VARCHAR},
                </if>
                <if test="item.businessSpecial != null and item.businessSpecial != ''">
                    business_special = #{item.businessSpecial,jdbcType=VARCHAR},
                </if>
                <if test="item.coreProcess != null and item.coreProcess != ''">
                    core_process = #{item.coreProcess,jdbcType=VARCHAR},
                </if>
                <if test="item.productSpecial != null and item.productSpecial != ''">
                    product_special = #{item.productSpecial,jdbcType=VARCHAR},
                </if>
                <if test="item.productLength != null">
                    product_length = #{item.productLength,jdbcType=VARCHAR},
                </if>
                <if test="item.productWidth != null">
                    product_width = #{item.productWidth,jdbcType=VARCHAR},
                </if>
                <if test="item.productThickness != null">
                    product_thickness = #{item.productThickness,jdbcType=VARCHAR},
                </if>
                <if test="item.productColor != null and item.productColor != ''">
                    product_color = #{item.productColor,jdbcType=VARCHAR},
                </if>
                <if test="item.productArea != null">
                    product_area = #{item.productArea,jdbcType=VARCHAR},
                </if>
                <if test="item.productWeight != null">
                    product_weight = #{item.productWeight,jdbcType=VARCHAR},
                </if>
                <if test="item.weightUnit != null and item.weightUnit != ''">
                    weight_unit = #{item.weightUnit,jdbcType=VARCHAR},
                </if>
                <if test="item.productQuantity != null">
                    product_quantity = #{item.productQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.expireDate != null">
                    expire_date = #{item.expireDate,jdbcType=INTEGER},
                </if>
                <if test="item.specialDesc != null and item.specialDesc != ''">
                    special_desc = #{item.specialDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.minOrderQuantity != null">
                    min_order_quantity = #{item.minOrderQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.transportCycle != null">
                    transport_cycle = #{item.transportCycle,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseProcessPre != null and item.purchaseProcessPre != ''">
                    purchase_process_pre = #{item.purchaseProcessPre,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseProcessIng != null and item.purchaseProcessIng != ''">
                    purchase_process_ing = #{item.purchaseProcessIng,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseProcessAfter != null and item.purchaseProcessAfter != ''">
                    purchase_process_after = #{item.purchaseProcessAfter,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseLockPeriod != null">
                    purchase_lock_period = #{item.purchaseLockPeriod,jdbcType=VARCHAR},
                </if>
                <if test="item.productPlanUser != null and item.productPlanUser != ''">
                    product_plan_user = #{item.productPlanUser,jdbcType=VARCHAR},
                </if>
                <if test="item.productUser != null and item.productUser != ''">
                    product_user = #{item.productUser,jdbcType=VARCHAR},
                </if>
                <if test="item.orderPlanner != null and item.orderPlanner != ''">
                    order_planner = #{item.orderPlanner,jdbcType=VARCHAR},
                </if>
                <if test="item.productionPlanner != null and item.productionPlanner != ''">
                    production_planner = #{item.productionPlanner,jdbcType=VARCHAR},
                </if>
                <if test="item.materialPlanner != null and item.materialPlanner != ''">
                    material_planner = #{item.materialPlanner,jdbcType=VARCHAR},
                </if>
                <if test="item.productSop != null">
                    product_sop = #{item.productSop,jdbcType=TIMESTAMP},
                </if>
                <if test="item.productEop != null">
                    product_eop = #{item.productEop,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.plannerCode != null and item.plannerCode != ''">
                    planner_code = #{item.plannerCode,jdbcType=VARCHAR},
                </if>
                <if test="item.poCategory != null and item.poCategory != ''">
                    po_category = #{item.poCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.productCategory != null and item.productCategory != ''">
                    product_category = #{item.productCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.organizationId != null and item.organizationId != ''">
                    organization_id = #{item.organizationId,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryItemId != null and item.inventoryItemId != ''">
                    inventory_item_id = #{item.inventoryItemId,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryItemStatusCode != null and item.inventoryItemStatusCode != ''">
                    inventory_item_status_code = #{item.inventoryItemStatusCode,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.materialDemandStatus != null">
                    material_demand_status = #{item.materialDemandStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.ediFlag != null and item.ediFlag != ''">
                    edi_flag = #{item.ediFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.itemFlag != null and item.itemFlag != ''">
                    item_flag = #{item.itemFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.isbj != null and item.isbj != ''">
                    isbj = #{item.isbj,jdbcType=VARCHAR},
                </if>
                <if test="item.moldQuantityLimit != null and item.moldQuantityLimit != ''">
                    mold_quantity_limit = #{item.moldQuantityLimit,jdbcType=INTEGER},
                </if>
                <if test="item.pickUpType != null and item.pickUpType != ''">
                    pick_up_type = #{item.pickUpType,jdbcType=VARCHAR},
                </if>
                <if test="item.tallyOrderMode != null and item.tallyOrderMode != ''">
                    tally_order_mode = #{item.tallyOrderMode,jdbcType=VARCHAR},
                </if>
                <if test="item.fullBoxFlag != null and item.fullBoxFlag != ''">
                    full_box_flag = #{item.fullBoxFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.itemCost != null and item.itemCost != ''">
                    item_cost = #{item.itemCost,jdbcType=VARCHAR},
                </if>
                version_value = version_value + 1,
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="doLogicDeleteBatch">
        <foreach collection="deleteProductList" index="index" item="item" separator=";">
            update mds_product_stock_point
            set
            enabled = 'NO',
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>

    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_product_stock_point
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete
        from mds_product_stock_point where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            delete
            from mds_product_stock_point
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point where product_user = #{userId,jdbcType=VARCHAR}
        <if test="list != null and list.size() > 0">
            and stock_point_code in
            <foreach collection="list" item="stockPointCode" index="index" open="(" separator="," close=")">
                #{stockPointCode,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="selectTwoData" resultType="com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO">
        select product_code as productCode, loading_position as loadingPosition
        from mds_product_stock_point
        where product_code is not null
    </select>

    <select id="selectLoadingPositionSub" resultType="java.lang.String">
        SELECT DISTINCT loading_position_sub
        FROM mds_product_stock_point
        WHERE loading_position_sub IS NOT NULL
    </select>

    <select id="selectProductListByParamOnDynamicColumns" resultMap="BaseResultMap">
        select ${dynamicColumn}
        from mds_product_stock_point
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectProductTypeLike" resultMap="BaseResultMap">
        select
        product_type
        from mds_product_stock_point where 1 = 1
        <if test="productType != null and productType != ''">
            and product_type like #{productType,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectSupplyTypeLike" resultMap="BaseResultMap">
        select
        supply_type
        from mds_product_stock_point where 1 = 1
        <if test="supplyType != null and supplyType != ''">
            and supply_type like #{supplyType,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectProductColorLike" resultMap="BaseResultMap">
        select
        product_color
        from mds_product_stock_point where 1 = 1
        <if test="productColor != null and productColor != ''">
            and product_color like #{productColor,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectByLoadPosition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where loading_position_sub in
        <foreach collection="accessPositionList" item="loadingPosition" index="index" open="(" separator=","
                 close=")">
            #{loadingPosition,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectProduct4LoadingDemandSubmission" resultMap="BaseResultMap">
        SELECT psp.product_code,
               psp.vehicle_model_code,
               psp.order_planner
        FROM mds_product_stock_point psp
                 LEFT JOIN mds_stock_point sp ON psp.stock_point_code = sp.stock_point_code
        WHERE sp.organize_type = 'SALE_ORGANIZATION'
    </select>
    <select id="selectVehiclesByProductCode" resultType="java.lang.String">
        SELECT DISTINCT vehicle_model_code
        FROM mds_product_stock_point
        WHERE product_code in
        <foreach collection="codeList" item="productCode" index="index" open="(" separator="," close=")">
            #{productCode,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByProductCodeStockPointCode" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                product_code = #{item.itemCode,jdbcType=VARCHAR} and stock_point_code =
                #{item.plantCode,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <select id="selectByStockCodeAndProductCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where (stock_point_code,product_code) in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            (#{item.stockPointCode,jdbcType=VARCHAR},#{item.rawProductCode,jdbcType=VARCHAR})
        </foreach>
    </select>
    <select id="selectSrmByStockCodeAndProductCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_product_stock_point
        where stock_point_code in (
        select stock_point_code
        from mds_stock_point
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                organization_id = #{item.orgId,jdbcType=VARCHAR}
                )
            </foreach>
        </where>
        )
        and product_code in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.itemCode,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectBaseInfoByStockPointCodes" resultType="com.yhl.scp.mds.newproduct.vo.NewProductStockPointBaseVO">
        select
        id,
        product_code productCode,
        product_name productName
        from mds_product_stock_point
        where 1=1
        <if test="stockPointCodes != null and stockPointCodes.size() > 0">
            and stock_point_code in
            <foreach collection="stockPointCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="selectProductCodeByParams" resultType="java.lang.String">
        select product_code
        from mds_product_stock_point
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectYpProductCodes" resultType="java.lang.String">
        SELECT DISTINCT product_code
        FROM mds_product_stock_point
        WHERE product_classify = 'RA.A'
          AND product_code NOT LIKE '%-DEL'
          AND LENGTH(product_code) &gt;= 16;
    </select>

    <select id="selectYpFactoryCode" resultType="com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO">
        SELECT t2.product_code       productCode,
               t4.product_code       productFactoryCode,
               t4.vehicle_model_code vehicleModelCode
        FROM mds_rou_product_bom t1
                 LEFT JOIN mds_product_stock_point t2 ON t2.id = t1.io_product_id
                 LEFT JOIN mds_rou_product_bom_version t3 ON t3.id = t1.bom_version_id
                 LEFT JOIN mds_product_stock_point t4 ON t4.id = t3.product_id
        WHERE t1.enabled = 'YES'
          AND t2.product_classify = 'RA.A'
          AND LENGTH(t2.product_code) >= 16
    </select>

    <select id="selectMaxEopMinSop" resultType="com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO">
        SELECT
        vehicle_model_code vehicleModelCode,
        MAX( product_eop ) productEop,
        MIN( product_sop ) productSop
        FROM
        mds_product_stock_point
        where enabled = 'YES'
        <if test="params.stockPointCode != null and params.stockPointCode != ''">
            and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
        </if>
        <if test="params.vehicleCodeList != null and params.vehicleCodeList.size() > 0">
            and vehicle_model_code in
            <foreach collection="params.vehicleCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY
        vehicle_model_code
    </select>

    <select id="selectDistinctProductCodesByParams" resultType="java.lang.String">
        select distinct product_code
        from mds_product_stock_point
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectFgProductByProductCodes" parameterType="java.util.List" resultMap="VOResultMap">
        SELECT distinct
            psp.product_code,
            psp.order_planner
        FROM mds_product_stock_point psp
                 LEFT JOIN mds_stock_point sp ON psp.stock_point_code = sp.stock_point_code
        WHERE sp.organize_type = 'SALE_ORGANIZATION'
        and product_code in
        <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectLoadingPositionType" resultType="java.lang.String">
        select distinct loading_position
        from mds_product_stock_point
        where loading_position is not null and loading_position != ''
    </select>
</mapper>
