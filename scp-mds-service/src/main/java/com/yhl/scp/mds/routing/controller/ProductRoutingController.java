package com.yhl.scp.mds.routing.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.product.service.ProductCandidateResourceTimeService;
import com.yhl.scp.mds.routing.dto.ProductRoutingDTO;
import com.yhl.scp.mds.routing.service.*;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.routing.vo.ProductRoutingVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <code>ProductRoutingController</code>
 * <p>
 * 物品工艺路径控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 17:51:45
 */
@Slf4j
@Api(tags = "物品工艺路径控制器")
@RestController
@RequestMapping("productRouting")
public class ProductRoutingController extends BaseController {

    @Resource
    protected RedisUtil redisUtil;
    @Resource
    private ProductRoutingService productRoutingService;
    @Resource
    private ProductCandidateResourceTimeService productCandidateResourceTimeService;
    @Resource
    private SpringBeanUtils springBeanUtils;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<ProductRoutingVO>> page() {
        List<ProductRoutingVO> productRoutingList = productRoutingService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ProductRoutingVO> pageInfo = new PageInfo<>(productRoutingList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ProductRoutingDTO productRoutingDTO) {
        return productRoutingService.doCreate(productRoutingDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ProductRoutingDTO productRoutingDTO) {
        return productRoutingService.doUpdate(productRoutingDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        productRoutingService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<ProductRoutingVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, productRoutingService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "工艺路径数据同步全量")
    @GetMapping(value = "syncAllRouting")
    public BaseResponse<Void> syncAllRouting() {
        String syncKey = RedisKeyManageEnum.MDS_ROUTING_INFO_ALL_SYNC.getKey();
        // 校验锁是否被占用
        if (redisUtil.hasKey(syncKey)) {
            return BaseResponse.error("数据正在同步!");
        }
        try {
            //设置过期时间20分钟
            redisUtil.set(syncKey, SystemHolder.getUserId(), 1200);
            log.info("工艺路径数据全量同步开始");
            productRoutingService.syncAllRouting();
            log.info("工艺路径数据全量同步完成");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("工艺路径数据全量同步失败：" + e);
            return BaseResponse.error("工艺路径数据全量同步失败:" + e);
        } finally {
            redisUtil.delete(syncKey);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "同步")
    @GetMapping(value = "syncResource")
    public BaseResponse<Void> syncMoldChangeTime() {
        IpsNewFeign ipsNewFeign=springBeanUtils.getBean(IpsNewFeign.class);
        ProductRoutingService productRoutingService=springBeanUtils.getBean(ProductRoutingService.class);
        NewRoutingService newRoutingService=springBeanUtils.getBean(NewRoutingService.class);
        NewRoutingStepService newRoutingStepService=springBeanUtils.getBean(NewRoutingStepService.class);
        MdsProductBomService mdsProductBomService=springBeanUtils.getBean(MdsProductBomService.class);
        NewProductCandidateResourceService newProductCandidateResourceService=
                springBeanUtils.getBean(NewProductCandidateResourceService.class);
        NewRoutingStepResourceService newRoutingStepResourceService=
                springBeanUtils.getBean(NewRoutingStepResourceService.class);
        NewStockPointService newStockPointService=springBeanUtils.getBean(NewStockPointService.class);
        NewRoutingStepInputService newRoutingStepInputService=
                springBeanUtils.getBean(NewRoutingStepInputService.class);

        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            log.warn("租户下不存在MDS模块信息");
        }
        for (Scenario scenario : scenarios) {
            log.info("开始处理scenario：{}下的产品工艺路径转物品工艺路径job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            // 1.工艺路径，工艺路径步骤转化处理
            List<ProductRoutingVO> productRoutingList = productRoutingService.selectAll();
            List<String> routingSequenceIds = productRoutingList.stream().map(ProductRoutingVO::getRoutingSequenceId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(routingSequenceIds)) {
                newRoutingService.doTransitionRouting(routingSequenceIds, scenario.getDataBaseName());
            }
            // 2.输入物品
            List<ProductBomVO> productBomVO = mdsProductBomService.selectAll();
            List<String> componentSequenceId = productBomVO.stream().map(ProductBomVO::getComponentSequenceId)
                    .distinct().collect(Collectors.toList());
            List<NewStockPointVO> stockPoints = newStockPointService.selectAll();
            newRoutingStepInputService.doTransitionRoutingStepInput(componentSequenceId, null, null,
                    stockPoints, scenario.getDataBaseName());
            // 3.候选资源
            List<ProductCandidateResourceVO> productCandidateResourceList = newProductCandidateResourceService.selectAll();
            List<String> productCandidateResourceIds = productCandidateResourceList.stream()
                    .map(ProductCandidateResourceVO::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productCandidateResourceIds)) {
                newRoutingStepResourceService.doTransitionRoutingStepResource(productCandidateResourceIds,
                        null, null, stockPoints, scenario.getDataBaseName());
            }}
        return productCandidateResourceTimeService.syncMoldChangeTime(SystemHolder.getTenantCode());
    }
}
