package com.yhl.scp.dfp.loading.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.loading.dto.DemandForecastAttachmentsDTO;
import com.yhl.scp.dfp.loading.vo.DemandForecastAttachmentsVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>DemandForecastAttachmentsService</code>
 * <p>
 * 源文件管理应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-03 14:43:13
 */
public interface DemandForecastAttachmentsService extends BaseService<DemandForecastAttachmentsDTO, DemandForecastAttachmentsVO> {

    /**
     * 查询所有
     *
     * @return list {@link DemandForecastAttachmentsVO}
     */
    List<DemandForecastAttachmentsVO> selectAll();

    void upload(String versionCode, String uploadStatus, Map<String,List<MultipartFile>> fileMap);

    void uploadFile(String versionCode, String oemCode, String uploadStatus, MultipartFile file);

    void download(String id, HttpServletResponse res);

    void remove(String id);

    List<String> listObjects();

}