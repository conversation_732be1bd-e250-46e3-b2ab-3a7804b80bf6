package com.yhl.scp.dfp.loading.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.excel.IExcelDynamicDataImport;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionReqDTO;
import com.yhl.scp.dfp.loading.vo.DemandParsedVO;
import com.yhl.scp.dfp.loading.vo.DemandSubmissionVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.ips.system.entity.Scenario;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <code>LoadingDemandSubmissionService</code>
 * <p>
 * 装车需求提报应用接口
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 10:11:42
 */
public interface LoadingDemandSubmissionService extends BaseService<LoadingDemandSubmissionDTO, LoadingDemandSubmissionVO>, IExcelDynamicDataImport {

    /**
     * 查询所有
     *
     * @return list {@link LoadingDemandSubmissionVO}
     */
    List<LoadingDemandSubmissionVO> selectAll();

    /**
     * 导出模板
     */
    void exportTemplate(HttpServletResponse response, String templateType);

    BaseResponse<Void> submissionData(LoadingDemandSubmissionReqDTO reqDTO);

    BaseResponse<Void> uploadFiles(String originVersionId, String oemCode, String contentType, MultipartFile originFile, MultipartFile submissionFile);

    /**
     * 批量删除
     *
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据本厂编码批量查询
     *
     * @param productCodeList
     * @return
     */
    List<LoadingDemandSubmissionVO> selectByProductCode(List<String> productCodeList);

    void downloadFile(String originVersionId, String oemCode, HttpServletResponse response);

    BaseResponse<Void> updateLoadingSubmissionDetail(List<LoadingDemandSubmissionDetailDTO> loadingDemandSubmissionDetailDTOs);

    BaseResponse<Void> importDemand(String originVersionId, String importType, String templateType,
                                    String demandCategory, String checkFlag, MultipartFile submissionFile);

    BaseResponse<Void> doCreateNewVersion(String scenario, String generateType);

    BaseResponse<Void> syncEdi(Scenario scenario);

    BaseResponse<Void> syncDemand(LoadingDemandSubmissionReqDTO reqDTO);

    BaseResponse<Void> apiSubmission(List<OemVO> ediOemList, String originVersionId, Scenario scenario);

    Map<String, BigDecimal> getProductDemandQuantity(String saleDate);

    BaseResponse<Object> getDemandTypeEnum();

    List<String> selectDisabledProductCodes();

    /**
     * 解析客户原始需求文件
     *
     * @param originVersionId 原始需求ID
     * @param oemId           主机厂ID
     * @param multipartFiles  附件数据
     * @return
     */
    BaseResponse<List<DemandParsedVO>> parseDemandFile(String originVersionId, String oemId, MultipartFile[] multipartFiles);

    /**
     * 保存客户原始需求文件解析数据
     *
     * @param demandParsedVO 原始数据解析结果实体
     * @return
     */
    BaseResponse<Void> saveParsedDemand(DemandSubmissionVO demandParsedVO);
}
