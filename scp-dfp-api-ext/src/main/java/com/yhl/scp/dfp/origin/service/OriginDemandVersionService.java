package com.yhl.scp.dfp.origin.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.origin.dto.OriginDemandVersionDTO;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.dfp.verison.dto.VersionCreateDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>OriginDemandVersionService</code>
 * <p>
 * 原始需求版本应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:06:14
 */
public interface OriginDemandVersionService extends BaseService<OriginDemandVersionDTO, OriginDemandVersionVO> {

    /**
     * 查询所有
     *
     * @return list {@link OriginDemandVersionVO}
     */
    List<OriginDemandVersionVO> selectAll();

    /**
     * 目标原始需求版本
     *
     * @param planPeriod
     * @return
     */
    List<LabelValue<String>> targetVersion(String planPeriod);

    /**
     * 原始需求版本创建
     *
     * @param demandCreateDTO
     */
    BaseResponse<String> createOriginalVersion(VersionCreateDTO demandCreateDTO);

    /**
     * 原始需求版本
     *
     * @param planPeriod
     * @param versionType
     * @return
     */

    /**
     * 批量删除
     *
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    void downloadSubmissionFiles(String versionId, String oemCode, HttpServletResponse response);

    BaseResponse<List<LabelValue<String>>> dropDownOemCode(String targetVersionId);

    int updateSubmissionType(String originVersionId, String oemCode, String contentType);

    BaseResponse<List<LabelValue<String>>> getVersionByForecast(String planPeriod);

    String selectPreVersionId(String planPeriod);

    String selectPreVersionIdByLike(String planPeriod);

    String selectLatestVersionId();

    /**
     * 获取计划期间内的最新版本
     * @param planPeriod
     * @return
     */
    OriginDemandVersionVO selectLastVersionByPlanPeriod(String planPeriod);

}
