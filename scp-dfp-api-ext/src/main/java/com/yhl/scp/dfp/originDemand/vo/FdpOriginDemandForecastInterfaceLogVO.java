package com.yhl.scp.dfp.originDemand.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>FdpOriginDemandForecastInterfaceLogVO</code>
 * <p>
 * Edi装车需求GRP接口同步记录表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 18:26:35
 */
@ApiModel(value = "Edi装车需求GRP接口同步记录表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FdpOriginDemandForecastInterfaceLogVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 879088901973992081L;

    /**
     * 预测集
     */
    @ApiModelProperty(value = "预测集")
    @FieldInterpretation(value = "预测集")
    private String forecastSet;
    @ApiModelProperty(value = "${column.comment}")
    @FieldInterpretation(value = "${column.comment}")
    private String ediLocation;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @FieldInterpretation(value = "客户名称")
    private String custName;
    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    @FieldInterpretation(value = "收货地址")
    private String siteAddress1;
    /**
     * 本厂编号
     */
    @ApiModelProperty(value = "本厂编号")
    @FieldInterpretation(value = "本厂编号")
    private String itemNum;
    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    @FieldInterpretation(value = "库存组织ID")
    private String orgId;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @FieldInterpretation(value = "数量")
    private BigDecimal qty;
    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型")
    @FieldInterpretation(value = "计划类型")
    private String scheduleType;
    /**
     * 原始需求日期从
     */
    @ApiModelProperty(value = "原始需求日期从")
    @FieldInterpretation(value = "原始需求日期从")
    private Date originalSchStDate;
    /**
     * 原始需求日期至
     */
    @ApiModelProperty(value = "原始需求日期至")
    @FieldInterpretation(value = "原始需求日期至")
    private Date originalSchEndDate;
    /**
     * 报文原始发货时间
     */
    @ApiModelProperty(value = "报文原始发货时间")
    @FieldInterpretation(value = "报文原始发货时间")
    private Date originalShipTime;
    /**
     * 报文原始交货时间
     */
    @ApiModelProperty(value = "报文原始交货时间")
    @FieldInterpretation(value = "报文原始交货时间")
    private Date originalDeliveryTime;
    /**
     * 预测开始日期(计算后的日期)
     */
    @ApiModelProperty(value = "预测开始日期(计算后的日期)")
    @FieldInterpretation(value = "预测开始日期(计算后的日期)")
    private Date forecastDateStart;
    /**
     * 预测结束日期(计算后的日期)
     */
    @ApiModelProperty(value = "预测结束日期(计算后的日期)")
    @FieldInterpretation(value = "预测结束日期(计算后的日期)")
    private Date forecastDateEnd;
    /**
     * ERP客户ID
     */
    @ApiModelProperty(value = "ERP客户ID")
    @FieldInterpretation(value = "ERP客户ID")
    private String ebsCustomerId;
    /**
     * ERP地址ID
     */
    @ApiModelProperty(value = "ERP地址ID")
    @FieldInterpretation(value = "ERP地址ID")
    private String ebsSiteId;
    /**
     * 客户零件号
     */
    @ApiModelProperty(value = "客户零件号")
    @FieldInterpretation(value = "客户零件号")
    private String customerItemNum;
    /**
     * 提前备料
     */
    @ApiModelProperty(value = "提前备料")
    @FieldInterpretation(value = "提前备料")
    private String materialsAdv;
    /**
     * 预测创建日期
     */
    @ApiModelProperty(value = "预测创建日期")
    @FieldInterpretation(value = "预测创建日期")
    private Date forecastCreationDate;
    /**
     * 预测版本号
     */
    @ApiModelProperty(value = "预测版本号")
    @FieldInterpretation(value = "预测版本号")
    private String releaseNum;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 日需求/月需求 DAY/MONTH
     */
    @ApiModelProperty(value = "日需求/月需求 DAY/MONTH")
    @FieldInterpretation(value = "日需求/月需求 DAY/MONTH")
    private String submissionType;
    /**
     * GRP/MES
     */
    @ApiModelProperty(value = "GRP/MES")
    @FieldInterpretation(value = "GRP/MES")
    private String importType;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    /**
     * 接口流水号
     */
    @ApiModelProperty(value = "接口流水号")
    @FieldInterpretation(value = "接口流水号")
    private String serialNum;
    /**
     * 业务实体ID(组织ID)
     */
    @ApiModelProperty(value = "业务实体ID(组织ID)")
    @FieldInterpretation(value = "业务实体ID(组织ID)")
    private String buId;
    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    @FieldInterpretation(value = "组织编码")
    private String organizationCode;
    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    @FieldInterpretation(value = "组织名称")
    private String organizationName;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @FieldInterpretation(value = "客户名称")
    private String partyName;
    /**
     * 客户地址
     */
    @ApiModelProperty(value = "客户地址")
    @FieldInterpretation(value = "客户地址")
    private String siteName;
    /**
     * 已制单数
     */
    @ApiModelProperty(value = "已制单数")
    @FieldInterpretation(value = "已制单数")
    private BigDecimal orderedQty;
    /**
     * SO地址ID
     */
    @ApiModelProperty(value = "SO地址ID")
    @FieldInterpretation(value = "SO地址ID")
    private String erpPartySiteId;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @FieldInterpretation(value = "业务类型")
    private String businessType;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "kid")
    @FieldInterpretation(value = "kid")
    private String kid;
    /**
     * 客户工厂
     */
    @ApiModelProperty(value = "plantCode")
    @FieldInterpretation(value = "plantCode")
    private String plantCode;
    /**
     * 物料EDI标识
     */
    @ApiModelProperty(value = "物料EDI标识")
    @FieldInterpretation(value = "物料EDI标识")
    private String productEdiFlag;
    @Override
    public void clean() {

    }

}
