package com.yhl.scp.dfp.loading.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <code>LoadingDemandSubmissionDTO</code>
 * <p>
 * 装车需求提报DTO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 10:11:42
 */
@ApiModel(value = "装车需求提报DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LoadingDemandSubmissionDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -92907352600620439L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 原始版本ID
     */
    @ApiModelProperty(value = "原始版本ID")
    private String versionId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String demandCategory;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂零件号
     */
    @ApiModelProperty(value = "主机厂零件号")
    private String partNumber;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 提报明细
     */
    @ApiModelProperty(value = "提报明细")
    private List<LoadingDemandSubmissionDetailDTO> details;

    /**
     * 导入时间
     */
    @ApiModelProperty(value = "导入时间")
    private Date importTime;

    /**
     * 上次导入时间
     */
    @ApiModelProperty(value = "上次导入时间")
    private Date lastImportTime;
    
    /**
     * 上次导入时间
     */
    @ApiModelProperty(value = "上次导入时间")
    private BigDecimal historyDemandQuantity;

}
