package com.yhl.scp.dfp.loading.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>LoadingDemandSubmissionDetailVO</code>
 * <p>
 * 装车需求提报详情VO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 14:29:12
 */
@ApiModel(value = "装车需求提报详情VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoadingDemandSubmissionDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 636552543592810929L;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @FieldInterpretation(value = "主表ID")
    private String submissionId;
    /**
     * 日需求/月需求
     */
    @ApiModelProperty(value = "日需求/月需求")
    @FieldInterpretation(value = "日需求/月需求")
    private String submissionType;
    /**
     * 需求时间：日期/月份
     */
    @ApiModelProperty(value = "需求时间：日期/月份")
    @FieldInterpretation(value = "需求时间：日期/月份")
    private String demandTime;
    /**
     * 原始需求时间：日期
     */
    @ApiModelProperty(value = "原始需求时间：日期", hidden = true)
    @FieldInterpretation(value = "原始需求时间：日期")
    private String originDemandTime;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    private BigDecimal demandQuantity;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @FieldInterpretation(value = "需求类型")
    private String demandCategory;
    /**
     * 原始需求版本ID
     */
    @ApiModelProperty(value = "原始需求版本ID")
    @FieldInterpretation(value = "原始需求版本ID")
    private String versionId;
    
    /**
     * 单元格背景颜色
     */
    @ApiModelProperty(value = "单元格背景颜色")
    @FieldInterpretation(value = "单元格背景颜色")
    private String backgroundColor;
    
    @Override
    public void clean() {

    }
}
