package com.yhl.scp.dfp.loading.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>DemandForecastAttachmentsVO</code>
 * <p>
 * 源文件管理VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-03 14:43:13
 */
@ApiModel(value = "源文件管理VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DemandForecastAttachmentsVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 153746928911295158L;

    /**
     * 原始需求版本号
     */
    @ApiModelProperty(value = "原始需求版本号")
    @FieldInterpretation(value = "原始需求版本号")
    private String versionCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    @FieldInterpretation(value = "文件名")
    private String fileName;
    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    @FieldInterpretation(value = "文件路径")
    private String filePath;

	/**
	 * 文件类型
	 */
	@ApiModelProperty(value = "文件路径")
	private String fileType;
	
	/**
	 * 导入状态
	 */
	@ApiModelProperty(value = "文件路径")
	private String uploadStatus;

    @Override
    public void clean() {

    }

}
