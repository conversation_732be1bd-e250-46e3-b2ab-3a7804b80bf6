package com.yhl.scp.dfp.report.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportVO;

import java.util.List;

/**
 * <code>CustomerOrderWeeklyReportService</code>
 * <p>
 * CustomerOrderWeeklyReportService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-12 16:22:11
 */
public interface CustomerOrderWeeklyReportService {

    List<LabelValue<String>> selectVersionDropdown();

    List<String> selectWeekSequence(String versionId);

    List<CustomerOrderWeeklyReportVO> selectByVersionId(String versionId);

    List<CustomerOrderWeeklyReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam,
                                                   String versionId);

    List<CustomerOrderWeeklyReportVO> selectByCondition(String sortParam, String queryCriteriaParam, String versionId);

}