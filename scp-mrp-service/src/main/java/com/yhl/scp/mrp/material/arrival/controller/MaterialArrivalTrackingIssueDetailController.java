package com.yhl.scp.mrp.material.arrival.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingIssueDetailDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingIssueDetailService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalTrackingIssueDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialArrivalTrackingIssueDetailController</code>
 * <p>
 * 材料到货跟踪下发明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 10:26:57
 */
@Slf4j
@Api(tags = "材料到货跟踪下发明细控制器")
@RestController
@RequestMapping("materialArrivalTrackingIssueDetail")
public class MaterialArrivalTrackingIssueDetailController extends BaseController {

    @Resource
    private MaterialArrivalTrackingIssueDetailService materialArrivalTrackingIssueDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MaterialArrivalTrackingIssueDetailVO>> page() {
        List<MaterialArrivalTrackingIssueDetailVO> materialArrivalTrackingIssueDetailList = materialArrivalTrackingIssueDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialArrivalTrackingIssueDetailVO> pageInfo = new PageInfo<>(materialArrivalTrackingIssueDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "分页查询（版本）")
    @GetMapping(value = "pageNoVersion")
    public BaseResponse<PageInfo<MaterialArrivalTrackingIssueDetailVO>> pageNoVersion(@RequestParam(value = "materialArrivalTrackingNo", required = false) String materialArrivalTrackingNo) {
        List<MaterialArrivalTrackingIssueDetailVO> materialArrivalTrackingIssueDetailList = materialArrivalTrackingIssueDetailService.selectByPageNoVersion(getPagination(), materialArrivalTrackingNo);
        PageInfo<MaterialArrivalTrackingIssueDetailVO> pageInfo = new PageInfo<>(materialArrivalTrackingIssueDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialArrivalTrackingIssueDetailDTO materialArrivalTrackingIssueDetailDTO) {
        return materialArrivalTrackingIssueDetailService.doCreate(materialArrivalTrackingIssueDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialArrivalTrackingIssueDetailDTO materialArrivalTrackingIssueDetailDTO) {
        return materialArrivalTrackingIssueDetailService.doUpdate(materialArrivalTrackingIssueDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialArrivalTrackingIssueDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MaterialArrivalTrackingIssueDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialArrivalTrackingIssueDetailService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "单号下拉")
    @GetMapping(value = "noDropDown")
    public BaseResponse<List<LabelValue<String>>> noDropDown() {
        return BaseResponse.success(materialArrivalTrackingIssueDetailService.noDropDown());
    }
}
