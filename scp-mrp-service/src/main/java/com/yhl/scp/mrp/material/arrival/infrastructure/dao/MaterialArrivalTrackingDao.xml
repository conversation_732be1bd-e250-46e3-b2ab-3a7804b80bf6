<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialArrivalTrackingDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingPO">
        <!--@Table mrp_material_arrival_tracking-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="purchase_order_code" jdbcType="VARCHAR" property="purchaseOrderCode"/>
        <result column="purchase_order_line_code" jdbcType="VARCHAR" property="purchaseOrderLineCode"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="require_date" jdbcType="TIMESTAMP" property="requireDate"/>
        <result column="recommend_require_date" jdbcType="TIMESTAMP" property="recommendRequireDate"/>
        <result column="require_quantity" jdbcType="VARCHAR" property="requireQuantity"/>
        <result column="wait_delivery_quantity" jdbcType="VARCHAR" property="waitDeliveryQuantity"/>
        <result column="product_unit" jdbcType="VARCHAR" property="productUnit"/>
        <result column="predict_arrival_date" jdbcType="TIMESTAMP" property="predictArrivalDate"/>
        <result column="predict_arrival_quantity" jdbcType="VARCHAR" property="predictArrivalQuantity"/>
        <result column="arrival_status" jdbcType="VARCHAR" property="arrivalStatus"/>
        <result column="approval_status" jdbcType="VARCHAR" property="approvalStatus"/>
        <result column="cancel_flag" jdbcType="VARCHAR" property="cancelFlag"/>
        <result column="closed_code" jdbcType="VARCHAR" property="closedCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="delivery_note_code" jdbcType="VARCHAR" property="deliveryNoteCode"/>
        <result column="shipping_date" jdbcType="TIMESTAMP" property="shippingDate"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="inventory_quantity" jdbcType="VARCHAR" property="inventoryQuantity"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="update_reason" jdbcType="VARCHAR" property="updateReason"/>
        <result column="publish_status" jdbcType="VARCHAR" property="publishStatus"/>
        <result column="qty_billed" jdbcType="VARCHAR" property="qtyBilled"/>
        <result column="manual_note" jdbcType="VARCHAR" property="manualNote"/>
        <result column="ticket_num" jdbcType="VARCHAR" property="ticketNum"/>
        <result column="return_qty" jdbcType="VARCHAR" property="returnQty"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO">
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="product_category" jdbcType="VARCHAR" property="productCategory"/>
        <result column="material_plan_need_no" jdbcType="VARCHAR" property="materialPlanNeedNo"/>
        <result column="purchase_request_code" jdbcType="VARCHAR" property="purchaseRequestCode"/>
        <result column="purchase_request_line_code" jdbcType="VARCHAR" property="purchaseRequestLineCode"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,
		source_id,
		data_source,
		purchase_order_code,
        purchase_order_line_code,
		product_id,
		stock_point_code,
		material_code,
		material_name,
		require_date,
        recommend_require_date,
		require_quantity,
		wait_delivery_quantity,
		product_unit,
		predict_arrival_date,
		predict_arrival_quantity,
		arrival_status,
		approval_status,
		cancel_flag,
		closed_code,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time,
		version_value,
		delivery_note_code,
		shipping_date,
        supplier_code,
        supplier_name,
        inventory_quantity,
        parent_id,
        update_reason,
        publish_status,
        qty_billed,
        manual_note,
        ticket_num,
        return_qty
    </sql>

    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>
        ,supply_type,product_category,material_plan_need_no,purchase_request_code,purchase_request_line_code,organization_id
    </sql>

    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.sourceId != null and params.sourceId != ''">
                and source_id = #{params.sourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.sourceIds != null and params.sourceIds.size() > 0">
                and source_id in
                <foreach collection="params.sourceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.dataSource != null and params.dataSource != ''">
                and data_source = #{params.dataSource,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderCode != null and params.purchaseOrderCode != ''">
                and purchase_order_code = #{params.purchaseOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderCodes != null and params.purchaseOrderCodes.size() > 0">
                and purchase_order_code in
                <foreach collection="params.purchaseOrderCodes" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.purchaseOrderLineCode != null and params.purchaseOrderLineCode != ''">
                and purchase_order_line_code = #{params.purchaseOrderLineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCode != null and params.materialCode != ''">
                and material_code = #{params.materialCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCodes != null and params.materialCodes.size() > 0">
                and material_code in
                <foreach collection="params.materialCodes" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.materialName != null and params.materialName != ''">
                and material_name = #{params.materialName,jdbcType=VARCHAR}
            </if>
            <if test="params.requireDate != null">
                and require_date = #{params.requireDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.recommendRequireDate != null">
                and recommend_require_date = #{params.recommendRequireDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.requireQuantity != null">
                and require_quantity = #{params.requireQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.waitDeliveryQuantity != null">
                and wait_delivery_quantity = #{params.waitDeliveryQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.productUnit != null and params.productUnit != ''">
                and product_unit = #{params.productUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.predictArrivalDate != null">
                and predict_arrival_date = #{params.predictArrivalDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.predictArrivalQuantity != null">
                and predict_arrival_quantity = #{params.predictArrivalQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.arrivalStatus != null and params.arrivalStatus != ''">
                and arrival_status = #{params.arrivalStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.approvalStatus != null and params.approvalStatus != ''">
                and approval_status = #{params.approvalStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.cancelFlag != null and params.cancelFlag != ''">
                and cancel_flag = #{params.cancelFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.closedCode != null and params.closedCode != ''">
                and closed_code = #{params.closedCode,jdbcType=VARCHAR}
            </if>
            <if test="params.qtyBilled != null">
                and qty_billed = #{params.qtyBilled,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.combineKeys != null and params.combineKeys.size() > 0">
                and CONCAT_WS('_',purchase_order_code,purchase_order_line_code,material_code) in
                <foreach collection="params.combineKeys" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.combineKeys02 != null and params.combineKeys02.size() > 0">
                and CONCAT_WS('_',purchase_order_code,purchase_order_line_code,delivery_note_code,ticket_num,material_code) in
                <foreach collection="params.combineKeys02" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.combineKeys03 != null and params.combineKeys03.size() > 0">
                and CONCAT_WS('_',purchase_order_code,purchase_order_line_code) in
                <foreach collection="params.combineKeys03" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.combineKeys04 != null and params.combineKeys04.size() > 0">
                and CONCAT_WS('_',purchase_request_code,purchase_request_line_code) in
                <foreach collection="params.combineKeys04" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.deliveryNoteCode != null and deliveryNoteCode.modifier != ''">
                and delivery_note_code = #{params.deliveryNoteCode,jdbcType=VARCHAR}
            </if>
            <if test="params.shippingDate != null">
                and shipping_date = #{params.shippingDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                and supplier_name = #{params.supplierName,jdbcType=VARCHAR}
            </if>
            <if test="params.waitDeliveryQuantityGreaterZero != null and params.waitDeliveryQuantityGreaterZero == 'YES'">
                and wait_delivery_quantity > 0
            </if>
            <if test="params.inventoryQuantity != null">
                and inventory_quantity = #{params.inventoryQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.parentId != null and params.parentId != ''">
                and parent_id = #{params.parentId,jdbcType=VARCHAR}
            </if>
            <if test="params.updateReason != null and params.updateReason != ''">
                and update_reason = #{params.updateReason,jdbcType=VARCHAR}
            </if>
            <if test="params.publishStatus != null and params.publishStatus != ''">
                and publish_status = #{params.publishStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.manualNote != null and params.manualNote != ''">
                and manual_note = #{params.manualNote,jdbcType=VARCHAR}
            </if>
            <if test="params.ticketNum != null and params.ticketNum != ''">
                and ticket_num = #{params.ticketNum,jdbcType=VARCHAR}
            </if>
            <if test="params.returnQty != null">
                and return_qty = #{params.returnQty,jdbcType=VARCHAR}
            </if>
            <if test="params.startModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{params.startModifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_tracking
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_tracking
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_arrival_tracking
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_tracking
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mrp_material_arrival_tracking
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectNeedClose" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        mrp_material_arrival_tracking ,
        (
        select order_header,order_line from mrp_ord_purchase_order_info t
        where t.line_status != 'OPEN' and t.quantity - t.quantity_received > 0
        ) t2
        where purchase_order_code = t2.order_header
        and purchase_order_line_code = t2.order_line
        and arrival_status = 'PLAN_PRUCHASE'
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_arrival_tracking(
        id,
        source_id,
        data_source,
        purchase_order_code,
        purchase_order_line_code,
        product_id,
        stock_point_code,
        material_code,
        material_name,
        recommend_require_date,
        require_date,
        require_quantity,
        wait_delivery_quantity,
        product_unit,
        predict_arrival_date,
        predict_arrival_quantity,
        arrival_status,
        approval_status,
        cancel_flag,
        closed_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        delivery_note_code,
        shipping_date,
        supplier_code,
        supplier_name,
        inventory_quantity,
        parent_id,
        update_reason,
        publish_status,
        qty_billed,
        manual_note,
        ticket_num,
        return_qty)
        values (
        #{id,jdbcType=VARCHAR},
        #{sourceId,jdbcType=VARCHAR},
        #{dataSource,jdbcType=VARCHAR},
        #{purchaseOrderCode,jdbcType=VARCHAR},
        #{purchaseOrderLineCode,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{materialCode,jdbcType=VARCHAR},
        #{materialName,jdbcType=VARCHAR},
        #{recommendRequireDate,jdbcType=TIMESTAMP},
        #{requireDate,jdbcType=TIMESTAMP},
        #{requireQuantity,jdbcType=VARCHAR},
        #{waitDeliveryQuantity,jdbcType=VARCHAR},
        #{productUnit,jdbcType=VARCHAR},
        #{predictArrivalDate,jdbcType=TIMESTAMP},
        #{predictArrivalQuantity,jdbcType=VARCHAR},
        #{arrivalStatus,jdbcType=VARCHAR},
        #{approvalStatus,jdbcType=VARCHAR},
        #{cancelFlag,jdbcType=VARCHAR},
        #{closedCode,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{deliveryNoteCode,jdbcType=VARCHAR},
        #{shippingDate,jdbcType=TIMESTAMP},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{inventoryQuantity,jdbcType=VARCHAR},
        #{parentId,jdbcType=VARCHAR},
        #{updateReason,jdbcType=VARCHAR},
        #{publishStatus,jdbcType=VARCHAR},
        #{qtyBilled,jdbcType=VARCHAR},
        #{manualNote,jdbcType=VARCHAR},
        #{ticketNum,jdbcType=VARCHAR},
        #{returnQty,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingPO">
        insert into mrp_material_arrival_tracking(id,
                                                  source_id,
                                                  data_source,
                                                  purchase_order_code,
                                                  purchase_order_line_code,
                                                  product_id,
                                                  stock_point_code,
                                                  material_code,
                                                  material_name,
                                                  require_date,
                                                  recommend_require_date,
                                                  require_quantity,
                                                  wait_delivery_quantity,
                                                  product_unit,
                                                  predict_arrival_date,
                                                  predict_arrival_quantity,
                                                  arrival_status,
                                                  approval_status,
                                                  cancel_flag,
                                                  closed_code,
                                                  remark,
                                                  enabled,
                                                  creator,
                                                  create_time,
                                                  modifier,
                                                  modify_time,
                                                  version_value,
                                                  delivery_note_code,
                                                  shipping_date,
                                                  supplier_code,
                                                  supplier_name,
                                                  inventory_quantity,
                                                  parent_id,
                                                  update_reason,
                                                  publish_status,
                                                  qty_billed,
                                                  manual_note,
                                                  ticket_num,
                                                  return_qty)
        values (#{id,jdbcType=VARCHAR},
                #{sourceId,jdbcType=VARCHAR},
                #{dataSource,jdbcType=VARCHAR},
                #{purchaseOrderCode,jdbcType=VARCHAR},
                #{purchaseOrderLineCode,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{materialCode,jdbcType=VARCHAR},
                #{materialName,jdbcType=VARCHAR},
                #{requireDate,jdbcType=TIMESTAMP},
                #{recommendRequireDate,jdbcType=TIMESTAMP},
                #{requireQuantity,jdbcType=VARCHAR},
                #{waitDeliveryQuantity,jdbcType=VARCHAR},
                #{productUnit,jdbcType=VARCHAR},
                #{predictArrivalDate,jdbcType=TIMESTAMP},
                #{predictArrivalQuantity,jdbcType=VARCHAR},
                #{arrivalStatus,jdbcType=VARCHAR},
                #{approvalStatus,jdbcType=VARCHAR},
                #{cancelFlag,jdbcType=VARCHAR},
                #{closedCode,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{deliveryNoteCode,jdbcType=VARCHAR},
                #{shippingDate,jdbcType=TIMESTAMP},
                #{supplierCode,jdbcType=VARCHAR},
                #{supplierName,jdbcType=VARCHAR},
                #{inventoryQuantity,jdbcType=VARCHAR},
                #{parentId,jdbcType=VARCHAR},
                #{updateReason,jdbcType=VARCHAR},
                #{publishStatus,jdbcType=VARCHAR},
                #{qtyBilled,jdbcType=VARCHAR},
                #{manualNote,jdbcType=VARCHAR},
                #{ticketNum,jdbcType=VARCHAR},
                #{returnQty,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_arrival_tracking(
        id,
        source_id,
        data_source,
        purchase_order_code,
        purchase_order_line_code,
        product_id,
        stock_point_code,
        material_code,
        material_name,
        require_date,
        recommend_require_date,
        require_quantity,
        wait_delivery_quantity,
        product_unit,
        predict_arrival_date,
        predict_arrival_quantity,
        arrival_status,
        approval_status,
        cancel_flag,
        closed_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        delivery_note_code,
        shipping_date,
        supplier_code,
        supplier_name,
        inventory_quantity,
        parent_id,
        update_reason,
        publish_status,
        qty_billed,
        manual_note,
        ticket_num,
        return_qty)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.sourceId,jdbcType=VARCHAR},
            #{entity.dataSource,jdbcType=VARCHAR},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.materialCode,jdbcType=VARCHAR},
            #{entity.materialName,jdbcType=VARCHAR},
            #{entity.requireDate,jdbcType=TIMESTAMP},
            #{entity.recommendRequireDate,jdbcType=TIMESTAMP},
            #{entity.requireQuantity,jdbcType=VARCHAR},
            #{entity.waitDeliveryQuantity,jdbcType=VARCHAR},
            #{entity.productUnit,jdbcType=VARCHAR},
            #{entity.predictArrivalDate,jdbcType=TIMESTAMP},
            #{entity.predictArrivalQuantity,jdbcType=VARCHAR},
            #{entity.arrivalStatus,jdbcType=VARCHAR},
            #{entity.approvalStatus,jdbcType=VARCHAR},
            #{entity.cancelFlag,jdbcType=VARCHAR},
            #{entity.closedCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.deliveryNoteCode,jdbcType=VARCHAR},
            #{entity.shippingDate,jdbcType=TIMESTAMP},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.inventoryQuantity,jdbcType=VARCHAR},
            #{entity.parentId,jdbcType=VARCHAR},
            #{entity.updateReason,jdbcType=VARCHAR},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.qtyBilled,jdbcType=VARCHAR},
            #{entity.manualNote,jdbcType=VARCHAR},
            #{entity.ticketNum,jdbcType=VARCHAR},
            #{entity.returnQty,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_arrival_tracking(
        id,
        source_id,
        data_source,
        purchase_order_code,
        purchase_order_line_code,
        product_id,
        stock_point_code,
        material_code,
        material_name,
        require_date,
        recommend_require_date,
        require_quantity,
        wait_delivery_quantity,
        product_unit,
        predict_arrival_date,
        predict_arrival_quantity,
        arrival_status,
        approval_status,
        cancel_flag,
        closed_code,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        delivery_note_code,
        shipping_date,
        supplier_code,
        supplier_name,
        inventory_quantity,
        parent_id,
        update_reason,
        publish_status,
        qty_billed,
        manual_note,
        ticket_num,
        return_qty)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.sourceId,jdbcType=VARCHAR},
            #{entity.dataSource,jdbcType=VARCHAR},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.materialCode,jdbcType=VARCHAR},
            #{entity.materialName,jdbcType=VARCHAR},
            #{entity.requireDate,jdbcType=TIMESTAMP},
            #{entity.recommendRequireDate,jdbcType=TIMESTAMP},
            #{entity.requireQuantity,jdbcType=VARCHAR},
            #{entity.waitDeliveryQuantity,jdbcType=VARCHAR},
            #{entity.productUnit,jdbcType=VARCHAR},
            #{entity.predictArrivalDate,jdbcType=TIMESTAMP},
            #{entity.predictArrivalQuantity,jdbcType=VARCHAR},
            #{entity.arrivalStatus,jdbcType=VARCHAR},
            #{entity.approvalStatus,jdbcType=VARCHAR},
            #{entity.cancelFlag,jdbcType=VARCHAR},
            #{entity.closedCode,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.deliveryNoteCode,jdbcType=VARCHAR},
            #{entity.shippingDate,jdbcType=TIMESTAMP},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.inventoryQuantity,jdbcType=VARCHAR},
            #{entity.parentId,jdbcType=VARCHAR},
            #{entity.updateReason,jdbcType=VARCHAR},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.qtyBilled,jdbcType=VARCHAR},
            #{entity.manualNote,jdbcType=VARCHAR},
            #{entity.ticketNum,jdbcType=VARCHAR},
            #{entity.returnQty,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingPO">
        update mrp_material_arrival_tracking
        set source_id                = #{sourceId,jdbcType=VARCHAR},
            data_source              = #{dataSource,jdbcType=VARCHAR},
            purchase_order_code      = #{purchaseOrderCode,jdbcType=VARCHAR},
            purchase_order_line_code = #{purchaseOrderLineCode,jdbcType=VARCHAR},
            product_id               = #{productId,jdbcType=VARCHAR},
            stock_point_code         = #{stockPointCode,jdbcType=VARCHAR},
            material_code            = #{materialCode,jdbcType=VARCHAR},
            material_name            = #{materialName,jdbcType=VARCHAR},
            require_date             = #{requireDate,jdbcType=TIMESTAMP},
            recommend_require_date   = #{recommendRequireDate,jdbcType=TIMESTAMP},
            require_quantity         = #{requireQuantity,jdbcType=VARCHAR},
            wait_delivery_quantity   = #{waitDeliveryQuantity,jdbcType=VARCHAR},
            product_unit             = #{productUnit,jdbcType=VARCHAR},
            predict_arrival_date     = #{predictArrivalDate,jdbcType=TIMESTAMP},
            predict_arrival_quantity = #{predictArrivalQuantity,jdbcType=VARCHAR},
            arrival_status           = #{arrivalStatus,jdbcType=VARCHAR},
            approval_status          = #{approvalStatus,jdbcType=VARCHAR},
            cancel_flag              = #{cancelFlag,jdbcType=VARCHAR},
            closed_code              = #{closedCode,jdbcType=VARCHAR},
            remark                   = #{remark,jdbcType=VARCHAR},
            enabled                  = #{enabled,jdbcType=VARCHAR},
            modifier                 = #{modifier,jdbcType=VARCHAR},
            modify_time              = #{modifyTime,jdbcType=TIMESTAMP},
            version_value            = #{versionValue,jdbcType=INTEGER},
            delivery_note_code       = #{deliveryNoteCode,jdbcType=VARCHAR},
            shipping_date            = #{shippingDate,jdbcType=TIMESTAMP},
            supplier_code            = #{supplierCode,jdbcType=VARCHAR},
            supplier_name            = #{supplierName,jdbcType=VARCHAR},
            inventory_quantity       = #{inventoryQuantity,jdbcType=VARCHAR},
            parent_id                = #{parentId,jdbcType=VARCHAR},
            update_reason            = #{updateReason,jdbcType=VARCHAR},
            publish_status           = #{publishStatus,jdbcType=VARCHAR},
            qty_billed               = #{qtyBilled,jdbcType=VARCHAR},
            manual_note              = #{manualNote,jdbcType=VARCHAR},
            ticket_num               = #{ticketNum,jdbcType=VARCHAR},
            return_qty               = #{returnQty,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingPO">
        update mrp_material_arrival_tracking
        <set>
            <if test="item.sourceId != null and item.sourceId != ''">
                source_id = #{item.sourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSource != null and item.dataSource != ''">
                data_source = #{item.dataSource,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialCode != null and item.materialCode != ''">
                material_code = #{item.materialCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialName != null and item.materialName != ''">
                material_name = #{item.materialName,jdbcType=VARCHAR},
            </if>
            <if test="item.requireDate != null">
                require_date = #{item.requireDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.recommendRequireDate != null">
                recommend_require_date = #{item.recommendRequireDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.requireQuantity != null">
                require_quantity = #{item.requireQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.waitDeliveryQuantity != null">
                wait_delivery_quantity = #{item.waitDeliveryQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.productUnit != null and item.productUnit != ''">
                product_unit = #{item.productUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.predictArrivalDate != null">
                predict_arrival_date = #{item.predictArrivalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.predictArrivalQuantity != null">
                predict_arrival_quantity = #{item.predictArrivalQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.arrivalStatus != null and item.arrivalStatus != ''">
                arrival_status = #{item.arrivalStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.approvalStatus != null and item.approvalStatus != ''">
                approval_status = #{item.approvalStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.cancelFlag != null and item.cancelFlag != ''">
                cancel_flag = #{item.cancelFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.closedCode != null and item.closedCode != ''">
                closed_code = #{item.closedCode,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.deliveryNoteCode != null and item.modifier != ''">
                delivery_note_code = #{item.deliveryNoteCode,jdbcType=VARCHAR},
            </if>
            <if test="item.shippingDate != null">
                shipping_date = #{item.shippingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null and item.supplierName != ''">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryQuantity != null">
                inventory_quantity = #{item.inventoryQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
            <if test="item.updateReason != null and item.updateReason != ''">
                update_reason = #{item.updateReason,jdbcType=VARCHAR},
            </if>
            <if test="item.publishStatus != null and item.publishStatus != ''">
                publish_status = #{item.publishStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.qtyBilled != null">
                qty_billed = #{item.qtyBilled,jdbcType=VARCHAR},
            </if>
            <if test="item.manualNote != null and item.manualNote != ''">
                manual_note = #{item.manualNote,jdbcType=VARCHAR},
            </if>
            <if test="item.ticketNum != null and item.ticketNum != ''">
                ticket_num = #{item.ticketNum,jdbcType=VARCHAR},
            </if>
            <if test="item.returnQty != null">
                return_qty = #{item.returnQty,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_arrival_tracking
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="source_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dataSource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderLineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="require_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requireDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="require_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requireQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="recommend_require_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.recommendRequireDate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="wait_delivery_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.waitDeliveryQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="predict_arrival_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.predictArrivalDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="predict_arrival_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.predictArrivalQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="arrival_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.arrivalStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="approval_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.approvalStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cancel_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cancelFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="closed_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.closedCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="delivery_note_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryNoteCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shipping_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shippingDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.updateReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="qty_billed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.qtyBilled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="manual_note = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.manualNote,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ticket_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ticketNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="return_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.returnQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_arrival_tracking
            <set>
                <if test="item.sourceId != null and item.sourceId != ''">
                    source_id = #{item.sourceId,jdbcType=VARCHAR},
                </if>
                <if test="item.dataSource != null and item.dataSource != ''">
                    data_source = #{item.dataSource,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                    purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                    purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productId != null and item.productId != ''">
                    product_id = #{item.productId,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCode != null and item.materialCode != ''">
                    material_code = #{item.materialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialName != null and item.materialName != ''">
                    material_name = #{item.materialName,jdbcType=VARCHAR},
                </if>
                <if test="item.requireDate != null">
                    require_date = #{item.requireDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.recommendRequireDate != null">
                    recommend_require_date = #{item.recommendRequireDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.requireQuantity != null">
                    require_quantity = #{item.requireQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.waitDeliveryQuantity != null">
                    wait_delivery_quantity = #{item.waitDeliveryQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.productUnit != null and item.productUnit != ''">
                    product_unit = #{item.productUnit,jdbcType=VARCHAR},
                </if>
                <if test="item.predictArrivalDate != null">
                    predict_arrival_date = #{item.predictArrivalDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.predictArrivalQuantity != null">
                    predict_arrival_quantity = #{item.predictArrivalQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.arrivalStatus != null and item.arrivalStatus != ''">
                    arrival_status = #{item.arrivalStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.approvalStatus != null and item.approvalStatus != ''">
                    approval_status = #{item.approvalStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.cancelFlag != null and item.cancelFlag != ''">
                    cancel_flag = #{item.cancelFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.closedCode != null and item.closedCode != ''">
                    closed_code = #{item.closedCode,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.deliveryNoteCode != null and item.modifier != ''">
                    delivery_note_code = #{item.deliveryNoteCode,jdbcType=VARCHAR},
                </if>
                <if test="item.shippingDate != null">
                    shipping_date = #{item.shippingDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierName != null and item.supplierName != ''">
                    supplier_name = #{item.supplierName,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryQuantity != null">
                    inventory_quantity = #{item.inventoryQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.parentId != null and item.parentId != ''">
                    parent_id = #{item.parentId,jdbcType=VARCHAR},
                </if>
                <if test="item.updateReason != null and item.updateReason != ''">
                    update_reason = #{item.updateReason,jdbcType=VARCHAR},
                </if>
                <if test="item.publishStatus != null and item.publishStatus != ''">
                    publish_status = #{item.publishStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.qtyBilled != null">
                    qty_billed = #{item.qtyBilled,jdbcType=VARCHAR},
                </if>
                <if test="item.manualNote != null and item.manualNote != ''">
                    manual_note = #{item.manualNote,jdbcType=VARCHAR},
                </if>
                <if test="item.ticketNum != null and item.ticketNum != ''">
                    ticket_num = #{item.ticketNum,jdbcType=VARCHAR},
                </if>
                <if test="item.returnQty != null">
                    return_qty = #{item.returnQty,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_arrival_tracking
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_arrival_tracking where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="doUpdateBatchBySourceId" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update
            mrp_material_arrival_tracking
            set
            purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
            purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
            cancel_flag = #{item.cancelFlag,jdbcType=VARCHAR},
            closed_code = #{item.closedCode,jdbcType=VARCHAR},
            qty_billed = #{item.qtyBilled,jdbcType=VARCHAR},
            modifier = #{item.modifier,jdbcType=VARCHAR},
            modify_time = #{item.modifyTime,jdbcType=TIMESTAMP}
            where
            source_id = #{item.sourceId,jdbcType=VARCHAR}
        </foreach>
    </update>

</mapper>
