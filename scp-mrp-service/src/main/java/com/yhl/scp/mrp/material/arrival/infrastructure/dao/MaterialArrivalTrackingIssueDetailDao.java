package com.yhl.scp.mrp.material.arrival.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingIssueDetailPO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalTrackingIssueDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>MaterialArrivalTrackingIssueDetailDao</code>
 * <p>
 * 材料到货跟踪下发明细DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 10:26:57
 */
public interface MaterialArrivalTrackingIssueDetailDao extends BaseDao<MaterialArrivalTrackingIssueDetailPO, MaterialArrivalTrackingIssueDetailVO> {

    List<MaterialArrivalTrackingIssueDetailVO> selectByPageNoVersion(@Param("materialArrivalTrackingNo") String materialArrivalTrackingNo);

}
