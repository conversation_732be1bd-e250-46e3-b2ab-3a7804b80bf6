package com.yhl.scp.mrp.material.purchase.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPrCreate;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.assignment.dto.MaterialDemandAssignmentDTO;
import com.yhl.scp.mrp.enums.*;
import com.yhl.scp.mrp.extension.material.vo.MaterialPurchaseRequirementDetailVO;
import com.yhl.scp.mrp.material.arrival.dto.SyncMaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedService;
import com.yhl.scp.mrp.material.plan.service.impl.NoGlassMrpServiceImpl;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.material.purchase.convertor.MaterialPurchaseRequirementDetailConvertor;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseRequirementDetailDO;
import com.yhl.scp.mrp.material.purchase.domain.service.MaterialPurchaseRequirementDetailDomainService;
import com.yhl.scp.mrp.material.purchase.dto.*;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseRequirementDetailDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseRequirementDetailPO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementDetailService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementIssueService;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementRelationService;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialPurchaseRequirementDetailServiceImpl</code>
 * <p>
 * 材料采购需求明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 19:00:59
 */
@Slf4j
@Service
public class MaterialPurchaseRequirementDetailServiceImpl extends AbstractService implements MaterialPurchaseRequirementDetailService {

    @Resource
    private MaterialPurchaseRequirementDetailDao materialPurchaseRequirementDetailDao;

    @Resource
    private MaterialPurchaseRequirementDetailDomainService materialPurchaseRequirementDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private MaterialPurchaseRequirementIssueService materialPurchaseRequirementIssueService;

    @Resource
    private MaterialPurchaseRequirementRelationService materialPurchaseRequirementRelationService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private MaterialPlanNeedService materialPlanNeedService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NoGlassMrpServiceImpl noGlassMrpService;

    @Override
    public BaseResponse<Void> doCreate(MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO) {
        // 0.数据转换
        MaterialPurchaseRequirementDetailDO materialPurchaseRequirementDetailDO = MaterialPurchaseRequirementDetailConvertor.INSTANCE.dto2Do(materialPurchaseRequirementDetailDTO);
        MaterialPurchaseRequirementDetailPO materialPurchaseRequirementDetailPO = MaterialPurchaseRequirementDetailConvertor.INSTANCE.dto2Po(materialPurchaseRequirementDetailDTO);
        // 1.数据校验
        materialPurchaseRequirementDetailDomainService.validation(materialPurchaseRequirementDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPurchaseRequirementDetailPO);
        materialPurchaseRequirementDetailPO.setRequireDate(materialPurchaseRequirementDetailDTO.getRequireDate());
        materialPurchaseRequirementDetailPO.setRequireQuantity(materialPurchaseRequirementDetailDTO.getRequireQuantity());
        materialPurchaseRequirementDetailPO.setDataSource(PurchaseRequirementDataSourceEnum.MANUAL_ADDITION.getCode());
        materialPurchaseRequirementDetailDao.insertWithPrimaryKey(materialPurchaseRequirementDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO) {
        // 0.数据转换
        MaterialPurchaseRequirementDetailDO materialPurchaseRequirementDetailDO = MaterialPurchaseRequirementDetailConvertor.INSTANCE.dto2Do(materialPurchaseRequirementDetailDTO);
        MaterialPurchaseRequirementDetailPO materialPurchaseRequirementDetailPO = MaterialPurchaseRequirementDetailConvertor.INSTANCE.dto2Po(materialPurchaseRequirementDetailDTO);
        // 1.数据校验
        materialPurchaseRequirementDetailDomainService.validation(materialPurchaseRequirementDetailDO);
        MaterialPurchaseRequirementDetailPO currentPO = materialPurchaseRequirementDetailDao.selectByPrimaryKey(materialPurchaseRequirementDetailDTO.getId());
        if (StringUtils.isNotEmpty(currentPO.getMaterialPurchaseRequirementIssueId())) {
            throw new BusinessException("材料采购需求已下发,不支持修改!");
        }
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseRequirementDetailPO);
        materialPurchaseRequirementDetailDao.updateSelective(materialPurchaseRequirementDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPurchaseRequirementDetailDTO> list) {
        List<MaterialPurchaseRequirementDetailPO> newList = MaterialPurchaseRequirementDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPurchaseRequirementDetailDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPurchaseRequirementDetailDTO> list) {
        List<MaterialPurchaseRequirementDetailPO> newList = MaterialPurchaseRequirementDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseRequirementDetailDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPurchaseRequirementDetailDao.deleteBatch(idList);
        }
        return materialPurchaseRequirementDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPurchaseRequirementDetailVO selectByPrimaryKey(String id) {
        MaterialPurchaseRequirementDetailPO po = materialPurchaseRequirementDetailDao.selectByPrimaryKey(id);
        return MaterialPurchaseRequirementDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_purchase_requirement_detail")
    public List<MaterialPurchaseRequirementDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
//        List<Role> roleList = ipsNewFeign.selectRoleListByUserId(SystemHolder.getUserId());
//        List<String> roleNameList = roleList.stream().map(Role::getRoleName).distinct().collect(Collectors.toList());
//        // 含有材料计划员、PVB计划员则需要加查询条件
//        if (roleNameList.contains("材料计划员") || roleNameList.contains("B/PVB计划员")) {
//            // 过滤材料计划员权限
//            String queryCondition = String.format(" find_in_set('%s', material_planner) > 0", SystemHolder.getUserId());
//            if (com.yhl.platform.common.utils.StringUtils.isEmpty(queryCriteriaParam)) {
//                queryCriteriaParam = queryCondition;
//            } else {
//                queryCriteriaParam = queryCriteriaParam + " and" + queryCondition;
//            }
//        }
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_purchase_requirement_detail")
    public List<MaterialPurchaseRequirementDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPurchaseRequirementDetailVO> dataList = materialPurchaseRequirementDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPurchaseRequirementDetailServiceImpl target = springBeanUtils.getBean(MaterialPurchaseRequirementDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPurchaseRequirementDetailVO> selectByParams(Map<String, Object> params) {
        List<MaterialPurchaseRequirementDetailPO> list = materialPurchaseRequirementDetailDao.selectByParams(params);
        return MaterialPurchaseRequirementDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPurchaseRequirementDetailVO> selectVOByParams(Map<String, Object> params) {
        return materialPurchaseRequirementDetailDao.selectVOByParams(params);
    }

    @Override
    public List<MaterialPurchaseRequirementDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_PURCHASE_REQUIREMENT_DETAIL.getCode();
    }

    @Override
    public List<MaterialPurchaseRequirementDetailVO> invocation(List<MaterialPurchaseRequirementDetailVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public String doBatchIssue(List<String> ids) {
        if (Boolean.TRUE.equals(redisUtil.hasKey(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_ISSUE.getKey()))) {
            throw new BusinessException("材料采购需求正在下发，请稍后操作");
        }
        List<MaterialPurchaseRequirementDetailVO> chooseIssueList =
                materialPurchaseRequirementDetailDao.selectVOByParams(MapUtil.of("ids", ids));

        chooseIssueList = chooseIssueList.stream().filter(x ->
                        IssueStatusEnum.UN_ISSUED.getCode().equals(x.getIssueStatus())
                                || IssueStatusEnum.ISSUED_FAIL.getCode().equals(x.getIssueStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chooseIssueList)) {
            throw new BusinessException("勾选的材料采购需求已全部下发，无需重复下发");
        }

        List<String> supplierCodeNullDataList = chooseIssueList.stream()
                .filter(item -> StringUtils.isBlank(item.getSupplierCode()))
                .map(MaterialPurchaseRequirementDetailVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());

        // 锁定7分钟
        List<String> unIssuedIds = chooseIssueList.stream().map(MaterialPurchaseRequirementDetailVO::getId)
                .collect(Collectors.toList());
        log.info("当前id{}", JSONObject.toJSONString(unIssuedIds));
        redisUtil.set(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_ISSUE.getKey(), String.join(",", unIssuedIds), 420);
        log.info("redis当前值{}", redisUtil.get(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_ISSUE.getKey()).toString());
        redisUtil.set(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_USER.getKey(), SystemHolder.getUserId(), 420);
        try {
            // 材料采购下发创建PR数据
            List<ErpPrCreate> erpPrCreateList = new ArrayList<>();
            initErpPrCreateList(chooseIssueList, erpPrCreateList);
            // 材料采购需求下发ERP
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("createList", erpPrCreateList);
            BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(SystemHolder.getTenantCode(),
                    ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.PR_CREATE.getCode(), params);
            log.info("材料采购下发响应：" + JSONObject.toJSONString(baseResponse));
            // 结果解析
            if (Boolean.TRUE.equals(baseResponse.getSuccess()) && baseResponse.getData() != null) {
                // 创建PR成功后，更新状态
                String data = baseResponse.getData();
                log.info("材料采购需求,获取接口日志请求:{}", data);
                ExtApiLogVO extApiLog = newDcpFeign.getExtApiLog(data);
                log.info("材料采购需求,获取接口日志响应:{}", JSONObject.toJSONString(extApiLog));
                Integer resolveCount = extApiLog.getResolveCount();
                Integer applyCount = extApiLog.getApplyCount();
                String remark = extApiLog.getRemark();
                String responseBody = extApiLog.getResponseBody();

                log.info("材料采购需求, applyCount数据值：{}", applyCount);
                log.info("材料采购需求, remark数据值：{}", remark);
                List<String> productCodes = new ArrayList<>();
                if (applyCount != null && applyCount > 0 && StringUtils.isNotBlank(remark)) {
                    List<String> detailIds = Lists.newArrayList(remark.split(","));
                    for (MaterialPurchaseRequirementDetailVO item : chooseIssueList) {
                        boolean successFlag = detailIds.contains(item.getId());
                        String productCode = item.getProductCode();
                        item.setIssueStatus(successFlag ? IssueStatusEnum.ISSUED_SUCCESS.getCode() : IssueStatusEnum.ISSUED_FAIL.getCode());
                        if (!successFlag && !productCodes.contains(productCode)) {
                            productCodes.add(productCode);
                        }
                    }
                }

//                MaterialPurchaseRequirementDetailConvertor instance =
//                        MaterialPurchaseRequirementDetailConvertor.INSTANCE;
//                List<MaterialPurchaseRequirementDetailDTO> updateList = instance.vo2Dtos(chooseIssueList);
//                Lists.partition(updateList, 500).forEach(this::doUpdateBatch);
                String result = "共%d条数据，成功%d条，失败%d条";
                result = String.format(result, resolveCount, applyCount, resolveCount - (applyCount == null ? 0 : applyCount));
                if (!Objects.equals(resolveCount, applyCount)) {
                    // responseBody转化为对象
                    ExtApiLogResponse erpPrCreateResponse = JSONObject.parseObject(responseBody, ExtApiLogResponse.class);
                    List<StringBuilder> list = new ArrayList<>();
                    if (null != erpPrCreateResponse && CollectionUtils.isNotEmpty(erpPrCreateResponse.getData())) {
                        for (ExtApiLogResponseData responseData : erpPrCreateResponse.getData()) {
                            StringBuilder stringBuilder = new StringBuilder();
                            stringBuilder.append("物料：").append(responseData.getItemNumber());
                            if (null != responseData.getNeedByDate()) {
                                stringBuilder.append("，需求日期：").append(DateUtils.dateToString(responseData.getNeedByDate(), DateUtils.COMMON_DATE_STR3));
                            }
                            stringBuilder.append("，原因").append(responseData.getValidataMsg());
                            list.add(stringBuilder);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(list)) {
                        result = result + "，失败明细 《" + String.join("；", list) + "》";
                    }
                }

                if (CollectionUtils.isNotEmpty(supplierCodeNullDataList)) {
                    result = result + String.join(",", supplierCodeNullDataList) + ";供应商为空";
                }
//                if (!Objects.equals(resolveCount, applyCount) && CollectionUtils.isNotEmpty(productCodes)) {
//                    String failureProductMessage = "，失败的物料有：%s";
//                    result = result + String.format(failureProductMessage, String.join(",", productCodes));
//                }
                return result;
            } else {
                log.error("材料采购需求下发报错，请查看最新对外接口日志");
                throw new BusinessException("材料采购需求下发报错，请查看最新对外接口日志");
            }
        } catch (Exception e) {
            log.error("材料采购需求下发报错", e);
            throw new BusinessException("材料采购需求下发报错：" + e.getMessage());
        } finally {
            redisUtil.delete(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_ISSUE.getKey());
            redisUtil.delete(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_USER.getKey());

            // TODO 进行mrp推移
//            log.info("材料采购需求下发完成,进行材料mrp计算");
//            MrpParamDTO mrpParamDTO = new MrpParamDTO();
//            mrpParamDTO.setDemandSource("DELIVERY_PLAN_DEMAND");
//            mrpParamDTO.setWhetherAutomatic(Boolean.FALSE);
//            mrpParamDTO.setCalcStartTime(DateUtils.getDayFirstTime(new Date()));
//            List<String> computeProductCodeList = chooseIssueList.stream()
//                    .map(MaterialPurchaseRequirementDetailVO::getProductCode)
//                    .distinct().collect(Collectors.toList());
//            mrpParamDTO.setComputeProductCodeList(computeProductCodeList);
//            noGlassMrpService.doRunMrp(mrpParamDTO);

            // 刷新mrp结果
//            CompletableFuture.runAsync(() -> {
//                try {
//                    String executionSequence = UUIDUtil.getUUID();
//                    log.info("$$$$$$$$$$$$$$$$$$$$$ 到货跟踪剩余待发货数量发生变更，开始刷新MRP计算结果, 执行序列编码:{}", executionSequence);
//                    noGlassMrpService.doRunMrp(MrpCalcTypeEnum.NO_GLASS.getCode(), scenario, DateUtils.dateToString(new Date()));
//                    log.info("$$$$$$$$$$$$$$$$$$$$$ 到货跟踪剩余待发货数量发生变更，刷新MRP计算结果结束, 执行序列编码：{}", executionSequence);
//                } catch (Exception e) {
//                    log.error("MRP材料推移失败", e);
//                }
//            });
//
//            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//                @Override
//                public void afterCommit() {
//                    //需要提交之后执行的代码
//                    log.info("MRP材料推移");
//                }
//            });
        }
    }

    private void initErpPrCreateList(List<MaterialPurchaseRequirementDetailVO> chooseIssueList,
                                     List<ErpPrCreate> erpPrCreateList) {
        List<String> stockPointCodes = chooseIssueList.stream().map(MaterialPurchaseRequirementDetailVO::getStockPointCode)
                .distinct().collect(Collectors.toList());
        Map<String, String> stockPointMap = getStockPointMapByCodes(stockPointCodes);
        String issueBatchCode = DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR4 + "SSS");
        chooseIssueList.forEach(e -> {
            ErpPrCreate erpPrCreate = new ErpPrCreate();
            erpPrCreate.setOrganizationName(stockPointMap.get(e.getStockPointCode()));
            erpPrCreate.setDeliverToLocation(erpPrCreate.getOrganizationName());
            erpPrCreate.setHeaderDescription("材料采购需求");
            erpPrCreate.setErpUserJobNumber(SystemHolder.getUserName());
            erpPrCreate.setItemNumber(e.getProductCode());
            erpPrCreate.setQuantity(e.getRequireQuantity().intValue());
            erpPrCreate.setUnitPrice(0);
            erpPrCreate.setNeedByDate(DateUtils.dateToString(e.getRequireDate(), DateUtils.COMMON_DATE_STR3));
            erpPrCreate.setIsAutoApprove("Y");
            erpPrCreate.setInterfaceSourceCode("BPIM");
            erpPrCreate.setBatchId(issueBatchCode);
            erpPrCreate.setBpimLineNumber(e.getId());
            erpPrCreateList.add(erpPrCreate);
        });
    }

    /**
     * 初始化到户跟踪数据
     *
     * @param chooseIssueList
     * @param syncArrivalList
     */
    private void initArrivalTrackingList(List<MaterialPurchaseRequirementDetailVO> chooseIssueList,
                                         List<SyncMaterialArrivalTrackingDTO> syncArrivalList) {
        chooseIssueList.forEach(e -> {
            //维护货跟踪数据
            SyncMaterialArrivalTrackingDTO arrivalTracking = SyncMaterialArrivalTrackingDTO.builder()
                    .sourceId(e.getId())
                    .stockPointCode(e.getStockPointCode())
                    .materialCode(e.getProductCode())
                    .materialName(e.getProductName())
                    .requireDate(e.getRequireDate())
                    .requireQuantity(e.getRequireQuantity())
                    .productUnit(e.getProductUnit())
                    .dataSource(ArrivalTrackingDataSourceEnum.MATERIAL_PURCHASE.getCode())
                    .supplierCode(e.getSupplierCode())
                    .supplierName(e.getSupplierName())
                    .publishStatus(ArrivalTrackingPublishStatusEnum.PUBLISHED.getCode())
                    .build();
            syncArrivalList.add(arrivalTracking);
        });
    }

    private Map<String, String> getStockPointMapByCodes(List<String> stockPointCodes) {

        List<ScenarioBusinessRangeVO> rangeData = ipsNewFeign.selectScenarioBusinessByParams(ImmutableMap.of("rangeData", stockPointCodes.get(0)));
//        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(),
//                TenantCodeEnum.FYQB.getCode());
        List<NewStockPointVO> stockPointList = newMdsFeign.selectStockPointByParams(rangeData.get(0).getScenario(),
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "stockPointCodes", stockPointCodes));
        Map<String, String> stockPointMap = stockPointList.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, NewStockPointVO::getStockPointName, (value1, value2) -> value1));
        return stockPointMap;
    }

    /**
     * 初始化下发记录
     *
     * @return
     */
    private MaterialPurchaseRequirementIssueDTO initIssueLog(String userId) {
        MaterialPurchaseRequirementIssueDTO issueLogDto = MaterialPurchaseRequirementIssueDTO.builder()
                .id(UUIDUtil.getUUID())
                .issueBatchCode("PD" + DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR4 + "SSS"))
                .issuer(userId)
                .issueStatus(IssueStatusEnum.ISSUED_SUCCESS.getCode())
                .issueTime(new Date())
                .build();
        return issueLogDto;
    }

    @Override
    public Set<String> doSyncMaterialPurchase(List<NoGlassInventoryShiftDataVO> shiftDataList,
                                              List<NoGlassInventoryShiftDetailVO> materialPlanInventoryShiftVOList,
                                              List<PurchaseOrderInfoVO> purchaseOrderInfoVOList, Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap,
                                              Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup, Map<String, SupplierVO> supplierVOMapOfId,
                                              String versionCode, List<String> permissionCodeList) {

        Set<String> errorMegList = new HashSet<>();
        Set<String> notSupplierProductList = new HashSet<>();
        Map<String, NoGlassInventoryShiftDataVO> shiftDataVOMap = shiftDataList.stream()
                .collect(Collectors.toMap(NoGlassInventoryShiftDataVO::getId, Function.identity()));

        materialPlanInventoryShiftVOList = materialPlanInventoryShiftVOList.stream()
                .filter(item -> item.getPlanPurchase().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialPlanInventoryShiftVOList)) {
            return errorMegList;
        }
        Map<String, List<NoGlassInventoryShiftDetailVO>> syncMaterialPurchaseMap = materialPlanInventoryShiftVOList.stream()
                .collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId));
        //2.处理要货数量：要考虑采购批量的信息，作为创建PR单的约束
        List<MaterialPurchaseRequirementDetailDTO> requirementDetailList = new ArrayList<>();
        List<MaterialPurchaseRequirementRelationDTO> requirementRelationList = new ArrayList<>();
        List<MaterialDemandAssignmentDTO> insertMaterialDemandAssignmentDTOList = new ArrayList<>();

        for (Entry<String, List<NoGlassInventoryShiftDetailVO>> syncEntry : syncMaterialPurchaseMap.entrySet()) {
            List<MaterialPurchaseRequirementDetailDTO> parentMaterialPurchaseRequirementDetailDTOList = new ArrayList<>();
            NoGlassInventoryShiftDataVO shiftDataVO = shiftDataVOMap.get(syncEntry.getKey());

            String productCode = shiftDataVO.getProductCode();
            List<NoGlassInventoryShiftDetailVO> syncDataList = syncEntry.getValue();
            syncDataList.sort(Comparator.comparing(NoGlassInventoryShiftDetailVO::getInventoryDate));
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMap.get(productCode);
            // 最小起运量
            BigDecimal minOrderQty = materialSupplierPurchaseVO.getMinOrderQty();
            // 包装批量
            BigDecimal packageLot = materialSupplierPurchaseVO.getPackageLot();
            // 订单下达提前期
            int orderPlacementLeadTimeDay = null != materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay() ? materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().intValue() : 0;

            // 组装默认供应商采购比例
            List<SupplierPurchaseRatioVO> defaultSupplierPurchaseRatioList = getDefaultSupplierPurchaseRatioList();
            // 获取供应商采购比例
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup
                    .getOrDefault(materialSupplierPurchaseVO.getId(), defaultSupplierPurchaseRatioList);
            if (CollectionUtils.isEmpty(syncDataList)) {
                continue;
            }
            // 按照采购比例排序
            supplierPurchaseRatioVOList.sort(
                    Comparator.nullsLast(Comparator.comparing(SupplierPurchaseRatioVO::getPurchaseRatio).reversed())
            );

            for (NoGlassInventoryShiftDetailVO noGlassInventoryShiftVO : syncDataList) {
                if (null == noGlassInventoryShiftVO.getPlanPurchase() || noGlassInventoryShiftVO.getPlanPurchase().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 剩余计划采购量
                BigDecimal remainderPlanPurchase = noGlassInventoryShiftVO.getPlanPurchase();
                // 按照供应商采购比例生成要货计划
                for (int i = 0; i < supplierPurchaseRatioVOList.size(); i++) {
                    SupplierPurchaseRatioVO supplierPurchaseRatioVO = supplierPurchaseRatioVOList.get(i);
                    // 采购比例
                    BigDecimal purchaseRatio = BigDecimal.ONE;
//                    if (null != supplierPurchaseRatioVO.getPurchaseRatio()) {
//                        purchaseRatio = supplierPurchaseRatioVO.getPurchaseRatio();
//                    }
                    // 没有供应商也可以生成采购需求
                    SupplierVO supplierVO = supplierVOMapOfId.get(supplierPurchaseRatioVO.getSupplierId());
                    if (null == supplierVO) {
                        notSupplierProductList.add(productCode);
                    }

//                    BigDecimal planPurchase = noGlassInventoryShiftVO.getPlanPurchase();
//                    planPurchase = planPurchase.multiply(purchaseRatio);
//                    // 最后一个供应商取剩余量，为0则并不生成
//                    if ((i == supplierPurchaseRatioVOList.size() - 1 && remainderPlanPurchase.compareTo(BigDecimal.ZERO) == 0) || remainderPlanPurchase.compareTo(BigDecimal.ZERO) < 0) {
//                        continue;
//                    }
//                    if (null != materialSupplierPurchaseVO.getPackageLot() && i != supplierPurchaseRatioVOList.size() - 1) {
//                        // 按照包装批量向上取整
//                        planPurchase = materialPlanNeedService.roundUpwards(planPurchase, materialSupplierPurchaseVO.getPackageLot());
//                    }
//                    if (i == supplierPurchaseRatioVOList.size() - 1) {
//                        // 最后一家取剩余量
//                        planPurchase = remainderPlanPurchase;
//                    }
//                    remainderPlanPurchase = remainderPlanPurchase.subtract(planPurchase);
                    // 生成采购需求
                    MaterialPurchaseRequirementDetailDTO mergeMaterialPurchaseRequirementDetailDTO = this.getMergeMaterialPurchaseRequirementDetailDTO(
                            noGlassInventoryShiftVO.getInventoryDate(), remainderPlanPurchase, purchaseRatio, supplierVO, orderPlacementLeadTimeDay);
                    mergeMaterialPurchaseRequirementDetailDTO.setNoGlassInventoryShiftId(noGlassInventoryShiftVO.getId());
                    mergeMaterialPurchaseRequirementDetailDTO.setProductCode(productCode);
                    mergeMaterialPurchaseRequirementDetailDTO.setInventoryShiftVersionCode(versionCode);
                    parentMaterialPurchaseRequirementDetailDTOList.add(mergeMaterialPurchaseRequirementDetailDTO);

                    // 采购计划不考虑多供应商
                    break;
                }
            }

            if (CollectionUtils.isNotEmpty(parentMaterialPurchaseRequirementDetailDTOList)) {
                // 数量需要满足批量和最小采购量
                for (MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO : parentMaterialPurchaseRequirementDetailDTOList) {
                    materialPurchaseRequirementDetailDTO.setMinOrderQty(minOrderQty);
                    materialPurchaseRequirementDetailDTO.setPackageLot(packageLot);
                    materialPurchaseRequirementDetailDTO.setProductCode(productCode);
                    materialPurchaseRequirementDetailDTO.setDataSource(PurchaseRequirementDataSourceEnum.PLAN_PURCHASE_ADDITION.getCode());
                    requirementDetailList.add(materialPurchaseRequirementDetailDTO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(requirementDetailList)) {
            List<String> planNeedProductCodeList = requirementDetailList.stream()
                    .map(MaterialPurchaseRequirementDetailDTO::getProductCode).distinct().collect(Collectors.toList());

            List<MaterialPurchaseRequirementDetailDTO> insertList = new ArrayList<>();
            List<MaterialPurchaseRequirementDetailDTO> updateList = new ArrayList<>();
            // 供需匹配
//			assignmentPurchaseOrder(requirementDetailList, purchaseOrderInfoVOList, insertMaterialDemandAssignmentDTOList);
            // 删除未下发的数据（改为权限下所有未发布的物料）
//            materialPurchaseRequirementDetailDao.deleteNotIssue(SystemHolder.getUserId(), null);
            materialPurchaseRequirementDetailDao.deleteNotIssueAndProductCodeList(null, permissionCodeList);

//            List<MaterialPurchaseRequirementDetailVO> dataBaseMaterialPurchaseRequirementDetailVOList = materialPurchaseRequirementDetailDao
//                    .selectVOByParams(ImmutableMap.of("productCodeList", planNeedProductCodeList));
//
//            Map<String, MaterialPurchaseRequirementDetailVO> databaseMaterialPlanNeedVOMapOfJoinKey = dataBaseMaterialPurchaseRequirementDetailVOList.stream()
//                    .collect(Collectors.toMap(item -> String.join("#",
//                                    item.getProductCode(), item.getSupplierCode(), DateUtils.dateToString(item.getRequireDate(), "yyyyMMdd")),
//                            Function.identity(), (k1, k2) -> k2));

//            if (CollectionUtils.isNotEmpty(dataBaseMaterialPurchaseRequirementDetailVOList)) {
//                for (MaterialPurchaseRequirementDetailDTO detailDTO : requirementDetailList) {
//                    String joinKey = String.join("#",
//                            detailDTO.getProductCode(), detailDTO.getSupplierCode(), DateUtils.dateToString(detailDTO.getRequireDate(), "yyyyMMdd"));
//
//                    MaterialPurchaseRequirementDetailVO materialPurchaseRequirementDetailVO = databaseMaterialPlanNeedVOMapOfJoinKey.get(joinKey);
//                    if (null != materialPurchaseRequirementDetailVO) {
//                        MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO2 = MaterialPurchaseRequirementDetailConvertor.INSTANCE.vo2Dto(materialPurchaseRequirementDetailVO);
//                        if (detailDTO.getRequireQuantity().compareTo(BigDecimal.ZERO) <= 0) {
//                            materialPurchaseRequirementDetailDTO2.setRequireQuantity(materialPurchaseRequirementDetailVO.getRequireQuantity().add(detailDTO.getRequireQuantity()));
//                        } else {
//                            materialPurchaseRequirementDetailDTO2.setRequireQuantity(detailDTO.getRequireQuantity());
//                            materialPurchaseRequirementDetailDTO2.setPackageLot(detailDTO.getPackageLot());
//                            materialPurchaseRequirementDetailDTO2.setMinOrderQty(detailDTO.getMinOrderQty());
//                        }
//                        updateList.add(materialPurchaseRequirementDetailDTO2);
//                    } else {
//                        insertList.add(detailDTO);
//                    }
//                }
//            } else {
            insertList = requirementDetailList;
//            }

            if (CollectionUtils.isNotEmpty(insertList)) {
                insertList = insertList.stream()
                        .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MaterialPurchaseRequirementDetailDTO::getId))), ArrayList::new));
//                computeRequireQuantity(requirementDetailList);
                this.doCreateBatch(insertList);
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
//                computeRequireQuantity(updateList);
                this.doUpdateBatch(updateList);
            }

        }
        if (CollectionUtils.isNotEmpty(requirementRelationList)) {
            //材料采购需求关系表数据
            materialPurchaseRequirementRelationService.doCreateBatch(requirementRelationList);
        }

        if (CollectionUtils.isNotEmpty(notSupplierProductList)) {
            String errorMessage = "%s没有维护供应商";
            errorMegList.add(String.format(errorMessage, notSupplierProductList));
        }
        return errorMegList;
    }

    private List<SupplierPurchaseRatioVO> getDefaultSupplierPurchaseRatioList() {
        List<SupplierPurchaseRatioVO> defaultSupplierPurchaseRatioList = new ArrayList<>();
        SupplierPurchaseRatioVO defaultSupplierPurchaseRatioVO = new SupplierPurchaseRatioVO();
        defaultSupplierPurchaseRatioList.add(defaultSupplierPurchaseRatioVO);
        return defaultSupplierPurchaseRatioList;
    }

    @Override
    public MaterialPurchaseRequirementDetailDTO getMergeMaterialPurchaseRequirementDetailDTO(Date requireDate, BigDecimal requireQuantitySum, BigDecimal purchaseRatio,
                                                                                             SupplierVO supplierVO, int orderPlacementLeadTimeDay) {
        String uuid = UUIDUtil.getUUID();
//        log.info("材料采购需求,生成的uuid:{}", uuid);
        MaterialPurchaseRequirementDetailDTO mergeMaterialPurchaseRequirementDetailDTO = new MaterialPurchaseRequirementDetailDTO();
        mergeMaterialPurchaseRequirementDetailDTO.setId(uuid);
        mergeMaterialPurchaseRequirementDetailDTO.setRequireDate(requireDate);
        // 汇总要货数量
        mergeMaterialPurchaseRequirementDetailDTO.setRequireQuantity(requireQuantitySum);
        mergeMaterialPurchaseRequirementDetailDTO.setSupplierCode(null != supplierVO ? supplierVO.getSupplierCode() : null);
        mergeMaterialPurchaseRequirementDetailDTO.setSupplierName(null != supplierVO ? supplierVO.getSupplierName() : null);
        mergeMaterialPurchaseRequirementDetailDTO.setRequirementReleaseDate(DateUtils.moveDay(requireDate, -orderPlacementLeadTimeDay));
        return mergeMaterialPurchaseRequirementDetailDTO;
    }

    private void assignmentPurchaseOrder(List<MaterialPurchaseRequirementDetailDTO> requirementDetailList, List<PurchaseOrderInfoVO> purchaseOrderInfoVOList, List<MaterialDemandAssignmentDTO> insertMaterialDemandAssignmentDTOList) {
        // 1、要货计划，按照要货日期排序
        requirementDetailList.sort(Comparator.comparing(MaterialPurchaseRequirementDetailDTO::getRequireDate));
        // 2、采购PO，按照采购承诺到货日期排序
        purchaseOrderInfoVOList.sort(Comparator.comparing(PurchaseOrderInfoVO::getPromisedDate));
        // 采购PO的已分配数量
        Map<String, BigDecimal> purchaseOrderUseQuantityMap = new HashMap<>();
        for (MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO : requirementDetailList) {
            BigDecimal requireQuantity = materialPurchaseRequirementDetailDTO.getRequireQuantity();
            for (PurchaseOrderInfoVO purchaseOrderInfoVO : purchaseOrderInfoVOList) {
                BigDecimal availableQuantity = purchaseOrderInfoVO.getQuantity();
                BigDecimal useQuantity = purchaseOrderUseQuantityMap.getOrDefault(purchaseOrderInfoVO.getId(), BigDecimal.ZERO);
                availableQuantity = availableQuantity.subtract(useQuantity);
                if (availableQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                // 进行分配数量
                // 要货数量小于等于采购数量
                if (requireQuantity.compareTo(availableQuantity) <= 0) {
                    MaterialDemandAssignmentDTO materialDemandAssignmentDTO = assembleMaterialDemandAssignmentDTO(materialPurchaseRequirementDetailDTO, purchaseOrderInfoVO, requireQuantity);
                    useQuantity = useQuantity.add(requireQuantity);
                    purchaseOrderUseQuantityMap.put(purchaseOrderInfoVO.getId(), useQuantity);
                    insertMaterialDemandAssignmentDTOList.add(materialDemandAssignmentDTO);
                    break;
                }
                // 要货数量大于采购数量
                if (requireQuantity.compareTo(availableQuantity) > 0) {
                    MaterialDemandAssignmentDTO materialDemandAssignmentDTO = assembleMaterialDemandAssignmentDTO(materialPurchaseRequirementDetailDTO, purchaseOrderInfoVO, availableQuantity);
                    useQuantity = useQuantity.add(availableQuantity);
                    purchaseOrderUseQuantityMap.put(purchaseOrderInfoVO.getId(), useQuantity);
                    requireQuantity = requireQuantity.subtract(useQuantity);
                    insertMaterialDemandAssignmentDTOList.add(materialDemandAssignmentDTO);
                }
            }
        }
    }

    private MaterialDemandAssignmentDTO assembleMaterialDemandAssignmentDTO(MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO, PurchaseOrderInfoVO purchaseOrderInfoVO, BigDecimal assignmentQuantity) {
        MaterialDemandAssignmentDTO materialDemandAssignmentDTO = new MaterialDemandAssignmentDTO();
        materialDemandAssignmentDTO.setId(UUIDUtil.getUUID());
        materialDemandAssignmentDTO.setMaterialDemandId(materialPurchaseRequirementDetailDTO.getId());
        materialDemandAssignmentDTO.setPurchaseOrderInfoId(purchaseOrderInfoVO.getId());
        materialDemandAssignmentDTO.setAssignmentQuantity(assignmentQuantity);
        return materialDemandAssignmentDTO;
    }

    @Override
    public MaterialPurchaseRequirementDetailVO selectDetailByPrimaryKey(String id) {
        MaterialPurchaseRequirementDetailVO detailVO = this.selectByPrimaryKey(id);
        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointByIds(
                SystemHolder.getScenario(), Arrays.asList(detailVO.getProductId()));
        if (CollUtil.isNotEmpty(productList)) {
            NewProductStockPointVO newProductStockPointVO = productList.get(0);
            detailVO.setProductCode(newProductStockPointVO.getProductCode());
            detailVO.setProductName(newProductStockPointVO.getProductName());
            detailVO.setStockPointCode(newProductStockPointVO.getStockPointCode());
        }
        return detailVO;
    }

    @Override
    public BaseResponse<Void> handleMaterialPurchaseRequirementDetail(List<ErpPrCreate> erpPrCreateList) {
        log.info(erpPrCreateList.size() + "：" + erpPrCreateList.get(0).getRequisitionHeaderId());
        log.info("erpPrCreateList:{}", JSON.toJSONString(erpPrCreateList));
        Map<String, ErpPrCreate> erpPrCreateMap = erpPrCreateList.stream()
                .collect(Collectors.toMap(e -> String.join("&", e.getOrganizationName(),
                                e.getItemNumber(), e.getNeedByDate().substring(0, 10)),
                        e -> e, (v1, v2) -> v1));
        //材料采购需求下发成功后维护采购单号行号等数据信息
        List<String> issuedIds = Arrays.asList(redisUtil.get(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_ISSUE.getKey()).toString().split(","));
        log.info("redis当前值{}", redisUtil.get(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_ISSUE.getKey()).toString());
        String userId = redisUtil.get(RedisKeyManageEnum.MATERIAL_PURCHASE_REQUIREMENT_USER.getKey()).toString();
        List<MaterialPurchaseRequirementDetailVO> currentIssueDetatil = materialPurchaseRequirementDetailDao
                .selectVOByParams(MapUtil.of("ids", issuedIds));
        List<String> stockPointCodes = currentIssueDetatil.stream().map(MaterialPurchaseRequirementDetailVO::getStockPointCode)
                .distinct().collect(Collectors.toList());
        Map<String, String> stockPointMap = getStockPointMapByCodes(stockPointCodes);
        //维护下发记录
        MaterialPurchaseRequirementIssueDTO issueLogDto = initIssueLog(userId);
        List<MaterialPurchaseRequirementDetailPO> updateBatchList = new ArrayList<>();
        for (MaterialPurchaseRequirementDetailVO e : currentIssueDetatil) {
            String stockPointName = stockPointMap.get(e.getStockPointCode());
            ErpPrCreate erpPrCreate = erpPrCreateMap.get(String.join("&", stockPointName, e.getProductCode(),
                    DateUtils.dateToString(e.getRequireDate(), DateUtils.COMMON_DATE_STR3)));
            log.info("erpPrCreate当前值{}，e.getId当前值{}", erpPrCreate, e.getId());
            MaterialPurchaseRequirementDetailPO updateDetail = new MaterialPurchaseRequirementDetailPO();
            updateDetail.setId(e.getId());
            if (erpPrCreate != null) {
                if ("VALID".equals(erpPrCreate.getValidataFlag())) {
                    updateDetail.setPurchaseRequestCode(erpPrCreate.getRequisitionNumber());
                    updateDetail.setPurchaseRequestLineId(erpPrCreate.getRequisitionLineId());
                }
                updateDetail.setPurchaseRequestLineCode(null == erpPrCreate.getPrLineNo() ? null : erpPrCreate.getPrLineNo().toString());
                updateDetail.setValidataType(erpPrCreate.getValidataFlag());
                updateDetail.setValidataMsg(erpPrCreate.getValidataMsg());
                updateDetail.setMaterialPurchaseRequirementIssueId(issueLogDto.getId());
                updateBatchList.add(updateDetail);
                log.info("RequisitionNumber())) :{}", erpPrCreate.getRequisitionNumber());
                if (StringUtils.isEmpty(erpPrCreate.getRequisitionNumber())) {
                    issueLogDto.setIssueStatus(IssueStatusEnum.ISSUED_FAIL.getCode());
                }
            }
        }
//        log.info("updateBatchList:{}", JSON.toJSONString(updateBatchList));
//        log.info("issueLogDto:{}", JSON.toJSONString(issueLogDto));
        if (CollectionUtils.isNotEmpty(updateBatchList)) {
            //材料采购需求汇总数据绑定下发记录
            materialPurchaseRequirementDetailDao.updateBatchSelective(updateBatchList);
        }
        //新增下发记录数据
        materialPurchaseRequirementIssueService.doCreate(issueLogDto);
        if (IssueStatusEnum.ISSUED_SUCCESS.getCode().equals(issueLogDto.getIssueStatus())) {
            //下发成功，同步到货跟踪数据
            List<SyncMaterialArrivalTrackingDTO> syncArrivalList = new ArrayList<>();
            initArrivalTrackingList(currentIssueDetatil, syncArrivalList);
            materialArrivalTrackingService.doSyncMaterialArrivalTracking(syncArrivalList);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> splitTheOrder(List<MaterialPurchaseRequirementDetailDTO> materialPurchaseRequirementDetailDTOList) {
        if (CollectionUtils.isEmpty(materialPurchaseRequirementDetailDTOList)) {
            throw new BusinessException("拆单的采购需求不能为空");
        }

        String detailId = materialPurchaseRequirementDetailDTOList.stream().map(MaterialPurchaseRequirementDetailDTO::getId).findFirst().orElse(null);
        MaterialPurchaseRequirementDetailVO materialPurchaseRequirementDetailVO = this.selectByPrimaryKey(detailId);

        BigDecimal requireQuantity = materialPurchaseRequirementDetailVO.getRequireQuantity();

        BigDecimal requireQuantitySum = materialPurchaseRequirementDetailDTOList.stream()
                .map(MaterialPurchaseRequirementDetailDTO::getRequireQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (requireQuantitySum.compareTo(requireQuantity) != 0) {
            throw new BusinessException("拆单数量之和必须等于原要货数量");
        }

        List<MaterialPurchaseRequirementDetailDTO> addList = new ArrayList<>();
        // 处理拆批数据
        for (MaterialPurchaseRequirementDetailDTO detailDTO : materialPurchaseRequirementDetailDTOList) {
            MaterialPurchaseRequirementDetailDTO materialPurchaseRequirementDetailDTO = new MaterialPurchaseRequirementDetailDTO();
            BeanUtils.copyProperties(detailDTO, materialPurchaseRequirementDetailDTO);
            materialPurchaseRequirementDetailDTO.setId(UUID.randomUUID().toString());
            materialPurchaseRequirementDetailDTO.setRemark("拆单数据（原数量" + requireQuantity + "）");
            materialPurchaseRequirementDetailDTO.setParentId(detailId);

            // 添加拆批数据
            addList.add(materialPurchaseRequirementDetailDTO);
        }

        // 删除原本要货计划
        this.doDelete(Collections.singletonList(detailId));

        //批量新增
        if (CollectionUtils.isNotEmpty(addList)) {
            Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialPurchaseRequirementDetailDTO> list) {
        List<MaterialPurchaseRequirementDetailPO> newList = MaterialPurchaseRequirementDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseRequirementDetailDao.updateBatchSelective(newList);
    }
}
