package com.yhl.scp.mrp.material.plan.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanReplacePO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanReplaceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPlanReplaceDao</code>
 * <p>
 * 替代料计划DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:15:40
 */
public interface MaterialPlanReplaceDao extends BaseDao<MaterialPlanReplacePO, MaterialPlanReplaceVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MaterialPlanReplaceVO}
     */
    List<MaterialPlanReplaceVO> selectVOByParams(@Param("params") Map<String, Object> params);

    void deleteByVersionId(@Param("materialPlanVersionId")String materialPlanVersionId);

    void deleteByProductCodes(@Param("productCodeList")List<String> productCodeList);

    List<MaterialPlanReplacePO> selectByProductCodes(@Param("productCodeList")List<String> productCodeList);

    void deleteAll();
}
