package com.yhl.scp.mrp.material.arrival.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.material.arrival.convertor.MaterialArrivalTrackingIssueDetailConvertor;
import com.yhl.scp.mrp.material.arrival.domain.entity.MaterialArrivalTrackingIssueDetailDO;
import com.yhl.scp.mrp.material.arrival.domain.service.MaterialArrivalTrackingIssueDetailDomainService;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingIssueDetailDTO;
import com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialArrivalTrackingIssueDetailDao;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingIssueDetailPO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingIssueDetailService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalTrackingIssueDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>MaterialArrivalTrackingIssueDetailServiceImpl</code>
 * <p>
 * 材料到货跟踪下发明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 10:26:58
 */
@Slf4j
@Service
public class MaterialArrivalTrackingIssueDetailServiceImpl extends AbstractService implements MaterialArrivalTrackingIssueDetailService {

    @Resource
    private MaterialArrivalTrackingIssueDetailDao materialArrivalTrackingIssueDetailDao;

    @Resource
    private MaterialArrivalTrackingIssueDetailDomainService materialArrivalTrackingIssueDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialArrivalTrackingIssueDetailDTO materialArrivalTrackingIssueDetailDTO) {
        // 0.数据转换
        MaterialArrivalTrackingIssueDetailDO materialArrivalTrackingIssueDetailDO = MaterialArrivalTrackingIssueDetailConvertor.INSTANCE.dto2Do(materialArrivalTrackingIssueDetailDTO);
        MaterialArrivalTrackingIssueDetailPO materialArrivalTrackingIssueDetailPO = MaterialArrivalTrackingIssueDetailConvertor.INSTANCE.dto2Po(materialArrivalTrackingIssueDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialArrivalTrackingIssueDetailDomainService.validation(materialArrivalTrackingIssueDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialArrivalTrackingIssueDetailPO);
        materialArrivalTrackingIssueDetailDao.insert(materialArrivalTrackingIssueDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialArrivalTrackingIssueDetailDTO materialArrivalTrackingIssueDetailDTO) {
        // 0.数据转换
        MaterialArrivalTrackingIssueDetailDO materialArrivalTrackingIssueDetailDO = MaterialArrivalTrackingIssueDetailConvertor.INSTANCE.dto2Do(materialArrivalTrackingIssueDetailDTO);
        MaterialArrivalTrackingIssueDetailPO materialArrivalTrackingIssueDetailPO = MaterialArrivalTrackingIssueDetailConvertor.INSTANCE.dto2Po(materialArrivalTrackingIssueDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialArrivalTrackingIssueDetailDomainService.validation(materialArrivalTrackingIssueDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialArrivalTrackingIssueDetailPO);
        materialArrivalTrackingIssueDetailDao.update(materialArrivalTrackingIssueDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialArrivalTrackingIssueDetailDTO> list) {
        List<MaterialArrivalTrackingIssueDetailPO> newList = MaterialArrivalTrackingIssueDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialArrivalTrackingIssueDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialArrivalTrackingIssueDetailDTO> list) {
        List<MaterialArrivalTrackingIssueDetailPO> newList = MaterialArrivalTrackingIssueDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialArrivalTrackingIssueDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialArrivalTrackingIssueDetailDao.deleteBatch(idList);
        }
        return materialArrivalTrackingIssueDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialArrivalTrackingIssueDetailVO selectByPrimaryKey(String id) {
        MaterialArrivalTrackingIssueDetailPO po = materialArrivalTrackingIssueDetailDao.selectByPrimaryKey(id);
        return MaterialArrivalTrackingIssueDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_ARRIVAL_TRACKING_ISSUE_DETAIL")
    public List<MaterialArrivalTrackingIssueDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_ARRIVAL_TRACKING_ISSUE_DETAIL")
    public List<MaterialArrivalTrackingIssueDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialArrivalTrackingIssueDetailVO> dataList = materialArrivalTrackingIssueDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialArrivalTrackingIssueDetailServiceImpl target = SpringBeanUtils.getBean(MaterialArrivalTrackingIssueDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialArrivalTrackingIssueDetailVO> selectByParams(Map<String, Object> params) {
        List<MaterialArrivalTrackingIssueDetailPO> list = materialArrivalTrackingIssueDetailDao.selectByParams(params);
        return MaterialArrivalTrackingIssueDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialArrivalTrackingIssueDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<LabelValue<String>> noDropDown() {
        return this.selectAll().stream()
                .filter(data -> StringUtils.isNotEmpty(data.getMaterialArrivalTrackingNo()))
                .map(item -> new LabelValue<>(item.getMaterialArrivalTrackingNo(), item.getMaterialArrivalTrackingNo()))
                .distinct()
                .sorted(Comparator.comparing(LabelValue::getLabel))
                .collect(Collectors.toList());
    }

    @Override
    public List<MaterialArrivalTrackingIssueDetailVO> selectByPageNoVersion(Pagination pagination, String materialArrivalTrackingNo) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return materialArrivalTrackingIssueDetailDao.selectByPageNoVersion(materialArrivalTrackingNo);
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialArrivalTrackingIssueDetailVO> invocation(List<MaterialArrivalTrackingIssueDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
