<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialArrivalTrackingIssueDetailDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingIssueDetailPO">
        <!--@Table mrp_material_arrival_tracking_issue_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="material_arrival_tracking_id" jdbcType="VARCHAR" property="materialArrivalTrackingId"/>
        <result column="material_arrival_tracking_no" jdbcType="VARCHAR" property="materialArrivalTrackingNo"/>
        <result column="issue_version" jdbcType="INTEGER" property="issueVersion"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="purchase_order_code" jdbcType="VARCHAR" property="purchaseOrderCode"/>
        <result column="purchase_order_line_code" jdbcType="VARCHAR" property="purchaseOrderLineCode"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="require_date" jdbcType="TIMESTAMP" property="requireDate"/>
        <result column="require_quantity" jdbcType="VARCHAR" property="requireQuantity"/>
        <result column="wait_delivery_quantity" jdbcType="VARCHAR" property="waitDeliveryQuantity"/>
        <result column="product_unit" jdbcType="VARCHAR" property="productUnit"/>
        <result column="predict_arrival_date" jdbcType="TIMESTAMP" property="predictArrivalDate"/>
        <result column="predict_arrival_quantity" jdbcType="VARCHAR" property="predictArrivalQuantity"/>
        <result column="arrival_status" jdbcType="VARCHAR" property="arrivalStatus"/>
        <result column="inventory_quantity" jdbcType="VARCHAR" property="inventoryQuantity"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="qty_billed" jdbcType="VARCHAR" property="qtyBilled"/>
        <result column="delivery_note_code" jdbcType="VARCHAR" property="deliveryNoteCode"/>
        <result column="shipping_date" jdbcType="TIMESTAMP" property="shippingDate"/>
        <result column="publish_status" jdbcType="VARCHAR" property="publishStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalTrackingIssueDetailVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,material_arrival_tracking_id,material_arrival_tracking_no,issue_version,status,purchase_order_code,purchase_order_line_code,stock_point_code,material_code,require_date,require_quantity,wait_delivery_quantity,product_unit,predict_arrival_date,predict_arrival_quantity,arrival_status,inventory_quantity,supplier_code,supplier_name,qty_billed,delivery_note_code,shipping_date,publish_status,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.materialArrivalTrackingId != null and params.materialArrivalTrackingId != ''">
                and material_arrival_tracking_id = #{params.materialArrivalTrackingId,jdbcType=VARCHAR}
            </if>
            <if test="params.materialArrivalTrackingNo != null and params.materialArrivalTrackingNo != ''">
                and material_arrival_tracking_no = #{params.materialArrivalTrackingNo,jdbcType=VARCHAR}
            </if>
            <if test="params.issueVersion != null">
                and issue_version = #{params.issueVersion,jdbcType=INTEGER}
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderCode != null and params.purchaseOrderCode != ''">
                and purchase_order_code = #{params.purchaseOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderLineCode != null and params.purchaseOrderLineCode != ''">
                and purchase_order_line_code = #{params.purchaseOrderLineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCode != null and params.materialCode != ''">
                and material_code = #{params.materialCode,jdbcType=VARCHAR}
            </if>
            <if test="params.requireDate != null">
                and require_date = #{params.requireDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.requireQuantity != null">
                and require_quantity = #{params.requireQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.waitDeliveryQuantity != null">
                and wait_delivery_quantity = #{params.waitDeliveryQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.productUnit != null and params.productUnit != ''">
                and product_unit = #{params.productUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.predictArrivalDate != null">
                and predict_arrival_date = #{params.predictArrivalDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.predictArrivalQuantity != null">
                and predict_arrival_quantity = #{params.predictArrivalQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.arrivalStatus != null and params.arrivalStatus != ''">
                and arrival_status = #{params.arrivalStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryQuantity != null">
                and inventory_quantity = #{params.inventoryQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierName != null and params.supplierName != ''">
                and supplier_name = #{params.supplierName,jdbcType=VARCHAR}
            </if>
            <if test="params.qtyBilled != null">
                and qty_billed = #{params.qtyBilled,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryNoteCode != null and params.deliveryNoteCode != ''">
                and delivery_note_code = #{params.deliveryNoteCode,jdbcType=VARCHAR}
            </if>
            <if test="params.shippingDate != null">
                and shipping_date = #{params.shippingDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.publishStatus != null and params.publishStatus != ''">
                and publish_status = #{params.publishStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_tracking_issue_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_tracking_issue_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_material_arrival_tracking_issue_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_arrival_tracking_issue_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByPageNoVersion" resultMap="VOResultMap">
        SELECT
        *
        FROM (
        SELECT
        *,
        ROW_NUMBER() OVER (
        PARTITION BY material_arrival_tracking_no,issue_version
        ORDER BY id
        ) AS rn
        FROM
        mrp_material_arrival_tracking_issue_detail
        ) t
        WHERE
        rn = 1
        <if test="materialArrivalTrackingNo != null and materialArrivalTrackingNo != ''">
            and material_arrival_tracking_no like concat('%', #{materialArrivalTrackingNo,jdbcType=VARCHAR}, '%')
        </if>
        order by material_arrival_tracking_no,issue_version
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingIssueDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_arrival_tracking_issue_detail(
        id,
        material_arrival_tracking_id,
        material_arrival_tracking_no,
        issue_version,
        status,
        purchase_order_code,
        purchase_order_line_code,
        stock_point_code,
        material_code,
        require_date,
        require_quantity,
        wait_delivery_quantity,
        product_unit,
        predict_arrival_date,
        predict_arrival_quantity,
        arrival_status,
        inventory_quantity,
        supplier_code,
        supplier_name,
        qty_billed,
        delivery_note_code,
        shipping_date,
        publish_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{materialArrivalTrackingId,jdbcType=VARCHAR},
        #{materialArrivalTrackingNo,jdbcType=VARCHAR},
        #{issueVersion,jdbcType=INTEGER},
        #{status,jdbcType=VARCHAR},
        #{purchaseOrderCode,jdbcType=VARCHAR},
        #{purchaseOrderLineCode,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{materialCode,jdbcType=VARCHAR},
        #{requireDate,jdbcType=TIMESTAMP},
        #{requireQuantity,jdbcType=VARCHAR},
        #{waitDeliveryQuantity,jdbcType=VARCHAR},
        #{productUnit,jdbcType=VARCHAR},
        #{predictArrivalDate,jdbcType=TIMESTAMP},
        #{predictArrivalQuantity,jdbcType=VARCHAR},
        #{arrivalStatus,jdbcType=VARCHAR},
        #{inventoryQuantity,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{qtyBilled,jdbcType=VARCHAR},
        #{deliveryNoteCode,jdbcType=VARCHAR},
        #{shippingDate,jdbcType=TIMESTAMP},
        #{publishStatus,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingIssueDetailPO">
        insert into mrp_material_arrival_tracking_issue_detail(id,
                                                               material_arrival_tracking_id,
                                                               material_arrival_tracking_no,
                                                               issue_version,
                                                               status,
                                                               purchase_order_code,
                                                               purchase_order_line_code,
                                                               stock_point_code,
                                                               material_code,
                                                               require_date,
                                                               require_quantity,
                                                               wait_delivery_quantity,
                                                               product_unit,
                                                               predict_arrival_date,
                                                               predict_arrival_quantity,
                                                               arrival_status,
                                                               inventory_quantity,
                                                               supplier_code,
                                                               supplier_name,
                                                               qty_billed,
                                                               delivery_note_code,
                                                               shipping_date,
                                                               publish_status,
                                                               remark,
                                                               enabled,
                                                               creator,
                                                               create_time,
                                                               modifier,
                                                               modify_time,
                                                               version_value)
        values (#{id,jdbcType=VARCHAR},
                #{materialArrivalTrackingId,jdbcType=VARCHAR},
                #{materialArrivalTrackingNo,jdbcType=VARCHAR},
                #{issueVersion,jdbcType=INTEGER},
                #{status,jdbcType=VARCHAR},
                #{purchaseOrderCode,jdbcType=VARCHAR},
                #{purchaseOrderLineCode,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{materialCode,jdbcType=VARCHAR},
                #{requireDate,jdbcType=TIMESTAMP},
                #{requireQuantity,jdbcType=VARCHAR},
                #{waitDeliveryQuantity,jdbcType=VARCHAR},
                #{productUnit,jdbcType=VARCHAR},
                #{predictArrivalDate,jdbcType=TIMESTAMP},
                #{predictArrivalQuantity,jdbcType=VARCHAR},
                #{arrivalStatus,jdbcType=VARCHAR},
                #{inventoryQuantity,jdbcType=VARCHAR},
                #{supplierCode,jdbcType=VARCHAR},
                #{supplierName,jdbcType=VARCHAR},
                #{qtyBilled,jdbcType=VARCHAR},
                #{deliveryNoteCode,jdbcType=VARCHAR},
                #{shippingDate,jdbcType=TIMESTAMP},
                #{publishStatus,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_arrival_tracking_issue_detail(
        id,
        material_arrival_tracking_id,
        material_arrival_tracking_no,
        issue_version,
        status,
        purchase_order_code,
        purchase_order_line_code,
        stock_point_code,
        material_code,
        require_date,
        require_quantity,
        wait_delivery_quantity,
        product_unit,
        predict_arrival_date,
        predict_arrival_quantity,
        arrival_status,
        inventory_quantity,
        supplier_code,
        supplier_name,
        qty_billed,
        delivery_note_code,
        shipping_date,
        publish_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.materialArrivalTrackingId,jdbcType=VARCHAR},
            #{entity.materialArrivalTrackingNo,jdbcType=VARCHAR},
            #{entity.issueVersion,jdbcType=INTEGER},
            #{entity.status,jdbcType=VARCHAR},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.materialCode,jdbcType=VARCHAR},
            #{entity.requireDate,jdbcType=TIMESTAMP},
            #{entity.requireQuantity,jdbcType=VARCHAR},
            #{entity.waitDeliveryQuantity,jdbcType=VARCHAR},
            #{entity.productUnit,jdbcType=VARCHAR},
            #{entity.predictArrivalDate,jdbcType=TIMESTAMP},
            #{entity.predictArrivalQuantity,jdbcType=VARCHAR},
            #{entity.arrivalStatus,jdbcType=VARCHAR},
            #{entity.inventoryQuantity,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.qtyBilled,jdbcType=VARCHAR},
            #{entity.deliveryNoteCode,jdbcType=VARCHAR},
            #{entity.shippingDate,jdbcType=TIMESTAMP},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_arrival_tracking_issue_detail(
        id,
        material_arrival_tracking_id,
        material_arrival_tracking_no,
        issue_version,
        status,
        purchase_order_code,
        purchase_order_line_code,
        stock_point_code,
        material_code,
        require_date,
        require_quantity,
        wait_delivery_quantity,
        product_unit,
        predict_arrival_date,
        predict_arrival_quantity,
        arrival_status,
        inventory_quantity,
        supplier_code,
        supplier_name,
        qty_billed,
        delivery_note_code,
        shipping_date,
        publish_status,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.materialArrivalTrackingId,jdbcType=VARCHAR},
            #{entity.materialArrivalTrackingNo,jdbcType=VARCHAR},
            #{entity.issueVersion,jdbcType=INTEGER},
            #{entity.status,jdbcType=VARCHAR},
            #{entity.purchaseOrderCode,jdbcType=VARCHAR},
            #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.materialCode,jdbcType=VARCHAR},
            #{entity.requireDate,jdbcType=TIMESTAMP},
            #{entity.requireQuantity,jdbcType=VARCHAR},
            #{entity.waitDeliveryQuantity,jdbcType=VARCHAR},
            #{entity.productUnit,jdbcType=VARCHAR},
            #{entity.predictArrivalDate,jdbcType=TIMESTAMP},
            #{entity.predictArrivalQuantity,jdbcType=VARCHAR},
            #{entity.arrivalStatus,jdbcType=VARCHAR},
            #{entity.inventoryQuantity,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.qtyBilled,jdbcType=VARCHAR},
            #{entity.deliveryNoteCode,jdbcType=VARCHAR},
            #{entity.shippingDate,jdbcType=TIMESTAMP},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingIssueDetailPO">
        update mrp_material_arrival_tracking_issue_detail
        set material_arrival_tracking_id = #{materialArrivalTrackingId,jdbcType=VARCHAR},
            material_arrival_tracking_no = #{materialArrivalTrackingNo,jdbcType=VARCHAR},
            issue_version                = #{issueVersion,jdbcType=INTEGER},
            status                       = #{status,jdbcType=VARCHAR},
            purchase_order_code          = #{purchaseOrderCode,jdbcType=VARCHAR},
            purchase_order_line_code     = #{purchaseOrderLineCode,jdbcType=VARCHAR},
            stock_point_code             = #{stockPointCode,jdbcType=VARCHAR},
            material_code                = #{materialCode,jdbcType=VARCHAR},
            require_date                 = #{requireDate,jdbcType=TIMESTAMP},
            require_quantity             = #{requireQuantity,jdbcType=VARCHAR},
            wait_delivery_quantity       = #{waitDeliveryQuantity,jdbcType=VARCHAR},
            product_unit                 = #{productUnit,jdbcType=VARCHAR},
            predict_arrival_date         = #{predictArrivalDate,jdbcType=TIMESTAMP},
            predict_arrival_quantity     = #{predictArrivalQuantity,jdbcType=VARCHAR},
            arrival_status               = #{arrivalStatus,jdbcType=VARCHAR},
            inventory_quantity           = #{inventoryQuantity,jdbcType=VARCHAR},
            supplier_code                = #{supplierCode,jdbcType=VARCHAR},
            supplier_name                = #{supplierName,jdbcType=VARCHAR},
            qty_billed                   = #{qtyBilled,jdbcType=VARCHAR},
            delivery_note_code           = #{deliveryNoteCode,jdbcType=VARCHAR},
            shipping_date                = #{shippingDate,jdbcType=TIMESTAMP},
            publish_status               = #{publishStatus,jdbcType=VARCHAR},
            remark                       = #{remark,jdbcType=VARCHAR},
            enabled                      = #{enabled,jdbcType=VARCHAR},
            modifier                     = #{modifier,jdbcType=VARCHAR},
            modify_time                  = #{modifyTime,jdbcType=TIMESTAMP},
            version_value                = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingIssueDetailPO">
        update mrp_material_arrival_tracking_issue_detail
        <set>
            <if test="item.materialArrivalTrackingId != null and item.materialArrivalTrackingId != ''">
                material_arrival_tracking_id = #{item.materialArrivalTrackingId,jdbcType=VARCHAR},
            </if>
            <if test="item.materialArrivalTrackingNo != null and item.materialArrivalTrackingNo != ''">
                material_arrival_tracking_no = #{item.materialArrivalTrackingNo,jdbcType=VARCHAR},
            </if>
            <if test="item.issueVersion != null">
                issue_version = #{item.issueVersion,jdbcType=INTEGER},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialCode != null and item.materialCode != ''">
                material_code = #{item.materialCode,jdbcType=VARCHAR},
            </if>
            <if test="item.requireDate != null">
                require_date = #{item.requireDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.requireQuantity != null">
                require_quantity = #{item.requireQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.waitDeliveryQuantity != null">
                wait_delivery_quantity = #{item.waitDeliveryQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.productUnit != null and item.productUnit != ''">
                product_unit = #{item.productUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.predictArrivalDate != null">
                predict_arrival_date = #{item.predictArrivalDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.predictArrivalQuantity != null">
                predict_arrival_quantity = #{item.predictArrivalQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.arrivalStatus != null and item.arrivalStatus != ''">
                arrival_status = #{item.arrivalStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryQuantity != null">
                inventory_quantity = #{item.inventoryQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null and item.supplierName != ''">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.qtyBilled != null">
                qty_billed = #{item.qtyBilled,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryNoteCode != null and item.deliveryNoteCode != ''">
                delivery_note_code = #{item.deliveryNoteCode,jdbcType=VARCHAR},
            </if>
            <if test="item.shippingDate != null">
                shipping_date = #{item.shippingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.publishStatus != null and item.publishStatus != ''">
                publish_status = #{item.publishStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_arrival_tracking_issue_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="material_arrival_tracking_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialArrivalTrackingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_arrival_tracking_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialArrivalTrackingNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="issue_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.issueVersion,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderLineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="require_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requireDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="require_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requireQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="wait_delivery_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.waitDeliveryQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="predict_arrival_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.predictArrivalDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="predict_arrival_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.predictArrivalQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="arrival_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.arrivalStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="qty_billed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.qtyBilled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_note_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryNoteCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shipping_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shippingDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_material_arrival_tracking_issue_detail
            <set>
                <if test="item.materialArrivalTrackingId != null and item.materialArrivalTrackingId != ''">
                    material_arrival_tracking_id = #{item.materialArrivalTrackingId,jdbcType=VARCHAR},
                </if>
                <if test="item.materialArrivalTrackingNo != null and item.materialArrivalTrackingNo != ''">
                    material_arrival_tracking_no = #{item.materialArrivalTrackingNo,jdbcType=VARCHAR},
                </if>
                <if test="item.issueVersion != null">
                    issue_version = #{item.issueVersion,jdbcType=INTEGER},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                    purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                    purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.materialCode != null and item.materialCode != ''">
                    material_code = #{item.materialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.requireDate != null">
                    require_date = #{item.requireDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.requireQuantity != null">
                    require_quantity = #{item.requireQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.waitDeliveryQuantity != null">
                    wait_delivery_quantity = #{item.waitDeliveryQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.productUnit != null and item.productUnit != ''">
                    product_unit = #{item.productUnit,jdbcType=VARCHAR},
                </if>
                <if test="item.predictArrivalDate != null">
                    predict_arrival_date = #{item.predictArrivalDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.predictArrivalQuantity != null">
                    predict_arrival_quantity = #{item.predictArrivalQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.arrivalStatus != null and item.arrivalStatus != ''">
                    arrival_status = #{item.arrivalStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryQuantity != null">
                    inventory_quantity = #{item.inventoryQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierCode != null and item.supplierCode != ''">
                    supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplierName != null and item.supplierName != ''">
                    supplier_name = #{item.supplierName,jdbcType=VARCHAR},
                </if>
                <if test="item.qtyBilled != null">
                    qty_billed = #{item.qtyBilled,jdbcType=VARCHAR},
                </if>
                <if test="item.deliveryNoteCode != null and item.deliveryNoteCode != ''">
                    delivery_note_code = #{item.deliveryNoteCode,jdbcType=VARCHAR},
                </if>
                <if test="item.shippingDate != null">
                    shipping_date = #{item.shippingDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.publishStatus != null and item.publishStatus != ''">
                    publish_status = #{item.publishStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_material_arrival_tracking_issue_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_arrival_tracking_issue_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
