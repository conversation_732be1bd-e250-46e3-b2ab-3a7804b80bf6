package com.yhl.scp.mrp.inventory.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpOriginalFilmFFOnway;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPoCreate;
import com.yhl.scp.mds.routing.dto.ProductRoutingStepDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryFloatGlassShippedDetailDTO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <code>InventoryFloatGlassShippedDetailService</code>
 * <p>
 * 原片浮法已发运库存批次明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:31:57
 */
public interface InventoryFloatGlassShippedDetailService extends BaseService<InventoryFloatGlassShippedDetailDTO, InventoryFloatGlassShippedDetailVO> {

    void doUpdateBatchSelective(List<InventoryFloatGlassShippedDetailDTO> list);

    /**
     * 查询所有
     *
     * @return list {@link InventoryFloatGlassShippedDetailVO}
     */
    List<InventoryFloatGlassShippedDetailVO> selectAll();

    List<InventoryFloatGlassShippedDetailVO> selectByPlanNumbersAndlineIds(Map<String, Object> params);
    void doCreateBatchWithPartition(List<InventoryFloatGlassShippedDetailDTO> list,Integer pageSize);
    void doUpdateBatchWithPartition(List<InventoryFloatGlassShippedDetailDTO> list,Integer pageSize);

    BaseResponse<Void> syncOriginalFilmFFOnway(String tenantId);

    BaseResponse<Void> sync(String scenario, List<ErpOriginalFilmFFOnway> o);

    void doUpload(MultipartFile file);

    void downloadErrorData(HttpServletResponse response);

    BaseResponse<Void> handlePoCreate(String scenario, List<ErpPoCreate> o);

    BaseResponse<Void> syncPoCreate(String tenantId);

    String doBatchCreatPo(List<String> ids);

    String syncAutoCreatPo(String tenantId);

    BaseResponse<String> doUploadInternal(MultipartFile file);

    void exportTemplateInternal(HttpServletResponse response);

    void exportTemplateOutsourcing(HttpServletResponse response);

    void uploadOutsourcing(MultipartFile file);

}
