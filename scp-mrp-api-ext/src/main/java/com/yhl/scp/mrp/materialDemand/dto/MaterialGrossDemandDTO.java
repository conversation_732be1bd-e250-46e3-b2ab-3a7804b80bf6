package com.yhl.scp.mrp.materialDemand.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <code>MaterialGrossDemandDTO</code>
 * <p>
 * 材料计划毛需求DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 09:52:29
 */
@ApiModel(value = "材料计划毛需求DTO")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialGrossDemandDTO extends BaseDTO implements Serializable  {

    private static final long serialVersionUID = -51687665920785856L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 毛需求计算版本ID
     */
    @ApiModelProperty(value = "毛需求计算版本ID")
    private String materialGrossDemandVersionId;

    /**
     * 主料编码
     */
    @ApiModelProperty(value = "主料编码")
    private String mainProductCode;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @ExcelProperty(value = "材料编码*", index = 0)
    @ExcelPropertyCheck(required = true)
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @ExcelProperty(value = "材料编码*", index = 1)
    @ExcelPropertyCheck(required = true)
    private String productName;
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    private String productId;
    /**
     * 物料类别(PVB、B、RA.A)
     */
    @ApiModelProperty(value = "物料类别(PVB、B、RA.A)")
    @ExcelProperty(value = "物料类别(PVB、B、RA.A)*", index = 2)
    @ExcelPropertyCheck(required = true)
    private String productCategory;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    @ExcelProperty(value = "通用类别*", index = 3)
    @ExcelPropertyCheck(required = true)
    private String productClassify;

    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @ExcelProperty(value = "本厂编码", index = 4)
    private String productFactoryCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @ExcelProperty(value = "车型编码", index = 5)
    private String vehicleModeCode;
    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    private BigDecimal inputFactor;
    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
//    @ExcelProperty(value = "需求时间*")
//    @ExcelPropertyCheck(required = true)
    private Date demandTime;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
//    @ExcelProperty(value = "需求数量*")
//    @ExcelPropertyCheck(required = true)
    private BigDecimal demandQuantity;
    /**
     * 未分配数量
     */
    @ApiModelProperty(value = "未分配数量")
    private BigDecimal unFulfillmentQuantity;
    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    private String operationCode;
    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    private String supplyModel;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String productColor;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private String demandSource;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

    private Map<String, String> dynamicColumns = new HashMap<>();


}
