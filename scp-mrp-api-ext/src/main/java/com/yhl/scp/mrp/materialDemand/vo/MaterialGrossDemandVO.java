package com.yhl.scp.mrp.materialDemand.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <code>MaterialGrossDemandVO</code>
 * <p>
 * 材料计划毛需求VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 09:52:30
 */
@ApiModel(value = "材料计划毛需求VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialGrossDemandVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -39854972984633794L;

    /**
     * 毛需求计算版本ID
     */
    @ApiModelProperty(value = "毛需求计算版本ID")
    @FieldInterpretation(value = "毛需求计算版本ID")
    private String materialGrossDemandVersionId;
    /**
     * 主料
     */
    @ApiModelProperty(value = "主料编码")
    @FieldInterpretation(value = "主料编码")
    private String mainProductCode;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @FieldInterpretation(value = "物品编码")
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @FieldInterpretation(value = "物品名称")
    private String productName;
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    @FieldInterpretation(value = "物品ID")
    private String productId;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    @FieldInterpretation(value = "物料分类")
    private String productClassify;
    /**
     * 物料类别(PVB、B、RA.A)
     */
    @ApiModelProperty(value = "物料类别(PVB、B、RA.A)")
    @FieldInterpretation(value = "物料类别(PVB、B、RA.A)")
    private String productCategory;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productFactoryCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModeCode;
    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    @FieldInterpretation(value = "单耗")
    private BigDecimal inputFactor;
    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    @FieldInterpretation(value = "需求时间")
    private Date demandTime;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    private BigDecimal demandQuantity;
    /**
     * 未分配数量
     */
    @ApiModelProperty(value = "未分配数量")
    @FieldInterpretation(value = "未分配数量")
    private BigDecimal unFulfillmentQuantity;
    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    @FieldInterpretation(value = "工序代码")
    private String operationCode;
    /**
     * 供应方式
     */
    @ApiModelProperty(value = "供应方式")
    @FieldInterpretation(value = "供应方式")
    private String supplyModel;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String productColor;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @FieldInterpretation(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    @FieldInterpretation(value = "需求来源")
    private String demandSource;

    @ApiModelProperty(value = "时间")
    private List<String> dataList;

    @ApiModelProperty(value = "每天的汇总明细")
    private List<MaterialGrossDemandSummaryVO> detailList;

    /**
     * 物料长度
     */
    @ApiModelProperty(value = "物料长度")
    @FieldInterpretation(value = "物料长度")
    private BigDecimal productLength;
    /**
     * 物料宽度
     */
    @ApiModelProperty(value = "物料宽度")
    @FieldInterpretation(value = "物料宽度")
    private BigDecimal productWidth;

    /**
     * 本厂名称
     */
    @ApiModelProperty(value = "本厂名称")
    @FieldInterpretation(value = "本厂名称")
    private String productFactoryName;

    @Override
    public void clean() {

    }

}
