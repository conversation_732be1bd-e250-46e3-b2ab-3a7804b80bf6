package com.yhl.scp.dfp.demand.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.clean.service.CleanAlgorithmDataService;
import com.yhl.scp.dfp.clean.service.CleanForecastDataService;
import com.yhl.scp.dfp.common.enums.ShowLevelEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.demand.dto.DemandForecastReplayDTO;
import com.yhl.scp.dfp.demand.enums.RowTypeEnum;
import com.yhl.scp.dfp.demand.service.DemandForecastReplayService;
import com.yhl.scp.dfp.demand.vo.DemandForecastReplayVO;
import com.yhl.scp.dfp.demand.vo.DynamicCellVO;
import com.yhl.scp.dfp.demand.vo.ForecastReplayDetailVO;
import com.yhl.scp.dfp.switchrelation.service.SwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationVO;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseRecordDao;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>DemandForecastReplayServiceImpl</code>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 16:29:37
 */
@Slf4j
@Service
public class DemandForecastReplayServiceImpl implements DemandForecastReplayService {

    public static final String YYYY_MM = "yyyyMM";

    @Resource
    private CleanForecastDataService forecastDataService;

    @Resource
    private ConsistenceDemandForecastDataService consistenceService;

    @Resource
    private CleanAlgorithmDataService algorithmDataService;

    @Resource
    private WarehouseReleaseRecordDao warehouseReleaseRecordDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private SwitchRelationBetweenProductService switchRelationBetweenProductService;

    @Override
    public List<DemandForecastReplayVO> selectForecastReplay(DemandForecastReplayDTO replayDTO) {
        log.info("开始查询需求预测复盘");
        String replayType = replayDTO.getReplayType();
        String planPeriod = replayDTO.getPlanPeriod();
        String showLevel = replayDTO.getShowLevel();
        List<String> productCodeList = replayDTO.getProductCodeList();
        List<String> oemCodeList = replayDTO.getOemCodeList();
        List<String> planPeriods = getSortedDateList(replayDTO.getShowBeginTime(), replayDTO.getShowEndTime());
        Map<String, Object> basicParams = new HashMap<>(6);
        basicParams.put("oemCodeList", oemCodeList);
        basicParams.put("vehicleCodeList", replayDTO.getVehicleCodeList());
        basicParams.put("productCodeList", productCodeList);
        List<DemandForecastReplayVO> dataList;
        if (VersionTypeEnum.CLEAN_ALGORITHM.getCode().equals(replayType)) {
            dataList = algorithmDataService.selectForecastReplay(basicParams, showLevel, planPeriod, planPeriods);
            log.info("算法预测查询完成");
        } else if (VersionTypeEnum.CLEAN_FORECAST.getCode().equals(replayType)) {
            dataList = forecastDataService.selectForecastReplay(basicParams, showLevel, planPeriod, planPeriods);
            log.info("滚动预测查询完成");
        } else {
            dataList = consistenceService.selectForecastReplay(basicParams, showLevel, planPeriod, planPeriods);
            log.info("复盘数据查询完成");
        }

        // 转换根据版本查询出的结果
        Map<String, BigDecimal> valueMap = convertReplay2ValueMap(dataList);
        // 计算仓库发货
        log.info("开始查询仓库收发货数据");
        String startMonth = planPeriods.get(0);
        String endMonth = planPeriods.get(planPeriods.size() - 1);

        SwitchRelationVO switchRelation = switchRelationBetweenProductService.getSwitchRelation(oemCodeList,
                productCodeList);
        Map<String, String> newOldMap = switchRelation.getNewOldMap();
        Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        List<WarehouseReleaseRecordMonthVO> releaseList = warehouseReleaseRecordDao
                .selectMonthVOByItemCodes(oemCodeList, allProductCodes, startMonth, endMonth);

        Map<String, String> productCodeVehicleCodeMap = new HashMap<>();
        Map<String, Map<String, BigDecimal>> releaseRecordMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(releaseList)) {
            List<String> productCodes = releaseList.stream().map(WarehouseReleaseRecordMonthVO::getItemCode)
                    .filter(StringUtils::isEmpty).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productCodes)) {
                FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                        .dynamicColumnParam(Lists.newArrayList("product_code", "vehicle_model_code"))
                        .queryParam(ImmutableMap.of("productCodeList", productCodes))
                        .build();
                List<NewProductStockPointVO> newProductStockPointVOS =
                        newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
                productCodeVehicleCodeMap = newProductStockPointVOS
                        .stream().filter(item -> StringUtils.isNotBlank(item.getProductCode())
                                && StringUtils.isNotBlank(item.getVehicleModelCode())).collect(Collectors
                                .toMap(NewProductStockPointVO::getProductCode,
                                        NewProductStockPointVO::getVehicleModelCode,
                                        (t1, t2) -> t2));
            }
            for (WarehouseReleaseRecordMonthVO record : releaseList) {
                String code = productCodeVehicleCodeMap.get(record.getItemCode());
                record.setVehicleModelCode(code);
            }
            if (showLevel.equals(ShowLevelEnum.PART.getCode())) {
                Map<String, BigDecimal> qtyMap = releaseList.stream().collect(Collectors
                        .groupingBy(x -> String.join(Constants.DELIMITER, x.getOemCode(),
                                        x.getVehicleModelCode(), x.getItemCode(), x.getYearMonth()),
                                Collectors.reducing(BigDecimal.ZERO,
                                        WarehouseReleaseRecordMonthVO::getSumQty, BigDecimal::add)));
                for (WarehouseReleaseRecordMonthVO item : releaseList) {
                    String oemCode = item.getOemCode();
                    String vehicleModelCode = item.getVehicleModelCode();
                    String itemCode = item.getItemCode();
                    String yearMonth = item.getYearMonth();
                    BigDecimal sumQty = item.getSumQty();
                    if (newOldMap.containsKey(itemCode)) {
                        String xItemCode = newOldMap.get(itemCode);
                        String xKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode, xItemCode, yearMonth);
                        sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
                    }
                    if (oldNewMap.containsKey(itemCode)) {
                        String yItemCode = oldNewMap.get(itemCode);
                        String yKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode, yItemCode, yearMonth);
                        sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
                    }
                    item.setSumQty(sumQty);
                }
            }
            releaseRecordMap = getWarehouseReleaseRecordByMonth(releaseList, showLevel);
        }

        for (DemandForecastReplayVO demandForecastReplayVO : dataList) {
            String join = String.join(Constants.DELIMITER,
                    demandForecastReplayVO.getOemCode(),
                    demandForecastReplayVO.getVehicleModelCode(),
                    demandForecastReplayVO.getProductCode());

            Map<String, BigDecimal> keyQuantityMap = releaseRecordMap.entrySet()
                    .stream()
                    .filter(entry -> join.startsWith(entry.getKey()))
                    .findFirst()
                    .map(Map.Entry::getValue)
                    .orElse(new HashMap<>());
            if (MapUtils.isNotEmpty(keyQuantityMap)) {
                List<DynamicCellVO> currentQtyList = new ArrayList<>();
                List<DynamicCellVO> currentMonth = new ArrayList<>();
                List<DynamicCellVO> previousMonth = new ArrayList<>();
                List<DynamicCellVO> monthBeforeLast = new ArrayList<>();

                planPeriods.sort(Comparator.comparing(Function.identity()));
                List<ForecastReplayDetailVO> detailList = demandForecastReplayVO.getDetailList();
                for (ForecastReplayDetailVO forecastReplayDetailVO : detailList) {
                    List<DynamicCellVO> cells = forecastReplayDetailVO.getCells();
                    Map<String, DynamicCellVO> dynamicCellVOMap =
                            cells.stream().collect(Collectors.toMap(DynamicCellVO::getPlanPeriod,
                                    Function.identity()));
                    for (String planPeriodStr : planPeriods) {
                        if (dynamicCellVOMap.containsKey(planPeriodStr)) {
                            continue;
                        }
                        DynamicCellVO dynamicCellVO =
                                DynamicCellVO.builder().planPeriod(planPeriodStr).quantity(BigDecimal.ZERO).build();
                        cells.add(dynamicCellVO);
                    }
                }

                // 计算固定行：实际发货值，前月，上月，当月行
                for (int i = 0; i < planPeriods.size(); i++) {
                	//时间格式202403需转换2024-03
                	String pp = planPeriods.get(i);
                	String cellPlanPeriod = pp.substring(0, 4) + "-" + pp.substring(4, 6);
                    BigDecimal value = keyQuantityMap.getOrDefault(pp, BigDecimal.ZERO);
                    currentQtyList.add(DynamicCellVO.builder().planPeriod(cellPlanPeriod).quantity(value).build());

                    String joinMain = String.join(Constants.DELIMITER, pp, join);
                    BigDecimal demandQty = valueMap.getOrDefault(joinMain, BigDecimal.ZERO);
                    BigDecimal releaseQty = keyQuantityMap.getOrDefault(pp, BigDecimal.ZERO);
                    BigDecimal accuracy = getAchievement(demandQty, releaseQty);
                    currentMonth.add(DynamicCellVO.builder().planPeriod(cellPlanPeriod).quantity(null).accuracy(accuracy).build());

                    if (i >= 1) {
                        String pp1 = planPeriods.get(i - 1);
                        BigDecimal demandQty1 = valueMap.getOrDefault(String.join(Constants.DELIMITER, pp1, join)
                                , BigDecimal.ZERO);
                        BigDecimal releaseQty1 = keyQuantityMap.getOrDefault(pp1, BigDecimal.ZERO);

                        BigDecimal accuracy1 = getAchievement(demandQty1, releaseQty1);
                        previousMonth.add(DynamicCellVO.builder().planPeriod(cellPlanPeriod).quantity(null).accuracy(accuracy1).build());
                    } else {
                        previousMonth.add(DynamicCellVO.builder().planPeriod(cellPlanPeriod).quantity(null).accuracy(BigDecimal.ZERO).build());
                    }

                    if (i >= 2) {
                        String pp2 = planPeriods.get(i - 2);
                        BigDecimal demandQty2 = valueMap.get(String.join(Constants.DELIMITER, pp2, join));
                        BigDecimal releaseQty2 = keyQuantityMap.getOrDefault(pp2, BigDecimal.ZERO);

                        BigDecimal accuracy2 = getAchievement(demandQty2, releaseQty2);
                        monthBeforeLast.add(DynamicCellVO.builder().planPeriod(cellPlanPeriod).quantity(null).accuracy(accuracy2).build());
                    } else {
                        monthBeforeLast.add(DynamicCellVO.builder().planPeriod(cellPlanPeriod).quantity(null).accuracy(BigDecimal.ZERO).build());
                    }

                }

                // 实际发货值
                ForecastReplayDetailVO buildQty =
                        ForecastReplayDetailVO.builder().rowType(RowTypeEnum.ACTUAL_SHIPMENT).cells(new ArrayList<>()).build();
                buildQty.getCells().addAll(currentQtyList);
                demandForecastReplayVO.getDetailList().add(buildQty);

                // 当月预测准确率
                ForecastReplayDetailVO build1 =
                        ForecastReplayDetailVO.builder().rowType(RowTypeEnum.CURRENT_MONTH).cells(new ArrayList<>()).build();
                build1.getCells().addAll(currentMonth);
                demandForecastReplayVO.getDetailList().add(build1);

                // 上月预测准确率
                ForecastReplayDetailVO build2 =
                        ForecastReplayDetailVO.builder().rowType(RowTypeEnum.PREVIOUS_MONTH).cells(new ArrayList<>()).build();
                build2.getCells().addAll(previousMonth);
                demandForecastReplayVO.getDetailList().add(build2);

                // 前月预测准确率
                ForecastReplayDetailVO build3 =
                        ForecastReplayDetailVO.builder().rowType(RowTypeEnum.MONTH_BEFORE_LAST).cells(new ArrayList<>()).build();
                build3.getCells().addAll(monthBeforeLast);
                demandForecastReplayVO.getDetailList().add(build3);

            } else {
                ForecastReplayDetailVO build =
                        ForecastReplayDetailVO.builder().rowType(RowTypeEnum.ACTUAL_SHIPMENT).build();
                demandForecastReplayVO.getDetailList().add(build);
                ForecastReplayDetailVO build2 =
                        ForecastReplayDetailVO.builder().rowType(RowTypeEnum.CURRENT_MONTH).build();
                demandForecastReplayVO.getDetailList().add(build2);
                ForecastReplayDetailVO build3 =
                        ForecastReplayDetailVO.builder().rowType(RowTypeEnum.PREVIOUS_MONTH).build();
                demandForecastReplayVO.getDetailList().add(build3);
                ForecastReplayDetailVO build4 =
                        ForecastReplayDetailVO.builder().rowType(RowTypeEnum.MONTH_BEFORE_LAST).build();
                demandForecastReplayVO.getDetailList().add(build4);
            }
        }
        log.info("预测复盘结果查询结束");
        return dataList;
    }

    private BigDecimal getAchievement(BigDecimal demandForecast, BigDecimal delivered) {
        if (null == demandForecast || null == delivered) {
            return BigDecimal.ZERO;
        }
        if (delivered.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        // 计算未达成量
        BigDecimal subtract = demandForecast.subtract(delivered);
        // 计算未达成量比例
        BigDecimal divide = BigDecimalUtils.divide(subtract, demandForecast, 2);
        if (divide.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal value = BigDecimal.ONE.subtract(divide);
        return BigDecimalUtils.multiply(value, new BigDecimal("100"));
    }

    private Map<String, BigDecimal> convertReplay2ValueMap(List<DemandForecastReplayVO> consistenceReleaseList) {
        Map<String, BigDecimal> result = new HashMap<>(16);
        for (DemandForecastReplayVO item : consistenceReleaseList) {
            String oemCode = item.getOemCode();
            String vehicleModelCode = item.getVehicleModelCode();
            String productCode = item.getProductCode();
            List<ForecastReplayDetailVO> detailList = item.getDetailList();
            for (ForecastReplayDetailVO secItem : detailList) {
                if (!RowTypeEnum.DEMAND_FORECAST.equals(secItem.getRowType())) {
                    continue;
                }
                List<DynamicCellVO> cells = secItem.getCells();
                for (DynamicCellVO cell : cells) {
                    String xAxis = cell.getPlanPeriod();
                    BigDecimal quantity = cell.getQuantity();
                    result.put(String.join(Constants.DELIMITER, xAxis, oemCode, vehicleModelCode, productCode),
                            quantity);
                }
            }
        }
        return result;
    }

    private Map<String, Map<String, BigDecimal>> getWarehouseReleaseRecordByMonth(
            List<WarehouseReleaseRecordMonthVO> releaseRecords, String showLevel) {
        // 获取每个月的发货量数据
        Function<WarehouseReleaseRecordMonthVO, String> function = ShowLevelEnum.OEM.getCode().equals(showLevel) ?
                oemShowLevelFunction1() :
                (ShowLevelEnum.VEHICLE_MODEL.getCode().equals(showLevel) ? vehicleShowLevelFunction1() :
                        productShowLevelFunction1());

        return releaseRecords.stream().collect(Collectors.groupingBy(function, Collectors.groupingBy(
                WarehouseReleaseRecordMonthVO::getYearMonth,
                Collectors.reducing(BigDecimal.ZERO,
                        WarehouseReleaseRecordMonthVO::getSumQty,
                        BigDecimal::add))));
    }

    private static Function<WarehouseReleaseRecordMonthVO, String> productShowLevelFunction1() {
        return item -> String.join(Constants.DELIMITER, item.getOemCode(),
                item.getVehicleModelCode(), item.getItemCode());
    }

    private static Function<WarehouseReleaseRecordMonthVO, String> vehicleShowLevelFunction1() {
        return item -> String.join(Constants.DELIMITER, item.getOemCode(),
                item.getVehicleModelCode());
    }

    private static Function<WarehouseReleaseRecordMonthVO, String> oemShowLevelFunction1() {
        return item -> String.join(Constants.DELIMITER, item.getOemCode());
    }

    /**
     * 获取排序后的日期李彪
     *
     * @param showBeginTime 开始时间
     * @param showEndTime   结束时间
     * @return java.util.List<java.lang.String>
     */
    @SneakyThrows
    private static List<String> getSortedDateList(String showBeginTime, String showEndTime) {
        List<String> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM);
        // 获取最小日期
        Calendar min = Calendar.getInstance();
        // 获取最大日期
        Calendar max = Calendar.getInstance();

        min.setTime(sdf.parse(showBeginTime));
        // 最小日期的1号
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        max.setTime(sdf.parse(showEndTime));
        // 最大日期2号
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        // 初始赋值，从最小的开始
        // 判断 是否大于 最大日期2号
        while (min.before(max)) {
            // 放入list
            result.add(sdf.format(min.getTime()));
            // 月 + 1
            min.add(Calendar.MONTH, 1);
            // 看到这里，就知道为什么需要定义最大日期的2号开始
        }
        return result;
    }

}