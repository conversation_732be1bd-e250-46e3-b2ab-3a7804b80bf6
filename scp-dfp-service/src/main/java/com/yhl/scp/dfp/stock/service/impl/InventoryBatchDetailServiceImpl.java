package com.yhl.scp.dfp.stock.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.stock.convertor.InventoryBatchDetailConvertor;
import com.yhl.scp.dfp.stock.domain.entity.InventoryBatchDetailDO;
import com.yhl.scp.dfp.stock.domain.service.InventoryBatchDetailDomainService;
import com.yhl.scp.dfp.stock.dto.InventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryBatchDetailDao;
import com.yhl.scp.dfp.stock.infrastructure.po.InventoryBatchDetailPO;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.product.vo.StockPointBasicVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.extension.product.vo.StockPointVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.material.plan.enums.GlassRefreshMrpEnum;
import com.yhl.scp.sds.basic.pegging.vo.FulfillmentBasicVO;
import com.yhl.scp.sds.extension.pegging.dto.DemandDTO;
import com.yhl.scp.sds.extension.pegging.dto.FulfillmentDTO;
import com.yhl.scp.sds.extension.pegging.dto.SupplyDTO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.FulfillmentVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>InventoryBatchDetailServiceImpl</code>
 * <p>
 * 库存批次明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 09:53:39
 */
@Slf4j
@Service
public class InventoryBatchDetailServiceImpl extends AbstractService implements InventoryBatchDetailService {

    @Resource
    private InventoryBatchDetailDao inventoryBatchDetailDao;

    @Resource
    private InventoryBatchDetailDomainService inventoryBatchDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    private MpsFeign mpsFeign;
    @Resource
    private MrpFeign mrpFeign;
    public static final String INVENTORY_SUPPLY = "INVENTORY_SUPPLY";
    public static final String UNFULFILL = "UNFULFILL";

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(InventoryBatchDetailDTO inventoryBatchDetailDTO) {
        // 0.数据转换
        InventoryBatchDetailDO inventoryBatchDetailDO = InventoryBatchDetailConvertor.INSTANCE.dto2Do(inventoryBatchDetailDTO);
        InventoryBatchDetailPO inventoryBatchDetailPO = InventoryBatchDetailConvertor.INSTANCE.dto2Po(inventoryBatchDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryBatchDetailDomainService.validation(inventoryBatchDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(inventoryBatchDetailPO);
        inventoryBatchDetailDao.insert(inventoryBatchDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(InventoryBatchDetailDTO inventoryBatchDetailDTO) {
        // 0.数据转换
        InventoryBatchDetailDO inventoryBatchDetailDO = InventoryBatchDetailConvertor.INSTANCE.dto2Do(inventoryBatchDetailDTO);
        InventoryBatchDetailPO inventoryBatchDetailPO = InventoryBatchDetailConvertor.INSTANCE.dto2Po(inventoryBatchDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryBatchDetailDomainService.validation(inventoryBatchDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(inventoryBatchDetailPO);
        inventoryBatchDetailDao.update(inventoryBatchDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<InventoryBatchDetailDTO> list) {
        List<InventoryBatchDetailPO> newList = InventoryBatchDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        inventoryBatchDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<InventoryBatchDetailDTO> list) {
        List<InventoryBatchDetailPO> newList = InventoryBatchDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryBatchDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return inventoryBatchDetailDao.deleteBatch(idList);
        }
        return inventoryBatchDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public InventoryBatchDetailVO selectByPrimaryKey(String id) {
        InventoryBatchDetailPO po = inventoryBatchDetailDao.selectByPrimaryKey(id);
        return InventoryBatchDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "INVENTORY_BATCH_DETAIL")
    public List<InventoryBatchDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "INVENTORY_BATCH_DETAIL")
    public List<InventoryBatchDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<InventoryBatchDetailVO> dataList = inventoryBatchDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        InventoryBatchDetailServiceImpl target = springBeanUtils.getBean(InventoryBatchDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<InventoryBatchDetailVO> selectByParams(Map<String, Object> params) {
        List<InventoryBatchDetailPO> list = inventoryBatchDetailDao.selectByParams(params);
        return InventoryBatchDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<InventoryBatchDetailVO> selectVOByParams(Map<String, Object> params) {
        return inventoryBatchDetailDao.selectVOByParams(params);
    }

    @Override
    public PageInfo<InventoryBatchDetailVO> selectByParamsPage(Map<String, Object> params) {
        PageHelper.startPage((Integer) params.get("pageNum"), (Integer) params.get("pageSize"));
        List<InventoryBatchDetailVO> inventoryBatchDetailPOS = inventoryBatchDetailDao.selectByVOParams(params);
        return new PageInfo<>(inventoryBatchDetailPOS);
    }

    @Override
    public List<InventoryBatchDetailVO> selectByVOParams(Map<String, Object> params) {
        return inventoryBatchDetailDao.selectByVOParams02(params);
    }

    @Override
    public PageInfo<InventoryBatchDetailVO> selectInventoryBatchDetailPageCustomize(Pagination pagination, String sortParam, String queryCriteriaParam, String overdueSort) {
        // 查询全部数据，因为需要根据超期天数排序（过滤出原片的数据，通用类别是RA.A）
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<InventoryBatchDetailVO> allDataList = inventoryBatchDetailDao.selectByConditionOurFactory(sortParam, queryCriteriaParam);
        for (InventoryBatchDetailVO inventoryBatchDetailVO : allDataList) {
            inventoryBatchDetailVO.setCurrentQuantitySum(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
        }
        return new PageInfo<>(allDataList);
    }

    @Override
    public List<InventoryBatchDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public int doDeleteAll(String sourceType) {
        return inventoryBatchDetailDao.deleteAll(sourceType);
    }

    @Override
    public int doDeleteAllByOrgId(String sourceType, String originalOrgId) {
        return inventoryBatchDetailDao.deleteAllByOrgId(sourceType, originalOrgId);
    }

    @Override
    public int doDeleteAllByOrgIds(String sourceType, List<String> orgIds) {

        return inventoryBatchDetailDao.doDeleteAllByOrgIds(sourceType, orgIds);
    }

    /**
     * 库存批次明细数据同步
     *
     * @return
     */
    @Override
    public BaseResponse<Void> syncStockBatchDetail(Scenario scenario, String stockPoints) {
        log.info("开始同步MES库存批次明细数据");
        this.syncMesStockBatchDetail(scenario, stockPoints);
        log.info("同步MES库存批次明细数据完成");
        log.info("开始同步ERP库存批次明细数据");
        this.syncErpStockBatchDetail(scenario, stockPoints);
        log.info("同步ERP库存批次明细数据完成");
        return BaseResponse.success("同步成功");
    }

    /**
     * ERP库存批次明细数据同步
     *
     * @return
     */
    @Override
    public BaseResponse<Void> syncErpStockBatchDetail(Scenario scenario, String stockPoints) {

        Map stockParams = MapUtil.builder().put("enabled", YesOrNoEnum.YES.getCode()).build();
        List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(scenario.getDataBaseName(), stockParams);

        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        Map<String, Object> scenarioMap = new HashMap<>();
        scenarioMap.put("tenantId", scenario.getTenantId());
        scenarioMap.put("dataBaseName", scenario.getDataBaseName());
        List<String> orgIds = newStockPointVOS.stream().
                filter(t -> StringUtils.isNotEmpty(t.getOrganizeId()) && StringUtils.isNotEmpty(t.getInterfaceFlag())).
                filter(t -> t.getInterfaceFlag().contains(ApiCategoryEnum.ERP_INVENTORY_BATCH_DETAIL.getCode())).
                map(NewStockPointVO::getOrganizeId).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(stockPoints)) {
            List<String> stockPointsList = Arrays.asList(stockPoints.split(","));
            List<String> filteredStockPoints = stockPointsList.stream()
                    .filter(orgIds::contains)
                    .collect(Collectors.toList());
            for (String orgId : filteredStockPoints) {
                // 调用远程的销售组织信息
                Map<String, Object> newStockPoingMap = new HashMap<>(4);
                newStockPoingMap.put("orgId", orgId);
                newStockPoingMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
                newStockPoingMap.put("scenario", scenarioMap);

                newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.ERP.getCode(),
                        ApiCategoryEnum.ERP_INVENTORY_BATCH_DETAIL.getCode(), newStockPoingMap);
            }
        } else {
            log.info("同步所有库存点：{}", orgIds);
            for (String orgId : orgIds) {
                log.info("开始同步库存点：{}", orgId);
                // 调用远程的销售组织信息
                Map<String, Object> newStockPoingMap = new HashMap<>(4);
                newStockPoingMap.put("orgId", orgId);
                newStockPoingMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
                newStockPoingMap.put("scenario", scenarioMap);
                newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.ERP.getCode(),
                        ApiCategoryEnum.ERP_INVENTORY_BATCH_DETAIL.getCode(), newStockPoingMap);
            }
        }
        return BaseResponse.success("同步成功");
    }

    /**
     * MES库存批次明细数据同步
     *
     * @param scenario 场景信息
     * @param stockPoints 库存点列表，逗号分隔
     * @return 同步结果
     */
    @Override
    public BaseResponse<Void> syncMesStockBatchDetail(Scenario scenario, String stockPoints) {
        try {

            List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(scenario.getDataBaseName(),
                    ImmutableMap.of("enabled",YesOrNoEnum.YES.getCode()));
            if (CollectionUtils.isEmpty(newStockPointVOS)) {
                log.warn("scenario: {} 下库存点信息为空", scenario.getDataBaseName());
                return BaseResponse.error("库存点信息为空");
            }

            Map<String, Object> scenarioMap = new HashMap<>(4);
            scenarioMap.put("tenantId", scenario.getTenantId());
            scenarioMap.put("dataBaseName", scenario.getDataBaseName());

            List<String> orgIds = newStockPointVOS.stream()
                    .filter(t -> StringUtils.isNotEmpty(t.getOrganizeId()) && StringUtils.isNotEmpty(t.getInterfaceFlag()))
                    .filter(t -> t.getInterfaceFlag().contains(ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode()))
                    .map(NewStockPointVO::getOrganizeId)
                    .collect(Collectors.toList());

            if (StringUtils.isNotEmpty(stockPoints)) {
                List<String> stockPointsList = Arrays.asList(stockPoints.split(","));
                List<String> plantIds = stockPointsList.stream()
                        .filter(orgIds::contains)
                        .collect(Collectors.toList());
                
                log.info("指定同步库存点: {}", plantIds);
                for (String orgId : plantIds) {
                    Map<String, Object> params = MapUtil.newHashMap();
                    params.put("scenario", scenarioMap);
                    params.put("plantId", orgId);
                    
                    log.info("开始同步库存点: {}", orgId);
                    newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.MES.getCode(),
                            ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode(), params);
                    log.info("库存点: {} 同步完成", orgId);
                }
            } else {
                log.info("同步所有库存点: {}", orgIds);
                for (String orgId : orgIds) {
                    Map<String, Object> params = MapUtil.newHashMap();
                    params.put("scenario", scenarioMap);
                    params.put("plantId", orgId);
                    
                    log.info("开始同步库存点: {}", orgId);
                    newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.MES.getCode(),
                            ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode(), params);
                    log.info("库存点: {} 同步完成", orgId);
                }
            }
            
            return BaseResponse.success("同步成功");
            
        } catch (Exception e) {
            log.error("MES库存批次明细同步异常 - scenario: {}", scenario.getDataBaseName(), e);
            return BaseResponse.error("同步异常: " + e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> syncMesStockBatchDetail(Scenario scenario, List<String> stockPoints) {
        Map<String, Object> scenarioMap = new HashMap<>(4);
        scenarioMap.put("tenantId", scenario.getTenantId());
        scenarioMap.put("dataBaseName", scenario.getDataBaseName());
        for (String orgId : stockPoints) {
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("scenario", scenarioMap);
            params.put("plantId", orgId);

            log.info("开始同步库存点: {}", orgId);
            newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode(), params);
            log.info("库存点: {} 同步完成", orgId);
        }
        return null;
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.INVENTORY_BATCH_DETAIL.getCode();
    }

    @Override
    public List<InventoryBatchDetailVO> invocation(List<InventoryBatchDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<InventoryBatchDetailVO> selectByProductCodes(List<String> productCodes, String stockPointType) {
        return inventoryBatchDetailDao.selectByProductCodes(productCodes, stockPointType);
    }

    @Override
    public List<InventoryBatchDetailVO> selectByParamMap(Map<String, Object> params) {
        log.info("实时库存查询参数:{}", JSON.toJSONString(params));
        return inventoryBatchDetailDao.selectByParamMap(params);
    }

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return 0;
        }
        inventoryBatchDetailDomainService.checkDelete(removeVersionDTOS);
        return inventoryBatchDetailDao.deleteBatchVersion(removeVersionDTOS);
    }


    @Override
    public Map<String, Integer> getRealTimeInventory(List<String> productCodes) {
        Map<String, Integer> result = new HashMap<>();
        List<InventoryBatchDetailVO> realTimeDataPOList = inventoryBatchDetailDao.selectRealTimeInventory(productCodes);
        if (CollectionUtils.isNotEmpty(realTimeDataPOList)) {
            realTimeDataPOList.forEach(x -> {
                result.put(x.getOemCode() + "&&" + x.getProductCode(), Double.valueOf(x.getCurrentQuantity()).intValue());
            });
        }
        return result;
    }

    @Override
    public BaseResponse<Void> syncMes(String scenario, List<MesInventoryBatchDetail> details, List<String> orgIds) {
        log.info("DFPFeign开始处理库存批次详情");

        if (CollectionUtils.isEmpty(details)) {
            return BaseResponse.success();
        }

        Map<String, Object> stockQueryMap = MapUtil.newHashMap();
        stockQueryMap.put("organizeIds", orgIds);
        stockQueryMap.put("enabled", YesOrNoEnum.YES.getCode());

        List<NewStockPointVO> stockPoints = mdsFeign.selectStockPointByParams(scenario, stockQueryMap);
        Map<String, NewStockPointVO> stockMap = stockPoints.stream()
                .collect(Collectors.toMap(NewStockPointVO::getOrganizeId, Function.identity(), (v1, v2) -> v1));

        List<String> itemIds = details.stream().map(MesInventoryBatchDetail::getItemId).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productPoints = mdsFeign.selectProductStockPointByParams(scenario,
                ImmutableMap.of("inventoryItemIds", itemIds));

        Map<String, NewProductStockPointVO> productMap = productPoints.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getInventoryItemId, Function.identity(), (v1, v2) -> v1));

        Date currentDate = DateUtils.truncateTimeOfDate(new Date());

        int unMatchCount = 0;
        int bProductCount = 0;

        List<MesInventoryBatchDetail> summaryDetails = new ArrayList<>();
        Map<String, MesInventoryBatchDetail> groupedMap = new HashMap<>();

        for (MesInventoryBatchDetail detail : details) {
            String key = detail.getCode() + "#" + detail.getItemCode() + "#" + detail.getWarehouseCode();
            log.info("库存批次接口查找key：{}",key);
            if (!groupedMap.containsKey(key)) {
                MesInventoryBatchDetail copy = new MesInventoryBatchDetail();
                copy.setLastUpdateDate(detail.getLastUpdateDate());
                copy.setQuantity(detail.getQuantity() != null ? detail.getQuantity() : 0);
                copy.setSupplierId(detail.getSupplierId());
                copy.setCode(detail.getCode());
                copy.setLocatorDesc(detail.getLocatorDesc());
                copy.setPlantId(detail.getPlantId());
                copy.setWarehouseDesc(detail.getWarehouseDesc());
                copy.setLotNumber(detail.getLotNumber());
                copy.setWarehouseCode(detail.getWarehouseCode());
                copy.setPartNum(detail.getPartNum());
                copy.setTypeCode(detail.getTypeCode());
                copy.setItemId(detail.getItemId());
                copy.setItemCode(detail.getItemCode());
                copy.setLocatorCode(detail.getLocatorCode());
                copy.setAssignedTime(detail.getAssignedTime());
                groupedMap.put(key, copy);
            } else {
                MesInventoryBatchDetail existing = groupedMap.get(key);
                int existingQty = existing.getQuantity() != null ? existing.getQuantity() : 0;
                int newQty = detail.getQuantity() != null ? detail.getQuantity() : 0;
                existing.setQuantity(existingQty + newQty);
            }
        }

        summaryDetails.addAll(groupedMap.values());


        List<InventoryBatchDetailDTO> batchList = new ArrayList<>();

        for (MesInventoryBatchDetail detail : summaryDetails) {
            String itemId = detail.getItemId();
            String itemCode = detail.getItemCode();
            String plantId = detail.getPlantId();

            NewStockPointVO stockPoint = stockMap.get(plantId);
            if (stockPoint == null) {
                log.info("库存点：{}，不存在，跳过", plantId);
                unMatchCount++;
                continue;
            }

            NewProductStockPointVO productPoint = productMap.get(itemId);
            if (productPoint != null &&
                    stockPoint.getInterfaceFlag().contains(ApiCategoryEnum.ERP_INVENTORY_BATCH_DETAIL.getCode()) &&
                    stockPoint.getInterfaceFlag().contains(ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode()) &&
                    productPoint.getClassifyDesc().startsWith("B")) {
                log.info("库存点：{}，物料编码:{},物料分类为：{}，是B类，跳过", stockPoint.getOrganizeId(),productPoint.getProductCode(), productPoint.getClassifyDesc());
                bProductCount++;
                continue;
            }

            InventoryBatchDetailDTO dto = new InventoryBatchDetailDTO();
            boolean isNegativeItemId = false;
            try {
                long itemIdValue = Long.parseLong(itemId);
                isNegativeItemId = itemIdValue < 0;
            } catch (NumberFormatException e) {
                log.info("itemId不是有效数字");
            }
            if (isNegativeItemId) {
                int lastDashIndex = itemCode.lastIndexOf('-');
                if (lastDashIndex > 0 && lastDashIndex < itemCode.length() - 1) {
                    // 检查"-"后是否都是数字
                    String suffix = itemCode.substring(lastDashIndex + 1);
                    if (suffix.matches("\\d+")) {
                        dto.setProductCode(itemCode.substring(0, lastDashIndex));
                        dto.setOperationCode(suffix);
                    } else {
                        dto.setProductCode(itemCode);
                    }
                } else {
                    dto.setProductCode(itemCode);
                }
            } else {
                dto.setProductCode(itemCode);
            }

            dto.setOriginalProductCode(itemCode);
            dto.setOriginalOrgId(plantId);
            dto.setStockPointCode(stockPoint.getStockPointCode());
            dto.setCurrentQuantity(String.valueOf(detail.getQuantity()));
            dto.setBatch(detail.getLotNumber());
            dto.setCustomerNum(detail.getSupplierId());
            dto.setPartNum(detail.getPartNum());
            dto.setFreightSpace(String.valueOf(detail.getLocatorCode()));
            dto.setFreightSpaceDescription(detail.getLocatorDesc());
            dto.setSubinventory(detail.getWarehouseCode());
            dto.setSubinventoryDescription(detail.getWarehouseDesc());
            dto.setAssignedTime(detail.getAssignedTime());
            dto.setBarCode(detail.getCode());
            dto.setSourceType(ApiSourceEnum.MES.getCode());
            dto.setLastUpdateDate(DateUtils.dateToString(detail.getLastUpdateDate()));

            if (productPoint != null) {
                dto.setProductCode(productPoint.getProductCode());

                if (productPoint.getExpireDate() != null) {
                    dto.setWarrantyDate(String.valueOf(productPoint.getExpireDate()));
                }

                // 库龄、保质期时间计算
                Date assignDate = DateUtils.stringToDate(dto.getAssignedTime());
                int ageDays = (int) DateUtil.between(assignDate, currentDate, DateUnit.DAY);
                dto.setStockAgeDay(String.valueOf(ageDays));
                dto.setStockAge(resolveStockAgeLabel(ageDays / 30));

                int distanceEnableDate = ageDays - Optional.ofNullable(productPoint.getExpireDate()).orElse(0);
                dto.setDistanceEnableDate(String.valueOf(distanceEnableDate));
            }

            batchList.add(dto);
        }

        // 插入数据
        if (CollectionUtils.isNotEmpty(batchList)) {
            this.doDeleteAllByOrgIds(ApiSourceEnum.MES.getCode(), orgIds);
            log.info("删除库存点ID为{}的MES库存批次明细数据", orgIds);

            List<List<InventoryBatchDetailDTO>> partitions = com.google.common.collect.Lists.partition(batchList, 3000);
            for (List<InventoryBatchDetailDTO> part : partitions) {
                this.doCreateBatch(part);
            }
        }

        log.info("同步库存批次明细数据完成，库存点:{}，未匹配:{}，B类:{}，新增:{}", orgIds, unMatchCount, bProductCount, batchList.size());
        return BaseResponse.success("同步成功");
    }

    // 封装：库龄区间标签
    private String resolveStockAgeLabel(int months) {
        if (months <= 3) {
            return "≤3 个月";
        }
        if (months <= 6) {
            return "4-6 个月";
        }
        if (months <= 12) {
            return "7-12 个月";
        }
        if (months <= 24) {
            return "1-2 年";
        }
        if (months <= 36) {
            return "2-3 年";
        }
        return "超过三年";
    }

    @Override
    public BaseResponse<Void> syncErp(String scenario, List<ErpInventoryBatchDetail> erpInventoryBatchDetails, String orgId) {
        log.info("DFPFeign开始处理ERP库存批次详情");
        if (CollectionUtils.isEmpty(erpInventoryBatchDetails)) {
            return BaseResponse.success();
        }
        List<NewStockPointVO> stockPoints = mdsFeign.selectAllStockPoint(scenario);
        Map<String, NewStockPointVO> stockMap = stockPoints.stream().collect(Collectors.toMap(NewStockPointVO::getOrganizeId, Function.identity(), (v1, v2) -> v1));

        List<InventoryBatchDetailDTO> insertInventoryBatchDetailDTOs = Lists.newArrayList();

        List<String> itemIds = erpInventoryBatchDetails.stream().map(ErpInventoryBatchDetail::getItemId).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectProductStockPointByParams(scenario, ImmutableMap.of("inventoryItemIds", itemIds));

        Map<String, NewProductStockPointVO> productStockMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getInventoryItemId, Function.identity(), (v1, v2) -> v1));

        int unMatchCount = 0;
        int bProductCount = 0;
        for (ErpInventoryBatchDetail erpInventoryBatchDetail : erpInventoryBatchDetails) {

            String itemId = erpInventoryBatchDetail.getItemId();
            String erpOrgId = erpInventoryBatchDetail.getOrgId();

            String itemCode = erpInventoryBatchDetail.getItemCode();
            NewStockPointVO newStockPointVO = stockMap.get(erpOrgId);

            NewProductStockPointVO newProductStockPointVO = productStockMap.get(itemId);

            InventoryBatchDetailDTO inventoryBatchDetailDTO = new InventoryBatchDetailDTO();
            if (Objects.nonNull(newProductStockPointVO)) {
                inventoryBatchDetailDTO.setProductCode(newProductStockPointVO.getProductCode());
                if (Objects.nonNull(newProductStockPointVO.getExpireDate())) {
                    inventoryBatchDetailDTO.setWarrantyDate(String.valueOf(newProductStockPointVO.getExpireDate()));
                }
                if (newStockPointVO.getInterfaceFlag().contains(ApiCategoryEnum.ERP_INVENTORY_BATCH_DETAIL.getCode())
                        && newStockPointVO.getInterfaceFlag().contains(ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode())) {
                    if (!newProductStockPointVO.getClassifyDesc().startsWith("B")) {
                        bProductCount++;
                        log.info("库存点：{}，物料分类为：{}，不是B类型，跳过", newStockPointVO.getOrganizeId(), newProductStockPointVO.getClassifyDesc());
                        continue;
                    }

                }
            } else {
                log.error("没有找到对应的物料信息：{}，库存点：{}", erpInventoryBatchDetail.getItemCode(), erpInventoryBatchDetail.getOrgId());
                unMatchCount++;
                continue;
            }

            if (Objects.equals(newStockPointVO.getPlanArea(), "FYE") && Integer.parseInt(newStockPointVO.getOrganizeId().trim()) > 0) {
                log.info("匹配到FYE库存点，库存点代码：{}，名称：{}，id:{}", newStockPointVO.getStockPointCode(), newStockPointVO.getStockPointName(), newStockPointVO.getOrganizeId());
                inventoryBatchDetailDTO.setStockPointCode("FYE");
            } else if (Objects.equals(newStockPointVO.getPlanArea(), "FYSSL")) {
                log.info("匹配到FYSSL库存点，库存点代码：{}，名称：{}，id:{}", newStockPointVO.getStockPointCode(), newStockPointVO.getStockPointName(), newStockPointVO.getOrganizeId());
                inventoryBatchDetailDTO.setStockPointCode("FYSSL");
            } else {
                inventoryBatchDetailDTO.setStockPointCode(newStockPointVO.getStockPointCode());
            }
            if(itemCode.length()>3&&itemCode.charAt(itemCode.length() - 3) == '-'){
                inventoryBatchDetailDTO.setProductCode(itemCode.substring(0, itemCode.length() - 3));
                inventoryBatchDetailDTO.setOperationCode(itemCode.substring(itemCode.length() - 2));
            }else{
                inventoryBatchDetailDTO.setProductCode(erpInventoryBatchDetail.getItemCode());
            }
            inventoryBatchDetailDTO.setOriginalProductCode(erpInventoryBatchDetail.getItemCode());
            inventoryBatchDetailDTO.setOriginalOrgId(orgId);
            inventoryBatchDetailDTO.setProductCode(erpInventoryBatchDetail.getItemCode());
            inventoryBatchDetailDTO.setCurrentQuantity(String.valueOf(erpInventoryBatchDetail.getOnHandQty()));
            inventoryBatchDetailDTO.setBatch(erpInventoryBatchDetail.getLotNumber());
            inventoryBatchDetailDTO.setFreightSpace(String.valueOf(erpInventoryBatchDetail.getLocatorCode()));
            inventoryBatchDetailDTO.setFreightSpaceDescription(erpInventoryBatchDetail.getLocatorDesc());
            inventoryBatchDetailDTO.setSubinventory(erpInventoryBatchDetail.getSubCode());
            inventoryBatchDetailDTO.setSubinventoryDescription(erpInventoryBatchDetail.getSubDesc());
            inventoryBatchDetailDTO.setSourceType(ApiSourceEnum.ERP.getCode());
            inventoryBatchDetailDTO.setLastUpdateDate(DateUtils.dateToString(erpInventoryBatchDetail.getLastUpdateDate()));
            insertInventoryBatchDetailDTOs.add(inventoryBatchDetailDTO);
        }

        if (CollectionUtils.isNotEmpty(insertInventoryBatchDetailDTOs)) {
            this.doDeleteAllByOrgId(ApiSourceEnum.ERP.getCode(), orgId);
            log.info("删除库存点ID为{}的ERP库存批次明细数据", orgId);

            List<List<InventoryBatchDetailDTO>> partition = com.google.common.collect.Lists.partition(insertInventoryBatchDetailDTOs, 3000);
            for (List<InventoryBatchDetailDTO> inventoryBatchDetailDTOS : partition) {
                this.doCreateBatch(inventoryBatchDetailDTOS);
            }
        }
        log.info("同步ERP库存批次明细数据，库存点:{},未匹配物料数量：{},非B类数量{},新增数据量:{}", orgId, unMatchCount, bProductCount, insertInventoryBatchDetailDTOs.size());

        return BaseResponse.success("同步成功");
    }

    @Override
    public List<InventoryBatchDetailVO> selectCollectByGroupType(String groupTyep) {
        List<InventoryBatchDetailVO> resultList = new ArrayList<>();
        if ("LOADING_POSIT".equals(groupTyep)) {
            //按照库存点，装车位置，工序编码分组统计实时库存数量
            resultList = inventoryBatchDetailDao.selectCollectGroupByLoadingPosit();
        } else {
            //按照库存点, 工序编码分组统计实时库存数量
            resultList = inventoryBatchDetailDao.selectCollectGroupByNoLoadingPosit();
        }
        resultList = resultList.stream().filter(e -> StringUtils.isNotEmpty(e.getOperationCode())).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<InventoryBatchDetailVO> selectAllGlassInventoryBatch() {
        return inventoryBatchDetailDao.selectAllGlassInventoryBatch();
    }

    @Override
    public void doProcessSupply(String scenario) {

        Map<String, Object> stockQueryMap = MapUtil.newHashMap();
        stockQueryMap.put("enabled", YesOrNoEnum.YES.getCode());
        stockQueryMap.put("stockPointType", StockPointTypeEnum.BC.getCode());
        List<NewStockPointVO> bcStockPoints = mdsFeign.selectStockPointByParams(scenario, stockQueryMap);
        Map<String, SupplyVO> supplyMap;
        List<SubInventoryCargoLocationVO> subInventoryCargoLocationInformationVOS;
        Map<String, SubInventoryCargoLocationVO> subInventoryMap;
        List<SupplyDTO> insertSupplyList = new ArrayList<>();
        List<SupplyDTO> updateSupplyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bcStockPoints)) {
            List<String> bcStockPointCodes=bcStockPoints.stream().map(NewStockPointVO::getStockPointCode).distinct().collect(Collectors.toList());

            Map<String, Object> subQueryMap = new HashMap<>(4);
            subQueryMap.put("stockPointCodes", bcStockPointCodes);
            subQueryMap.put("enabled", YesOrNoEnum.YES.getCode());
            //库存点，货位确认哪些货位库存点需要写入supply表
            subQueryMap.put("valid", YesOrNoEnum.YES.getCode());
            subInventoryCargoLocationInformationVOS = mpsFeign.queryByParams1(scenario, subQueryMap);
            subInventoryMap = subInventoryCargoLocationInformationVOS.stream().collect(Collectors
                    .toMap(t -> t.getFactoryCode() + "_" + t.getFreightSpaceCode(), Function.identity(), (v1, v2) -> v1));
            for (NewStockPointVO stockPointVO : bcStockPoints) {
                String stockPointCode = stockPointVO.getStockPointCode();
                String stockPointId = stockPointVO.getOrganizeId();
                List<InventoryBatchDetailVO> inventoryBatchDetails = this.selectVOByParams(ImmutableMap
                        .of("stockPointCode", stockPointCode, "sourceType", ApiSourceEnum.MES.getCode()));
                if (CollectionUtils.isNotEmpty(inventoryBatchDetails)) {
                    Map<String, Object> supplyQueryMap = MapUtil.newHashMap();
                    supplyQueryMap.put("stockPointId", stockPointId);
                    List<SupplyVO> supplyVOList = mpsFeign.getSupplyListByParams(scenario,supplyQueryMap);
                    supplyMap = supplyVOList.stream().collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));

                    for (InventoryBatchDetailVO inventoryBatchDetailVO : inventoryBatchDetails) {
                        String subInventoryKey = inventoryBatchDetailVO.getStockPointCode() + "_" + inventoryBatchDetailVO.getFreightSpace();
                        String supplyKey = inventoryBatchDetailVO.getBarCode() + "_" + inventoryBatchDetailVO.getProductCode() + "_" + inventoryBatchDetailVO.getSubinventory();
                        SupplyDTO supplyDTO = new SupplyDTO();

                        if (subInventoryMap.containsKey(subInventoryKey)) {
                            log.info("找到有效子库存货位subInventoryKey：{}", subInventoryKey);
                            if(supplyMap.containsKey(supplyKey)){
                                log.info("找到supplyKey：{}", supplyKey);
                                SupplyVO supplyVO = supplyMap.get(supplyKey);
                                BeanUtils.copyProperties(supplyVO, supplyDTO);
                                supplyDTO.setEnabled(YesOrNoEnum.YES.getCode());
                                supplyDTO.setProductId(inventoryBatchDetailVO.getProductId());
                                supplyDTO.setStockPointId(inventoryBatchDetailVO.getOriginalOrgId());
                                supplyDTO.setSupplyCode(supplyKey);
                                supplyDTO.setSupplyType(INVENTORY_SUPPLY);
                                supplyDTO.setSupplyTime(DateUtils.stringToDate(inventoryBatchDetailVO.getAssignedTime()));
                                supplyDTO.setFulfillmentStatus(UNFULFILL);
                                //quantity的量增大，即接口过来的量大于原有的量  未分配的数量等于接口过来的量-原来的未分配的数量
                                if (supplyDTO.getQuantity().compareTo(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity())) < 0) {
                                    log.info("接口量:{},大于原有量:{}", inventoryBatchDetailVO.getCurrentQuantity(), supplyDTO.getQuantity());
                                    supplyDTO.setUnfulfilledQuantity(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity())
                                            .subtract(supplyDTO.getQuantity()).add(supplyDTO.getUnfulfilledQuantity()));
                                    supplyDTO.setQuantity(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
                                }
                                //quantity的量减少，即接口过来的量小于原有的量
                                else {
                                    log.info("接口量:{},小于原有量:{}", inventoryBatchDetailVO.getCurrentQuantity(), supplyDTO.getQuantity());

                                    //如果接口减少的量小于原未分配的数量，则将quantity设置为接口的量,未分配的量为原未分配的量-差异的数量（原来的量(quantity）-接口的量）

                                    if (supplyDTO.getQuantity().subtract(new BigDecimal(inventoryBatchDetailVO
                                            .getCurrentQuantity())).compareTo(supplyDTO.getUnfulfilledQuantity()) < 0) {
                                        log.info("接口减少量小于原未分配数量");
                                        supplyDTO.setUnfulfilledQuantity(supplyDTO.getUnfulfilledQuantity()
                                                .add(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity())
                                                        .subtract(supplyDTO.getQuantity())));
                                        supplyDTO.setQuantity(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
                                    }
                                    //如果接口减少的量大于未分配的数量，则将quantity设置为接口的量，未分配的数量设为0，并要分情况进行处理：
                                    //1：减少的量大于已分配的数量，则将quantity设置为接口的量,删除对应分配行，还原需求行
                                    //2：减少的量小于已分配的数量，则将quantity设置为接口的量,未分配的数量设为0，
                                    //3：减少的量等于已分配的数量，则将quantity设置为接口的量,未分配的数量设为0，删除分配行，还原需求表的未分配数量
                                    else {
                                        log.info("接口减少量大于原未分配数量");

                                        //已分配数量
                                        BigDecimal fulfilledQuantity = supplyDTO.getQuantity().subtract(supplyDTO.getUnfulfilledQuantity());
                                        //差额数量
                                        BigDecimal remainQuantity = fulfilledQuantity.subtract(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
                                        //如果接口的量小于未分配的数量
                                        Map<String, Object> fulfillQueryMap = new HashMap<>();
                                        fulfillQueryMap.put("supplyId", supplyDTO.getId());
                                        fulfillQueryMap.put("enabled", YesOrNoEnum.YES.getCode());
                                        List<FulfillmentVO> fulfillmentVOList = mpsFeign.getFulfillmentListByParams(scenario,fulfillQueryMap);
                                        List<String> demandsIds = fulfillmentVOList.stream().map(FulfillmentBasicVO::getDemandId).collect(Collectors.toList());
                                        if (CollectionUtils.isNotEmpty(demandsIds)) {
                                            Map<String, Object> demandQueryMap = new HashMap<>();
                                            demandQueryMap.put("ids", demandsIds);
                                            demandQueryMap.put("enabled", YesOrNoEnum.YES.getCode());
                                            List<DemandVO> demandVOS = mpsFeign.getDemandListByParams(scenario,demandQueryMap);
                                            if (CollectionUtils.isNotEmpty(demandVOS)) {
                                                List<FulfillmentDTO> fulfillmentDTOS = new ArrayList<>();
                                                List<DemandDTO> demandDTOS = new ArrayList<>();
                                                List<DemandVO> demandVOList = demandVOS.stream().sorted(Comparator.comparing(DemandVO::getDemandTime).reversed()).collect(Collectors.toList());
                                                for (DemandVO t : demandVOList) {

                                                    FulfillmentDTO fulfillmentDTO = new FulfillmentDTO();
                                                    FulfillmentVO fulfillmentVO = fulfillmentVOList.stream().filter(w -> Objects.equals(t.getId(), w.getDemandId())).findFirst().orElse(null);
                                                    DemandDTO demandDTO = new DemandDTO();
                                                    BeanUtils.copyProperties(t, demandDTO);
                                                    if (Objects.nonNull(fulfillmentVO)) {
                                                        BeanUtils.copyProperties(fulfillmentVO, fulfillmentDTO);
                                                        //已分配数量等于差异数量  直接删除分配表的行，并还原需求表的未分配数量
                                                        if (fulfillmentVO.getFulfillmentQuantity().compareTo(remainQuantity) == 0) {
                                                            log.info("已分配数量等于减少的数量");
                                                            fulfillmentDTO.setFulfillmentQuantity(BigDecimal.ZERO);
                                                            fulfillmentDTO.setEnabled(YesOrNoEnum.NO.getCode());
                                                            fulfillmentDTOS.add(fulfillmentDTO);

                                                            demandDTO.setUnfulfilledQuantity(t.getUnfulfilledQuantity().add(remainQuantity));
                                                            demandDTOS.add(demandDTO);
                                                            break;
                                                        }
                                                        //已分配的数量大于差异数量  分配表的分配数量减去差异数量，并还原需求表的未分配数量
                                                        else if (fulfillmentVO.getFulfillmentQuantity().compareTo(remainQuantity) > 0) {
                                                            log.info("已分配数量大于减少的数量");

                                                            fulfillmentDTO.setFulfillmentQuantity(fulfillmentVO.getFulfillmentQuantity().subtract(remainQuantity));
                                                            fulfillmentDTOS.add(fulfillmentDTO);
                                                            demandDTO.setUnfulfilledQuantity(t.getUnfulfilledQuantity().add(remainQuantity));
                                                            demandDTOS.add(demandDTO);
                                                            break;
                                                        }
                                                        //已分配的数量小于差异数量  需要把分配表的改行删除并继续寻找下一行已分配的数量，并还原需求表的未分配数量
                                                        else {
                                                            log.info("已分配数量小于减少的数量");
                                                            fulfillmentDTO.setEnabled(YesOrNoEnum.NO.getCode());
                                                            fulfillmentDTO.setFulfillmentQuantity(BigDecimal.ZERO);
                                                            fulfillmentDTOS.add(fulfillmentDTO);
                                                            demandDTO.setUnfulfilledQuantity(t.getUnfulfilledQuantity().add(remainQuantity));
                                                            remainQuantity = remainQuantity.subtract(fulfillmentVO.getFulfillmentQuantity());
                                                            demandDTOS.add(demandDTO);
                                                        }
                                                    }
                                                }
                                                if (CollectionUtils.isNotEmpty(fulfillmentDTOS)) {
                                                    try {
                                                        mpsFeign.updateFulfillment(scenario,fulfillmentDTOS);
                                                    } catch (Exception e) {
                                                        log.error("更新分配行失败：{}", e.getMessage());
                                                        throw new BusinessException("更新分配行失败：" + e.getMessage());
                                                    }
                                                    log.info("更新分配行，数量:{}", fulfillmentDTOS.size());
                                                }
                                                if (CollectionUtils.isNotEmpty(demandDTOS)) {
                                                    try {
                                                        mpsFeign.updateDemand(scenario,demandDTOS);
                                                    } catch (Exception e) {
                                                        log.error("更新需求行失败：{}", e.getMessage());
                                                        throw new BusinessException("更新需求行失败：" + e.getMessage());
                                                    }
                                                    log.info("更新需求行，数量:{}", demandDTOS.size());
                                                }

                                            }
                                        }
                                        supplyDTO.setUnfulfilledQuantity(BigDecimal.ZERO);
                                        supplyDTO.setQuantity(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
                                    }
                                }
                                updateSupplyList.add(supplyDTO);
                            }else{
                                log.info("未找到supplyKey:{}", supplyKey);
                                supplyDTO.setUnfulfilledQuantity(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
                                supplyDTO.setQuantity(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
                                supplyDTO.setEnabled(YesOrNoEnum.YES.getCode());
                                supplyDTO.setProductId(inventoryBatchDetailVO.getProductId());
                                supplyDTO.setStockPointId(inventoryBatchDetailVO.getOriginalOrgId());
                                supplyDTO.setSupplyCode(inventoryBatchDetailVO.getCustomerNum());
                                supplyDTO.setId(supplyKey);
                                supplyDTO.setSupplyType(INVENTORY_SUPPLY);
                                supplyDTO.setSupplyTime(DateUtils.stringToDate(inventoryBatchDetailVO.getAssignedTime()));
                                supplyDTO.setFulfillmentStatus(UNFULFILL);
                                insertSupplyList.add(supplyDTO);
                            }
                        } else {
                            log.info("未找到有效子库存货位subInventoryKey:{}", subInventoryKey);
                        }
                    }

                }
            }
            if (CollectionUtils.isNotEmpty(insertSupplyList)) {
                List<List<SupplyDTO>> partition = com.google.common.collect.Lists.partition(insertSupplyList, 3000);
                for (List<SupplyDTO> supplyDTOS : partition) {
                    try {
                        mpsFeign.addSupply(scenario,supplyDTOS);
                    } catch (Exception e) {
                        log.error("新增供应数据失败：{}", e.getMessage());
                        throw new BusinessException("新增供应数据失败：" + e.getMessage());

                    }
                }
                log.info("新增供应数据条数：{}", insertSupplyList.size());
            }
            if (CollectionUtils.isNotEmpty(updateSupplyList)) {
                List<List<SupplyDTO>> partition = com.google.common.collect.Lists.partition(updateSupplyList, 3000);
                for (List<SupplyDTO> supplyDTOS : partition) {
                    try {
                        mpsFeign.updateSupply(scenario,supplyDTOS);
                    } catch (Exception e) {
                        log.error("更新供应数据失败：{}", e.getMessage());
                        throw new BusinessException("更新供应数据失败：" + e.getMessage());
                    }
                }
                log.info("更新供应数据条数：{}", updateSupplyList.size());
            }
        } else {
            log.info("本厂库存点为空，无需获取供应分配关系");
        }
    }

	@Override
	public List<InventoryBatchDetailVO> selectStatisticsInventory(List<String> productCodes,
			List<String> stockPointCodeList) {
		return inventoryBatchDetailDao.selectStatisticsInventory(productCodes, stockPointCodeList);
	}
}
