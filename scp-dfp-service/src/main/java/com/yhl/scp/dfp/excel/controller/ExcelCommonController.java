package com.yhl.scp.dfp.excel.controller;

import com.google.common.collect.Lists;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.ImportTypeEnum;
import com.yhl.scp.common.excel.enums.ImportObjectEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.service.ExcelService;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.log.dto.ExcelImportLogDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <code>ExcelCommonController</code>
 * <p>
 * 通用Excel控制器
 * </p>
 *
 * @version 1.0
 * @since 2023-11-06 13:56:30
 */
@Api(tags = "通用Excel控制器")
@RestController
@RequestMapping("excelCommon")
@Slf4j
public class ExcelCommonController extends BaseController {

    private static final String EXCEL_SERVICE_SUFFIX = "ExcelService";
    @Resource
    private Map<String, ExcelService> excelService;
    @Resource
    private IpsFeign ipsFeign;
    @Value("${spring.application.name}")
    private String applicationName;

    @ApiOperation(value = "导出模板")
    @GetMapping(value = "exportTemplate")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "objectType", value = "导入对象", dataTypeClass = String.class)
    })
    public void exportTemplate(@RequestParam(value = "objectType") String objectType) {
        ImportObjectEnum importObjectEnum = ImportObjectEnum.getByCode(objectType);
        ExcelService excelServiceImpl = excelService.get(importObjectEnum.getCode() + EXCEL_SERVICE_SUFFIX);
        excelServiceImpl.exportTemplate(response, objectType);
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "import")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "objectType", value = "导入对象", dataTypeClass = String.class),
            @ApiImplicitParam(name = "importType", allowableValues = "FULL_IMPORT,INCREMENTAL_IMPORT",
                    value = "导入方式", dataTypeClass = String.class),
    })
    public BaseResponse importData(@RequestPart MultipartFile file, @RequestParam(value = "objectType") String objectType,
                                   @RequestParam(value = "importType") String importType,
                                   @RequestParam(value = "yearMonth", required = false) String yearMonth,
                                   @RequestParam(value = "oemCode", required = false) String oemCode,
                                   @RequestParam(value = "deliveryDockingNumber", required = false) String deliveryDockingNumber,
                                   Map<String, Object> extraMap,
                                   @RequestHeader(value = "tenant") String tenant,
                                   @RequestHeader(value = "module") String module,
                                   @RequestHeader(value = "scenario") String scenario) {
        try {
            // TODO: extraMap参数处理，@RequestBody不支持form-data
            ImportTypeEnum importTypeEnum = ImportTypeEnum.valueOf(importType);
            ImportObjectEnum strategyEnum = ImportObjectEnum.getByCode(objectType);
            String moduleCode = strategyEnum.getModuleCode();
            if (!applicationName.toUpperCase().contains(moduleCode.toUpperCase())) {
                throw new BusinessException("导入接口不支持当前业务对象");
            }
            ExcelService excelServiceImpl = excelService.get(strategyEnum.getCode() + EXCEL_SERVICE_SUFFIX);
            String userId = SystemHolder.getUserId();
            extraMap.put("yearMonth", yearMonth);
            extraMap.put("oemCode", oemCode);
            extraMap.put("deliveryDockingNumber", deliveryDockingNumber);
            ImportContext importContext = ImportContext.builder().importType(importTypeEnum)
                    .importExcelStrategy(strategyEnum).userId(userId).extraMap(extraMap).build();
            ImportAnalysisResultHolder<? extends BaseDTO, ? extends BasePO> importAnalysisResultHolder
                    = excelServiceImpl.doImport(file, importContext);
            List<DataImportInfo> importLogList = importAnalysisResultHolder.getImportLogList();
            // 记录导入日志
            return doResolveLog(importLogList, importType, tenant, module, scenario, strategyEnum.getDesc());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导入失败", e);
            throw new BusinessException("导入失败,{0}", e);
        }
    }

    /**
     * 日志数据处理
     *
     * @param importLogList 导入日志数据列表
     * @param importType    导入方式
     * @param tenant        租户
     * @param module        模块
     * @param scenario      场景
     * @param objectDesc    导入对象描述
     * @return com.yhl.platform.common.entity.BaseResponse
     */
    private BaseResponse doResolveLog(List<DataImportInfo> importLogList, String importType,
                                      String tenant, String module, String scenario, String objectDesc) {
        List<ExcelImportLogDTO> logList = importLogList.stream().map(it -> {
            ExcelImportLogDTO excelImportLog = new ExcelImportLogDTO();
            BeanUtils.copyProperties(it, excelImportLog);
            excelImportLog.setImportType(importType);
            excelImportLog.setTenantId(tenant);
            excelImportLog.setModuleCode(module);
            excelImportLog.setScenarioId(scenario);
            excelImportLog.setObjectType(objectDesc);
            return excelImportLog;
        }).collect(Collectors.toList());
        // 日志落库
        Optional<ExcelImportLogDTO> first = logList.stream()
                .filter(k -> ImportDataTypeEnum.GATHER.getCode().equals(k.getInfoType())).findFirst();
        ExcelImportLogDTO gatherLog = first.orElse(null);
        if (gatherLog == null) {
            throw new BusinessException("导入错误，导入日志有误");
        }
        // 拿到总条数
        String totalCount = gatherLog.getRemark();
        long errorCount = logList.stream().filter(k -> ImportDataTypeEnum.ERROR.getCode().equals(k.getInfoType())).map(ExcelImportLogDTO::getDisplayIndex).distinct().count();
        long warningCount = logList.stream().filter(k -> ImportDataTypeEnum.WARNING.getCode().equals(k.getInfoType())).map(ExcelImportLogDTO::getDisplayIndex).distinct().count();
        String statistics = "数据导入完成！共解析 " + totalCount + " 条; 成功 " + Math.max(0, (Integer.parseInt(totalCount) - errorCount))
                + " 条; 错误 " + errorCount + " 条; 警告 " + warningCount + " 条; 详情请查看导入日志";
        gatherLog.setErrorDetail(statistics);
        gatherLog.setObjectType(objectDesc);
        gatherLog.setId(UUIDUtil.getUUID());
        ipsFeign.createExcelImportLog(Lists.newArrayList(gatherLog));
        logList.removeIf(k -> ImportDataTypeEnum.GATHER.getCode().equals(k.getInfoType()));
        if (CollectionUtils.isNotEmpty(logList)) {
            logList.forEach(excelImportLogDTO -> {
                excelImportLogDTO.setParentId(gatherLog.getId());
                excelImportLogDTO.setId(UUIDUtil.getUUID());
            });
            ipsFeign.createExcelImportLog(logList);
        }
        if (errorCount > 0) {
            return BaseResponse.error(logList, statistics);
        }
        return BaseResponse.success(statistics, logList);
    }

}